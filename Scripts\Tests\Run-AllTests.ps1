<#
.SYNOPSIS
    Runs all tests for the AutoCertLE module.

.DESCRIPTION
    This script runs all tests for the AutoCertLE module, including unit tests, integration tests, and load tests.

.PARAMETER Domain
    The domain to use for testing. You must own this domain and be able to modify its DNS records.

.PARAMETER Email
    The email address to use for Let's Encrypt registration.

.PARAMETER DnsPlugin
    The DNS plugin to use for testing. Default is 'Manual'.

.PARAMETER SkipUnitTests
    If specified, unit tests will be skipped.

.PARAMETER SkipIntegrationTests
    If specified, integration tests will be skipped.

.PARAMETER SkipEndToEndTests
    If specified, end-to-end tests will be skipped.

.PARAMETER SkipLoadTests
    If specified, load tests will be skipped.

.PARAMETER SetupTestEnvironment
    If specified, a test environment will be set up with mock Salient CompleteView services.

.PARAMETER CleanupTestEnvironment
    If specified, the test environment will be cleaned up after testing.

.EXAMPLE
    .\Run-AllTests.ps1 -Domain "test.example.com" -Email "<EMAIL>" -DnsPlugin "Cloudflare"
#>
[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$Domain,
    
    [Parameter(Mandatory = $true)]
    [string]$Email,
    
    [Parameter()]
    [string]$DnsPlugin = 'Manual',
    
    [Parameter()]
    [switch]$SkipUnitTests,
    
    [Parameter()]
    [switch]$SkipIntegrationTests,
    
    [Parameter()]
    [switch]$SkipEndToEndTests,
    
    [Parameter()]
    [switch]$SkipLoadTests,
    
    [Parameter()]
    [switch]$SetupTestEnvironment,
    
    [Parameter()]
    [switch]$CleanupTestEnvironment
)

# Start logging
$logFile = Join-Path -Path $PSScriptRoot -ChildPath "AllTests_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
Start-Transcript -Path $logFile -Append

try {
    Write-Host "=== AutoCertLE Test Suite ===" -ForegroundColor Cyan
    Write-Host "Domain: $Domain" -ForegroundColor Cyan
    Write-Host "Email: $Email" -ForegroundColor Cyan
    Write-Host "DNS Plugin: $DnsPlugin" -ForegroundColor Cyan
    Write-Host "Skip Unit Tests: $SkipUnitTests" -ForegroundColor Cyan
    Write-Host "Skip Integration Tests: $SkipIntegrationTests" -ForegroundColor Cyan
    Write-Host "Skip End-to-End Tests: $SkipEndToEndTests" -ForegroundColor Cyan
    Write-Host "Skip Load Tests: $SkipLoadTests" -ForegroundColor Cyan
    Write-Host "Setup Test Environment: $SetupTestEnvironment" -ForegroundColor Cyan
    Write-Host "Cleanup Test Environment: $CleanupTestEnvironment" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    
    # Setup test environment if requested
    if ($SetupTestEnvironment) {
        Write-Host "`n=== Setting up test environment ===" -ForegroundColor Cyan
        & "$PSScriptRoot\Setup-TestEnvironment.ps1" -CreateManagementServer -CreateRecordingServer -CreateCertificateFolders
    }
    
    # Run unit tests
    if (-not $SkipUnitTests) {
        Write-Host "`n=== Running unit tests ===" -ForegroundColor Cyan
        & "$PSScriptRoot\Run-Tests.ps1"
    } else {
        Write-Host "`n=== Skipping unit tests ===" -ForegroundColor Yellow
    }
    
    # Run integration tests
    if (-not $SkipIntegrationTests) {
        Write-Host "`n=== Running integration tests ===" -ForegroundColor Cyan
        & "$PSScriptRoot\Test-Integration.ps1" -Domain $Domain -DnsPlugin $DnsPlugin
    } else {
        Write-Host "`n=== Skipping integration tests ===" -ForegroundColor Yellow
    }
    
    # Run end-to-end tests
    if (-not $SkipEndToEndTests) {
        Write-Host "`n=== Running end-to-end tests ===" -ForegroundColor Cyan
        & "$PSScriptRoot\Test-EndToEnd.ps1" -Domain $Domain -Email $Email -DnsPlugin $DnsPlugin
    } else {
        Write-Host "`n=== Skipping end-to-end tests ===" -ForegroundColor Yellow
    }
    
    # Run load tests
    if (-not $SkipLoadTests) {
        Write-Host "`n=== Running load tests ===" -ForegroundColor Cyan
        & "$PSScriptRoot\Test-Load.ps1" -BaseDomain $Domain -Count 5 -OperationType "All"
    } else {
        Write-Host "`n=== Skipping load tests ===" -ForegroundColor Yellow
    }
    
    # Cleanup test environment if requested
    if ($CleanupTestEnvironment) {
        Write-Host "`n=== Cleaning up test environment ===" -ForegroundColor Cyan
        & "$PSScriptRoot\Setup-TestEnvironment.ps1" -Cleanup
    }
    
    Write-Host "`n=== All tests completed! ===" -ForegroundColor Green
} catch {
    Write-Host "Error during testing: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
} finally {
    Stop-Transcript
    Write-Host "`nTest log saved to: $logFile" -ForegroundColor Cyan
}
