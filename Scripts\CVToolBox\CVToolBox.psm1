# CVToolBox.psm1
# ===========================================
# A PowerShell module to support CompleteView (CV) software deployment, updates,
# and various Windows tasks, including robust WinRM configuration.
# This is a starter framework that will be expanded over time.

# ---------------------------
# Export our public functions
# ---------------------------
Export-ModuleMember -Function Enable-WinRM, Invoke-RemoteTask, Install-CV, Uninstall-CV, Update-CV

#region Enable-WinRM
function Enable-WinRM {
    <#
    .SYNOPSIS
        Ensures WinRM is enabled and accessible on a remote machine using fallback methods.

    .DESCRIPTION
        Tries multiple approaches to enable WinRM, including:
          1) WMI call to "Enable-PSRemoting -Force"
          2) Manually starting the WinRM service, setting it to Automatic, and opening firewall rules
          3) (Optional) Using a scheduled task if the other methods fail (when -UseScheduledTaskFallback is specified)

        Returns $true if WinRM is confirmed working, $false otherwise.

    .PARAMETER ComputerName
        The target machine to configure for WinRM.

    .PARAMETER Credential
        An admin credential with rights on the target.

    .PARAMETER UseScheduledTaskFallback
        If set, try a scheduled task method as a final fallback.

    .EXAMPLE
        Ensure-WinRM -ComputerName "Server01" -Credential $Cred
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$ComputerName,

        [Parameter(Mandatory=$true)]
        [PSCredential]$Credential,

        [Parameter(Mandatory=$false)]
        [switch]$UseScheduledTaskFallback
    )

    Write-Host "`n=== [CVToolBox] Ensuring WinRM on $ComputerName ===" -ForegroundColor Cyan

    # Quick check
    if (Test-WSMan -ComputerName $ComputerName -Credential $Credential -ErrorAction SilentlyContinue) {
        Write-Host "[WinRM] $ComputerName - Already active. No action needed." -ForegroundColor Green
        return $true
    }
    else {
        Write-Host "[WinRM] $ComputerName - Not available, attempting fallback..." -ForegroundColor Yellow
    }

    $errorList = New-Object System.Collections.Generic.List[string]

    # Method #1: WMI -> Enable-PSRemoting
    Write-Host "  => Method #1: Using WMI to run 'Enable-PSRemoting -Force'" -ForegroundColor White
    try {
        $r = Invoke-WmiMethod -Class Win32_Process -Name Create -ComputerName $ComputerName -Credential $Credential `
            -ArgumentList "powershell.exe -Command `"Enable-PSRemoting -Force`""
        if ($r.ReturnValue -eq 0) {
            Start-Sleep -Seconds 5
            if (Test-WSMan -ComputerName $ComputerName -Credential $Credential -ErrorAction SilentlyContinue) {
                Write-Host "     - WinRM now responding on $ComputerName (Method #1)" -ForegroundColor Green
                return $true
            }
            else {
                $msg = "     - Method #1 completed but WinRM is still not responding."
                Write-Host $msg -ForegroundColor Yellow
                $errorList.Add($msg) | Out-Null
            }
        } else {
            $msg = "     - Method #1 failed: Return code $($r.ReturnValue)."
            Write-Host $msg -ForegroundColor Red
            $errorList.Add($msg) | Out-Null
        }
    }
    catch {
        $msg = "     - Method #1 error: $($_.Exception.Message)"
        Write-Host $msg -ForegroundColor Red
        $errorList.Add($msg) | Out-Null
    }

    # Method #2: Manually start WinRM, set Automatic, open firewall, re-run remoting
    Write-Host "  => Method #2: Start WinRM service, set Auto, open firewall, re-run Enable-PSRemoting" -ForegroundColor White
    try {
        $serviceResult = Invoke-WmiMethod -Class Win32_Service -Name StartService -ComputerName $ComputerName -Credential $Credential `
                                          -ArgumentList "WinRM"
        if ($serviceResult.ReturnValue -ne 0) {
            $msg = "     - Could not start WinRM service (code: $($serviceResult.ReturnValue))"
            Write-Host $msg -ForegroundColor Yellow
            $errorList.Add($msg) | Out-Null
        }

        $winrmService = Get-WmiObject -Class Win32_Service -ComputerName $ComputerName -Credential $Credential -Filter "Name='WinRM'"
        if ($winrmService) {
            $winrmService.ChangeStartMode("Automatic") | Out-Null
        }

        $fwCmd = "netsh advfirewall firewall set rule group=`"Windows Remote Management`" new enable=yes"
        $fwProc = Invoke-WmiMethod -Class Win32_Process -Name Create -ComputerName $ComputerName -Credential $Credential `
                                   -ArgumentList "cmd.exe /c $fwCmd"
        if ($fwProc.ReturnValue -ne 0) {
            $msg = "     - Could not enable firewall rule group (code: $($fwProc.ReturnValue))."
            Write-Host $msg -ForegroundColor Yellow
            $errorList.Add($msg) | Out-Null
        }

        # Another attempt
        $rerun = Invoke-WmiMethod -Class Win32_Process -Name Create -ComputerName $ComputerName -Credential $Credential `
                -ArgumentList "powershell.exe -Command `"Enable-PSRemoting -Force`""
        if ($rerun.ReturnValue -ne 0) {
            $msg = "     - Another attempt to Enable-PSRemoting failed (code: $($rerun.ReturnValue))."
            Write-Host $msg -ForegroundColor Yellow
            $errorList.Add($msg) | Out-Null
        }

        Start-Sleep -Seconds 5
        if (Test-WSMan -ComputerName $ComputerName -Credential $Credential -ErrorAction SilentlyContinue) {
            Write-Host "     - WinRM now responding on $ComputerName (Method #2)" -ForegroundColor Green
            return $true
        }
        else {
            $msg = "     - Method #2 completed but WinRM still not responding."
            Write-Host $msg -ForegroundColor Red
            $errorList.Add($msg) | Out-Null
        }
    }
    catch {
        $msg = "     - Method #2 error: $($_.Exception.Message)"
        Write-Host $msg -ForegroundColor Red
        $errorList.Add($msg) | Out-Null
    }

    # Method #3: Scheduled task fallback
    if ($UseScheduledTaskFallback) {
        Write-Host "  => Method #3: Creating scheduled task to run 'Enable-PSRemoting -Force'" -ForegroundColor White
        try {
            $taskName = "ForceEnableWinRM_" + (Get-Random)
            $taskCmd = "Enable-PSRemoting -Force"
            $timeNow = (Get-Date).AddMinutes(1).ToString("HH:mm")
            $createCmd = "schtasks /Create /RU `"NT AUTHORITY\SYSTEM`" /SC ONCE /ST $timeNow /TN $taskName /TR `"`"powershell.exe`" -Command $taskCmd`" /F"

            $create = Invoke-WmiMethod -Class Win32_Process -Name Create -ComputerName $ComputerName -Credential $Credential `
                      -ArgumentList "cmd.exe /c $createCmd"
            if ($create.ReturnValue -eq 0) {
                Write-Host "     - Scheduled task $taskName created. Waiting 90s to let it run..." -ForegroundColor Cyan
                Start-Sleep -Seconds 90
                if (Test-WSMan -ComputerName $ComputerName -Credential $Credential -ErrorAction SilentlyContinue) {
                    Write-Host "     - WinRM responding on $ComputerName (via method #3)" -ForegroundColor Green
                    return $true
                }
                else {
                    $msg = "     - Method #3 completed but WinRM still not responding."
                    Write-Host $msg -ForegroundColor Red
                    $errorList.Add($msg) | Out-Null
                }
            }
            else {
                $msg = "     - Method #3 failed to create scheduled task (code: $($create.ReturnValue))"
                Write-Host $msg -ForegroundColor Red
                $errorList.Add($msg) | Out-Null
            }
        }
        catch {
            $msg = "     - Method #3 error: $($_.Exception.Message)"
            Write-Host $msg -ForegroundColor Red
            $errorList.Add($msg) | Out-Null
        }
    }

    # If we get here, all attempts failed
    Write-Host "[WinRM] $ComputerName - Failed after multiple attempts!" -ForegroundColor Red
    Write-Host "     - Errors encountered:" -ForegroundColor Red
    $errorList | ForEach-Object { Write-Host "       * $_" -ForegroundColor Red }
    return $false
}
#endregion Ensure-WinRM

#region Invoke-RemoteTask
function Invoke-RemoteTask {
    <#
    .SYNOPSIS
        Executes a scriptblock on remote machines, optionally ensuring WinRM first.

    .DESCRIPTION
        This generic function runs a scriptblock on one or more remote computers. If -EnsureWinRM
        is provided, it calls Ensure-WinRM before attempting the remote call. The results of each
        attempt are returned as objects, including success/failure info.

    .PARAMETER ComputerName
        Single or multiple target names/IP addresses.

    .PARAMETER Credential
        Admin credential for remote access.

    .PARAMETER ScriptBlock
        The code you want to run remotely.

    .PARAMETER EnsureWinRM
        If set, the function calls Ensure-WinRM first.

    .PARAMETER UseScheduledTaskFallback
        If set, passes that to Ensure-WinRM so it tries the scheduled task method if needed.

    .EXAMPLE
        Invoke-RemoteTask -ComputerName "Server01","Server02" -Credential $Cred -ScriptBlock {
            Get-Service
        } -EnsureWinRM
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$ComputerName,

        [Parameter(Mandatory=$true)]
        [PSCredential]$Credential,

        [Parameter(Mandatory=$true)]
        [ScriptBlock]$ScriptBlock,

        [switch]$EnsureWinRM,

        [switch]$UseScheduledTaskFallback
    )

    $results = New-Object System.Collections.Generic.List[PSObject]

    foreach ($comp in $ComputerName) {
        Write-Host "`n--- [Invoke-RemoteTask] $comp ---" -ForegroundColor Cyan

        if ($EnsureWinRM) {
            Write-Host "   Checking/Enabling WinRM for $comp..." -ForegroundColor White
            $wm = Ensure-WinRM -ComputerName $comp -Credential $Credential -UseScheduledTaskFallback:$UseScheduledTaskFallback
            if (-not $wm) {
                Write-Host "   Skipping task because WinRM could not be enabled on $comp." -ForegroundColor Red
                $results.Add([pscustomobject]@{
                    Computer   = $comp
                    Succeeded  = $false
                    Error      = "WinRM not enabled"
                    Output     = $null
                }) | Out-Null
                continue
            }
        }

        try {
            Write-Host "   Executing scriptblock on $comp..." -ForegroundColor Yellow
            $remoteResult = Invoke-Command -ComputerName $comp -Credential $Credential -ScriptBlock $ScriptBlock -ErrorAction Stop
            $results.Add([pscustomobject]@{
                Computer   = $comp
                Succeeded  = $true
                Error      = $null
                Output     = $remoteResult
            }) | Out-Null
            Write-Host "   Success on $comp" -ForegroundColor Green
        }
        catch {
            Write-Host "   Failed on ${comp}: $($_.Exception.Message)" -ForegroundColor Red
            $results.Add([pscustomobject]@{
                Computer   = $comp
                Succeeded  = $false
                Error      = $_.Exception.Message
                Output     = $null
            }) | Out-Null
        }
    }

    return $results
}
#endregion Invoke-RemoteTask

#region Install-CV
function Install-CV {
    <#
    .SYNOPSIS
        Installs CompleteView software on remote machines (placeholder).

    .DESCRIPTION
        Placeholder for automating CompleteView installation.
        We might copy an installer to the remote machine, 
        pull updated versions directly online, run MSI or EXE with silent switches, etc.
        This function uses Invoke-RemoteTask to do the actual remote call.

    .PARAMETER ComputerName
        Remote machine(s) on which to install CV.

    .PARAMETER Credential
        Credential with rights on the remote machine(s).

    .PARAMETER InstallerPath
        Path to the CompleteView installer (e.g. an MSI or EXE). Must be accessible to the remote system
        via UNC share or local path.

    .EXAMPLE
        Install-CV -ComputerName "Server01" -Credential $Cred -InstallerPath "\\Share\CompleteView\Setup.exe"
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$ComputerName,

        [Parameter(Mandatory=$true)]
        [PSCredential]$Credential,

        [Parameter(Mandatory=$true)]
        [string]$InstallerPath
    )

    Write-Host "[Install-CV] Installing CompleteView on: $($ComputerName -join ', ')" -ForegroundColor Cyan

    # The actual remote code. In real life, we'd:
    #  - Possibly Copy-Item from a share to a local temp folder
    #  - Run Start-Process with silent install flags
    #  - Check exit codes
    $scriptBlock = {
        param($CVInstaller)
        Write-Output "Installing CV from $CVInstaller..."
        # Example: Start-Process -FilePath msiexec.exe -ArgumentList "/i `"$CVInstaller`" /qn /norestart" -Wait
        return "Install logic (placeholder) executed for $CVInstaller"
    }

    $result = Invoke-RemoteTask -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $InstallerPath -EnsureWinRM -UseScheduledTaskFallback
    return $result
}
#endregion Install-CV

#region Uninstall-CV
function Uninstall-CV {
    <#
    .SYNOPSIS
        Uninstalls CompleteView software on remote machines (placeholder).

    .DESCRIPTION
        A placeholder to show how we might remove existing CompleteView installations.
        Searching the registry for an uninstall string, or using msiexec /x <ProductCode>.

    .PARAMETER ComputerName
        Remote machine(s).

    .PARAMETER Credential
        Admin credential.

    .EXAMPLE
        Uninstall-CV -ComputerName "Server01","Server02" -Credential $Cred
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$ComputerName,

        [Parameter(Mandatory=$true)]
        [PSCredential]$Credential
    )

    Write-Host "[Uninstall-CV] Uninstalling CompleteView on: $($ComputerName -join ', ')" -ForegroundColor Cyan

    $scriptBlock = {
        Write-Output "Uninstall logic placeholder for CV..."
        # Possibly parse registry for "CompleteView" display name or product code, run msiexec /x
        return "CV Uninstall placeholder on remote machine"
    }

    $result = Invoke-RemoteTask -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -EnsureWinRM -UseScheduledTaskFallback
    return $result
}
#endregion Uninstall-CV

#region Update-CV
function Update-CV {
    <#
    .SYNOPSIS
        Updates or patches CompleteView on remote machines.

    .DESCRIPTION
        Placeholder function – could handle upgrades, patches, or roll-ups.
        Possibly the same logic as Install-CV, but with a different MSI or patch file.

    .PARAMETER ComputerName
        Remote machines to update.

    .PARAMETER Credential
        Admin credential.

    .PARAMETER PatchPath
        Path to the patch or new version to be deployed.

    .EXAMPLE
        Update-CV -ComputerName Server01 -Credential $Cred -PatchPath "\\Share\CVUpdates\Patch123.msi"
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$ComputerName,

        [Parameter(Mandatory=$true)]
        [PSCredential]$Credential,

        [Parameter(Mandatory=$true)]
        [string]$PatchPath
    )

    Write-Host "[Update-CV] Updating CompleteView on: $($ComputerName -join ', ')" -ForegroundColor Cyan

    $scriptBlock = {
        param($CVPatch)
        Write-Output "Applying CV patch from $CVPatch..."
        # Example logic: Start-Process msiexec /p <patch> /qn ...
        return "CV patch placeholder logic"
    }

    $result = Invoke-RemoteTask -ComputerName $ComputerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $PatchPath -EnsureWinRM -UseScheduledTaskFallback
    return $result
}
#endregion Update-CV
