<#
.DESCRIPTION
    This script exports all users from a Management Server to a CSV on the desktop.
    The CSV contains the username, userId, userGuid, and the groups they belong to with an option to include user passwords.

.NOTES
    Author:         <EMAIL>
    Creation Date:  01/19/24

#>

# Function to handle error responses
function HandleErrorResponse($responseError) {
    if ($responseError.Exception -is [System.Net.WebException]) {
        Write-Error "Network error occurred: $($responseError.Exception.Message)"
    }
    elseif ($responseError.Exception.Response) {
        $responseStream = $responseError.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($responseStream)
        $responseBody = $reader.ReadToEnd()
        $reader.Close()
        Write-Error "API error occurred: $($responseBody | ConvertFrom-Json).error_description"
    }
    else {
        Write-Error "An unexpected error occurred: $($responseError.Exception.Message)"
    }
    if ($responseStream) {
        $responseStream.Close()
    }
}

# Function to validate credentials
function ValidateCredentials($username, [System.Security.SecureString]$securePassword) {
    if ([string]::IsNullOrWhiteSpace($username) -or $null -eq $securePassword -or $securePassword.Length -eq 0) {
        Write-Host "Username and password cannot be blank."
        return $false
    }
    return $true
}

# Function to convert SecureString to plain text
function ConvertTo-PlainText($secureString) {
    try {
        return [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureString))
    }
    catch {
        Write-Error "Failed to process secure string. Error: $_"
        return $null
    }
}

# Prompt for Management Server user
Write-Host ""
Write-Host "Retrieving MS credentials..." -ForegroundColor Green
Write-Host ""
do {
    $msUser = Read-Host "Enter MS Username"
    if ([string]::IsNullOrWhiteSpace($msUser)) {
        Write-Host "Username cannot be blank. Please enter a valid username."
    }
} while ([string]::IsNullOrWhiteSpace($msUser))

# Prompt for Management Server pass
$msPass = Read-Host "Enter MS Password" -AsSecureString

# Validate credentials
if (-not (ValidateCredentials $msUser $msPass)) {
    exit
}

# Convert the SecureString to plain text
$msPassText = ConvertTo-PlainText $msPass

# Prompt to include passwords in export
$includePasswords = $false
do {
    $includePasswordsResponse = Read-Host "Include user passwords? (y)es or (n)o"
    switch ($includePasswordsResponse.ToLower()) {
        'y' { $includePasswords = $true; break }
        'n' { $includePasswords = $false; break }
        default { Write-Host "Invalid input. Please enter 'y' for yes or 'n' for no." }
    }
} while ($includePasswordsResponse -ne 'y' -and $includePasswordsResponse -ne 'n')

# MS token request params
Write-Host ""
Write-Host "Requesting MS token..." -ForegroundColor Cyan
$body = @{
    grant_type = "password"
    client_id = "client.desktop"
    client_secret = "secret"
    username = $msUser
    password = $msPassText
    scope = "management-server.config"
}

# Access token URL
$tokenUrl = "http://localhost:8095/connect/token"

# Request OAuth token
try {
    $response = Invoke-RestMethod -Method Post -Uri $tokenUrl -Body $body -ContentType "application/x-www-form-urlencoded"
    $token = $response.access_token
    Write-Host "Token obtained successfully" -ForegroundColor Cyan
}
catch {
    HandleErrorResponse $_
    exit
}

# Format API URL for user list
$msApiUrl = "http://localhost:8095/api/v2.0/user?includeAll=true"

# Request user list
Write-Host "Requesting user list..." -ForegroundColor Green
try {
    $userListResponse = Invoke-RestMethod -Method Get -Uri $msApiUrl -Headers @{Authorization = "Bearer $token"}
    Write-Host "User list obtained successfully" -ForegroundColor Green
}
catch {
    HandleErrorResponse $_
    exit
}

# Parse JSON and export to CSV
try {
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $dateTimeStamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    $fileName = "User Export $dateTimeStamp.csv"
    $filePath = Join-Path $desktopPath $fileName

    if ($includePasswords) {
        $userListResponse | Select-Object username, password, @{Name='groups'; Expression={($_.groups | ForEach-Object { $_.name }) -join ', '}}, userId, userGuid | Export-Csv -Path $filePath -NoTypeInformation
    } else {
        $userListResponse | Select-Object username, @{Name='groups'; Expression={($_.groups | ForEach-Object { $_.name }) -join ', '}}, userId, userGuid | Export-Csv -Path $filePath -NoTypeInformation
    }
    
    Write-Host "Data exported to $filePath" -ForegroundColor Green
}
catch {
    Write-Error "Failed to export data. Error: $_"
}

Write-Host ""
Read-Host -Prompt "Press Enter to exit"
