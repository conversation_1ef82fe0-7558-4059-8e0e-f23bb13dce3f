# Function to send email notification
function Send-EmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Subject,
        
        [Parameter(Mandatory = $true)]
        [string]$Body,
        
        [Parameter()]
        [string]$To,
        
        [Parameter()]
        [string]$From,
        
        [Parameter()]
        [string]$SmtpServer,
        
        [Parameter()]
        [int]$Port = 25,
        
        [Parameter()]
        [switch]$UseSSL,
        
        [Parameter()]
        [System.Management.Automation.PSCredential]$Credential
    )
    
    # Get email settings if not provided
    $emailSettings = Get-EmailSettings
    
    if (-not $To) {
        $To = $emailSettings.To
    }
    
    if (-not $From) {
        $From = $emailSettings.From
    }
    
    if (-not $SmtpServer) {
        $SmtpServer = $emailSettings.SmtpServer
    }
    
    if (-not $Port -and $emailSettings.Port) {
        $Port = $emailSettings.Port
    }
    
    if (-not $PSBoundParameters.ContainsKey('UseSSL') -and $emailSettings.UseSSL) {
        $UseSSL = $emailSettings.UseSSL
    }
    
    if (-not $Credential -and $emailSettings.Username -and $emailSettings.Password) {
        $securePassword = ConvertTo-SecureString $emailSettings.Password -AsPlainText -Force
        $Credential = New-Object System.Management.Automation.PSCredential ($emailSettings.Username, $securePassword)
    }
    
    # Validate required parameters
    if (-not $To -or -not $From -or -not $SmtpServer) {
        Write-Warning "Email notification settings are incomplete. Please configure email settings."
        Write-Log "Email notification settings are incomplete. Please configure email settings." -Level 'Warning'
        return $false
    }
    
    try {
        $mailParams = @{
            To = $To
            From = $From
            Subject = $Subject
            Body = $Body
            SmtpServer = $SmtpServer
            Port = $Port
            UseSSL = $UseSSL
            BodyAsHtml = $true
        }
        
        if ($Credential) {
            $mailParams.Credential = $Credential
        }
        
        Send-MailMessage @mailParams
        Write-Verbose "Email notification sent to $To"
        Write-Log "Email notification sent to $To" -Level 'Info'
        return $true
    } catch {
        Write-Error "Failed to send email notification: $($_)"
        Write-Log "Failed to send email notification: $($_)" -Level 'Error'
        return $false
    }
}

# Function to get email settings
function Get-EmailSettings {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\email_settings.json"
    )
    
    if (Test-Path $SettingsPath) {
        try {
            $settings = Get-Content $SettingsPath -Raw | ConvertFrom-Json
            return $settings
        } catch {
            Write-Warning "Failed to load email settings: $($_)"
            Write-Log "Failed to load email settings: $($_)" -Level 'Warning'
        }
    }
    
    # Return default settings
    return @{
        To = ""
        From = ""
        SmtpServer = ""
        Port = 25
        UseSSL = $false
        Username = ""
        Password = ""
        EnableNotifications = $false
    }
}

# Function to save email settings
function Save-EmailSettings {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Settings,
        
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\email_settings.json"
    )
    
    try {
        $Settings | ConvertTo-Json | Set-Content -Path $SettingsPath
        return $true
    } catch {
        Write-Warning "Failed to save email settings: $($_)"
        Write-Log "Failed to save email settings: $($_)" -Level 'Warning'
        return $false
    }
}

# Function to send renewal notification
function Send-RenewalNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain,
        
        [Parameter()]
        [bool]$Success,
        
        [Parameter()]
        [string]$ErrorMessage,
        
        [Parameter()]
        [DateTime]$ExpirationDate
    )
    
    $emailSettings = Get-EmailSettings
    
    if (-not $emailSettings.EnableNotifications) {
        Write-Verbose "Email notifications are disabled"
        return
    }
    
    if ($Success) {
        $subject = "Certificate Renewal Success: $MainDomain"
        $body = @"
<html>
<body>
<h2>Certificate Renewal Successful</h2>
<p>The SSL/TLS certificate for <strong>$MainDomain</strong> has been successfully renewed.</p>
<p>The certificate is valid until <strong>$($ExpirationDate.ToString('yyyy-MM-dd HH:mm:ss'))</strong>.</p>
<p>This is an automated notification from the AutoCertLE certificate management system.</p>
</body>
</html>
"@
    } else {
        $subject = "Certificate Renewal FAILED: $MainDomain"
        $body = @"
<html>
<body>
<h2>Certificate Renewal Failed</h2>
<p>The SSL/TLS certificate renewal for <strong>$MainDomain</strong> has failed.</p>
<p>Error message: <strong>$ErrorMessage</strong></p>
<p>Please check the logs and take appropriate action to renew the certificate manually.</p>
<p>This is an automated notification from the AutoCertLE certificate management system.</p>
</body>
</html>
"@
    }
    
    Send-EmailNotification -Subject $subject -Body $body
}

# Function to configure email notifications
function Set-EmailNotificationSettings {
    [CmdletBinding()]
    param()
    
    $emailSettings = Get-EmailSettings
    
    Write-Host "`nEmail Notification Settings:" -ForegroundColor Cyan
    Write-Host "1) Enable/Disable Notifications: $($emailSettings.EnableNotifications)"
    Write-Host "2) Recipient Email: $($emailSettings.To)"
    Write-Host "3) Sender Email: $($emailSettings.From)"
    Write-Host "4) SMTP Server: $($emailSettings.SmtpServer)"
    Write-Host "5) SMTP Port: $($emailSettings.Port)"
    Write-Host "6) Use SSL: $($emailSettings.UseSSL)"
    Write-Host "7) SMTP Authentication"
    Write-Host "8) Test Email Settings"
    Write-Host "0) Back"
    
    $choice = Get-ValidatedInput -Prompt "`nEnter your choice (0-8)" -ValidOptions (0..8)
    
    switch ($choice) {
        0 { return }
        1 {
            $enableNotifications = Read-Host "`nEnable email notifications? (Y/N)"
            $emailSettings.EnableNotifications = $enableNotifications -match '^[Yy]$'
        }
        2 {
            $to = Read-Host "`nEnter recipient email address"
            if ($to) {
                $emailSettings.To = $to
            }
        }
        3 {
            $from = Read-Host "`nEnter sender email address"
            if ($from) {
                $emailSettings.From = $from
            }
        }
        4 {
            $smtpServer = Read-Host "`nEnter SMTP server address"
            if ($smtpServer) {
                $emailSettings.SmtpServer = $smtpServer
            }
        }
        5 {
            $port = Read-Host "`nEnter SMTP port (default: 25)"
            if ($port -match '^\d+$') {
                $emailSettings.Port = [int]$port
            }
        }
        6 {
            $useSSL = Read-Host "`nUse SSL for SMTP connection? (Y/N)"
            $emailSettings.UseSSL = $useSSL -match '^[Yy]$'
        }
        7 {
            $useAuth = Read-Host "`nUse SMTP authentication? (Y/N)"
            if ($useAuth -match '^[Yy]$') {
                $username = Read-Host "`nEnter SMTP username"
                $password = Read-Host "`nEnter SMTP password" -AsSecureString
                
                $emailSettings.Username = $username
                
                # Convert secure string to plain text for storage
                $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($password)
                $emailSettings.Password = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
            } else {
                $emailSettings.Username = ""
                $emailSettings.Password = ""
            }
        }
        8 {
            # Test email settings
            Write-Host "`nSending test email..."
            $subject = "AutoCertLE Test Email"
            $body = @"
<html>
<body>
<h2>AutoCertLE Test Email</h2>
<p>This is a test email from the AutoCertLE certificate management system.</p>
<p>If you received this email, your email notification settings are configured correctly.</p>
</body>
</html>
"@
            $result = Send-EmailNotification -Subject $subject -Body $body
            
            if ($result) {
                Write-Host "`nTest email sent successfully." -ForegroundColor Green
            } else {
                Write-Host "`nFailed to send test email. Please check your settings." -ForegroundColor Red
            }
            
            Read-Host "`nPress Enter to continue"
            Set-EmailNotificationSettings
            return
        }
    }
    
    # Save settings
    Save-EmailSettings -Settings $emailSettings
    
    Write-Host "`nEmail notification settings updated." -ForegroundColor Green
    Read-Host "`nPress Enter to continue"
    
    # Show the menu again
    Set-EmailNotificationSettings
}
