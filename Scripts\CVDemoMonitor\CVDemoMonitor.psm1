# CVDemoMonitor.psm1
# Main module file that imports all function files

# Get the path to the module directory
$moduleRoot = $PSScriptRoot

# Import all private functions
$privateFunctions = Get-ChildItem -Path "$moduleRoot\Private\*.ps1" -ErrorAction SilentlyContinue
foreach ($function in $privateFunctions) {
    try {
        . $function.FullName
    }
    catch {
        Write-Error "Failed to import private function $($function.FullName): $_"
    }
}

# Import all public functions
$publicFunctions = Get-ChildItem -Path "$moduleRoot\Public\*.ps1" -ErrorAction SilentlyContinue
foreach ($function in $publicFunctions) {
    try {
        . $function.FullName
    }
    catch {
        Write-Error "Failed to import public function $($function.FullName): $_"
    }
}

# Export public functions
Export-ModuleMember -Function 'Start-CVMonitoring'
