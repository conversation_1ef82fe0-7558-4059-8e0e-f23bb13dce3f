<#
.SYNOPSIS
    End-to-end test for the AutoCertLE module.

.DESCRIPTION
    This script performs an end-to-end test of the entire certificate lifecycle using the Let's Encrypt staging environment.

.PARAMETER Domain
    The domain to use for testing. You must own this domain and be able to modify its DNS records.

.PARAMETER Email
    The email address to use for Let's Encrypt registration.

.PARAMETER DnsPlugin
    The DNS plugin to use for testing. Default is 'Manual'.

.PARAMETER ManagementServer
    The name of a Management Server to install the certificate to. Default is the local machine.

.PARAMETER RecordingServer
    The name of a Recording Server to install the certificate to. Default is the local machine.

.PARAMETER SkipCleanup
    If specified, the test certificates will not be removed after testing.

.EXAMPLE
    .\Test-EndToEnd.ps1 -Domain "test.example.com" -Email "<EMAIL>" -DnsPlugin "Cloudflare"
#>
[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$Domain,
    
    [Parameter(Mandatory = $true)]
    [string]$Email,
    
    [Parameter()]
    [string]$DnsPlugin = 'Manual',
    
    [Parameter()]
    [string]$ManagementServer = $env:COMPUTERNAME,
    
    [Parameter()]
    [string]$RecordingServer = $env:COMPUTERNAME,
    
    [Parameter()]
    [switch]$SkipCleanup
)

# Import the module
$modulePath = Join-Path -Path $PSScriptRoot -ChildPath '..\AutoCertLE\AutoCertLE.psd1'
Import-Module $modulePath -Force

# Start logging
$logFile = Join-Path -Path $PSScriptRoot -ChildPath "EndToEndTest_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
Start-Transcript -Path $logFile -Append

try {
    Write-Host "=== AutoCertLE End-to-End Test ===" -ForegroundColor Cyan
    Write-Host "Domain: $Domain" -ForegroundColor Cyan
    Write-Host "Email: $Email" -ForegroundColor Cyan
    Write-Host "DNS Plugin: $DnsPlugin" -ForegroundColor Cyan
    Write-Host "Management Server: $ManagementServer" -ForegroundColor Cyan
    Write-Host "Recording Server: $RecordingServer" -ForegroundColor Cyan
    Write-Host "Skip Cleanup: $SkipCleanup" -ForegroundColor Cyan
    Write-Host "=================================" -ForegroundColor Cyan
    
    # Step 1: Ensure we're using the staging environment
    Write-Host "`nStep 1: Setting Let's Encrypt staging environment..." -ForegroundColor Cyan
    Set-ACMEServer -StagingMode
    
    # Step 2: Test DNS provider detection
    Write-Host "`nStep 2: Testing DNS provider detection..." -ForegroundColor Cyan
    $dnsInfo = Get-SuggestedDNSPlugin -Domain $Domain
    Write-Host "Detected provider: $($dnsInfo.DetectedProvider) (Confidence: $($dnsInfo.Confidence)%)" -ForegroundColor Green
    Write-Host "Suggested plugin: $($dnsInfo.SuggestedPlugin)" -ForegroundColor Green
    
    # Step 3: Register a new certificate
    Write-Host "`nStep 3: Registering a new certificate..." -ForegroundColor Cyan
    if ($DnsPlugin -eq 'Manual') {
        Write-Host "Using Manual plugin. You will need to create DNS records manually." -ForegroundColor Yellow
        $cert = Register-Certificate -Domain $Domain -Email $Email -AcceptTOS -Force
    } else {
        Write-Host "Using $DnsPlugin plugin." -ForegroundColor Yellow
        $cert = Register-Certificate -Domain $Domain -Email $Email -AcceptTOS -Force -Plugin $DnsPlugin
    }
    
    if (-not $cert) {
        throw "Certificate registration failed"
    }
    
    Write-Host "Certificate registration successful!" -ForegroundColor Green
    Write-Host "Certificate details:" -ForegroundColor Cyan
    Write-Host "  Main domain: $($cert.MainDomain)" -ForegroundColor Cyan
    Write-Host "  Expires: $($cert.Certificate.NotAfter)" -ForegroundColor Cyan
    Write-Host "  Issuer: $($cert.Certificate.Issuer)" -ForegroundColor Cyan
    
    # Step 4: Test certificate retrieval
    Write-Host "`nStep 4: Testing certificate retrieval..." -ForegroundColor Cyan
    $certs = Get-ExistingCertificates
    $foundCert = $certs | Where-Object { $_.MainDomain -eq $Domain }
    if (-not $foundCert) {
        throw "Could not retrieve the test certificate"
    }
    Write-Host "Certificate retrieval successful!" -ForegroundColor Green
    
    # Step 5: Test certificate installation to Management Server
    Write-Host "`nStep 5: Testing certificate installation to Management Server..." -ForegroundColor Cyan
    $mgmtResult = Install-SalientCertificate -Certificate $cert -ServerType ManagementServer -ComputerName $ManagementServer
    if (-not $mgmtResult -or -not $mgmtResult.Success) {
        Write-Warning "Management Server certificate installation failed or returned unexpected result"
    } else {
        Write-Host "Management Server certificate installation successful!" -ForegroundColor Green
    }
    
    # Step 6: Test certificate installation to Recording Server
    Write-Host "`nStep 6: Testing certificate installation to Recording Server..." -ForegroundColor Cyan
    $recResult = Install-SalientCertificate -Certificate $cert -ServerType RecordingServer -ComputerName $RecordingServer -CreateFolders
    if (-not $recResult -or -not $recResult.Success) {
        Write-Warning "Recording Server certificate installation failed or returned unexpected result"
    } else {
        Write-Host "Recording Server certificate installation successful!" -ForegroundColor Green
        if ($recResult.RecordingServer) {
            Write-Host "Certificate number: $($recResult.RecordingServer.CertificateNumber)" -ForegroundColor Cyan
        }
    }
    
    # Step 7: Test certificate monitoring
    Write-Host "`nStep 7: Testing certificate monitoring..." -ForegroundColor Cyan
    $monitoringReport = Get-CertificateMonitoringReport
    Write-Host "Monitoring report generated with $($monitoringReport.TotalCount) certificates" -ForegroundColor Green
    Write-Host "OK: $($monitoringReport.OkCount)" -ForegroundColor Green
    Write-Host "Warning: $($monitoringReport.WarningCount)" -ForegroundColor Yellow
    Write-Host "Critical: $($monitoringReport.CriticalCount)" -ForegroundColor Red
    Write-Host "Expired: $($monitoringReport.ExpiredCount)" -ForegroundColor Red
    
    # Step 8: Test automatic renewal configuration
    Write-Host "`nStep 8: Testing automatic renewal configuration..." -ForegroundColor Cyan
    Set-AutomaticRenewal -Domain $Domain -Enable
    Write-Host "Automatic renewal configured" -ForegroundColor Green
    
    # Step 9: Test certificate renewal
    Write-Host "`nStep 9: Testing certificate renewal..." -ForegroundColor Cyan
    $renewalResult = Invoke-AutoCertRenewal -MainDomain $Domain -Force
    if ($renewalResult) {
        Write-Host "Certificate renewal successful!" -ForegroundColor Green
    } else {
        Write-Warning "Certificate renewal failed or was not needed"
    }
    
    # Step 10: Cleanup
    if (-not $SkipCleanup) {
        Write-Host "`nStep 10: Cleaning up..." -ForegroundColor Cyan
        
        # Disable automatic renewal
        Set-AutomaticRenewal -Domain $Domain -Disable
        Write-Host "Automatic renewal disabled" -ForegroundColor Green
        
        # Remove the certificate
        Remove-Certificate -Domain $Domain -Force
        Write-Host "Certificate removed" -ForegroundColor Green
    } else {
        Write-Host "`nStep 10: Skipping cleanup as requested" -ForegroundColor Yellow
    }
    
    Write-Host "`nEnd-to-end testing completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error during end-to-end testing: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
} finally {
    Stop-Transcript
    Write-Host "`nTest log saved to: $logFile" -ForegroundColor Cyan
}
