<#
.SYNOPSIS
    Monitors CompleteView services for crashes and sends Slack alerts with dump file paths.

.DESCRIPTION
    Checks for Event ID 1000 crashes of the following CompleteView services:
    - RecordingServer64.exe (CompleteView Recording Server)
    - AdminService64.exe (CompleteView Administrative Service)
    - ManagementServer.exe (CompleteView Management Server)

    Sends an immediate Slack alert when a crash is detected.
    If the dump file is not available yet, the script waits and sends a second notification once it appears (up to 5 minutes).
    If the dump file never appears, a final Slack message is sent indicating the dump is unavailable.

.NOTES
    Author: <EMAIL>
    Version: 3.0
    Updated: 2025-05-14
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidatePattern('^https://hooks\.slack\.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$')]
    [string]$SlackWebhookUrl = "*******************************************************************************",

    # Original webhook URL (commented out for testing)
    # [string]$SlackWebhookUrl = "*******************************************************************************",

    [Parameter(Mandatory = $false)]
    [string]$LogPath = "C:\ProgramData\Salient Security Platform\CrashDumps\CVCrashMonitor.log",

    [Parameter(Mandatory = $false)]
    [string]$StateFilePath = "",

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 10080)] # Max 1 week (10080 minutes)
    [int]$LookbackMinutes = 60,

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 60)]
    [int]$DumpWaitTimeoutMinutes = 5,

    [Parameter(Mandatory = $false)]
    [switch]$NoSlack,

    [Parameter(Mandatory = $false)]
    [switch]$TestMode,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorRecordingServer,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorAdminService,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorManagementServer,

    [Parameter(Mandatory = $false)]
    [ValidateSet('ERROR', 'WARNING', 'INFO', 'DEBUG')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [switch]$LegacyMode,

    [Parameter(Mandatory = $false)]
    [switch]$RunAsService,

    [Parameter(Mandatory = $false)]
    [int]$HeartbeatIntervalMinutes = 15,

    [Parameter(Mandatory = $false)]
    [int]$WatchdogIntervalMinutes = 30,

    [Parameter(Mandatory = $false)]
    [string]$EventLogSource = "CVDemoCrashAlert"
)

# Global variables for event subscriptions
$script:EventSubscriptions = @()
$script:IsRunning = $true
$script:ServiceConfigs = @{}
$script:HeartbeatTimer = $null
$script:WatchdogTimer = $null
$script:LastHeartbeatTime = Get-Date

#region Functions

function Register-EventSubscriptions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [int[]]$EventIDs = @(1000, 1001, 7031, 7034, 2004, 6008),

        [Parameter(Mandatory = $false)]
        [string]$LogName = "Application"
    )

    try {
        Write-Log "Registering WMI event subscriptions for event IDs: $($EventIDs -join ', ')" -Level 'INFO'

        # Create a list to store all app names we want to monitor
        $appNames = $ServiceConfigs.Values | ForEach-Object { $_.AppName }
        Write-Log "Monitoring applications: $($appNames -join ', ')" -Level 'INFO'

        # Create event queries for each event ID
        foreach ($eventId in $EventIDs) {
            $query = "SELECT * FROM __InstanceCreationEvent WITHIN 2 WHERE " +
                     "TargetInstance ISA 'Win32_NTLogEvent' AND " +
                     "TargetInstance.LogFile = '$LogName' AND " +
                     "TargetInstance.EventCode = $eventId"

            Write-Log "Registering subscription for Event ID $eventId" -Level 'DEBUG'

            # Create the action script block
            $action = {
                param($eventObj, $eventSubscriptionId)

                # Get the event details
                $eventRecord = $eventObj.SourceEventArgs.NewEvent.TargetInstance

                # Convert to a format compatible with our existing processing
                $eventObj = [PSCustomObject]@{
                    Id = $eventRecord.EventCode
                    Message = $eventRecord.Message
                    TimeCreated = [DateTime]::ParseExact($eventRecord.TimeGenerated.Split('.')[0], 'yyyyMMddHHmmss', $null)
                    ProviderName = $eventRecord.SourceName
                }

                # Process the event
                Process-RealTimeEvent -LogEvent $eventObj
            }

            # Register the event subscription
            $subscription = Register-WmiEvent -Query $query -Action $action -SourceIdentifier "CVCrashAlert_Event_$eventId" -ErrorAction Stop

            # Store the subscription for later cleanup
            $script:EventSubscriptions += $subscription

            Write-Log "Successfully registered subscription for Event ID $eventId" -Level 'INFO'
        }

        Write-Log "All event subscriptions registered successfully" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error registering event subscriptions: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Unregister-EventSubscriptions {
    [CmdletBinding()]
    param()

    try {
        Write-Log "Unregistering all event subscriptions" -Level 'INFO'

        foreach ($subscription in $script:EventSubscriptions) {
            try {
                Unregister-Event -SourceIdentifier $subscription.Name -Force -ErrorAction SilentlyContinue
                Write-Log "Unregistered subscription: $($subscription.Name)" -Level 'DEBUG'
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error unregistering subscription $($subscription.Name): $errorMsg" -Level 'WARNING'
            }
        }

        # Also clean up any lingering event subscribers
        Get-EventSubscriber -ErrorAction SilentlyContinue |
            Where-Object { $_.SourceIdentifier -like "CVCrashAlert_*" } |
            ForEach-Object {
                try {
                    Unregister-Event -SourceIdentifier $_.SourceIdentifier -Force -ErrorAction SilentlyContinue
                    Write-Log "Unregistered lingering subscription: $($_.SourceIdentifier)" -Level 'DEBUG'
                }
                catch {
                    # Ignore errors for cleanup
                }
            }

        # Clear the subscriptions array
        $script:EventSubscriptions = @()

        Write-Log "All event subscriptions unregistered" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error during subscription cleanup: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Process-RealTimeEvent {
    # Note: Using a non-standard verb for internal function only
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSUseApprovedVerbs', '')]
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSObject]$LogEvent
    )

    try {
        Write-Log "Processing real-time event: ID $($LogEvent.Id) from $($LogEvent.ProviderName)" -Level 'INFO'

        # Format the event details using our existing function
        $details = Format-EventDetails -LogEvent $LogEvent

        # Skip if we couldn't extract the faulting app
        if (-not $details.FaultingApp) {
            Write-Log "Skipping event - could not determine faulting application" -Level 'WARNING'
            return
        }

        # Check if the faulting app matches any of our monitored services
        $matchedService = $null

        foreach ($serviceName in $script:ServiceConfigs.Keys) {
            $serviceConfig = $script:ServiceConfigs[$serviceName]
            if ($details.FaultingApp -eq $serviceConfig.AppName) {
                $matchedService = $serviceConfig
                break
            }
        }

        # If we found a matching service, process the event
        if ($matchedService) {
            $appName = $matchedService.AppName
            $friendlyName = $matchedService.FriendlyName
            $dumpFolder = $matchedService.DumpPath

            Write-Log "Processing real-time event for $friendlyName ($appName)" -Level 'INFO'

            $hostname = $env:COMPUTERNAME
            $time = $details.Time
            $faultingModule = if ($null -eq $details.FaultingModule) { "Unknown" } else { $details.FaultingModule }
            $exceptionCode = if ($null -eq $details.ExceptionCode) { "Unknown" } else { $details.ExceptionCode }

            # Ensure dump directory exists
            if (-not (Test-Path -Path $dumpFolder -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $dumpFolder -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created dump directory for $friendlyName - $dumpFolder" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to create dump directory for ${friendlyName}: $errorMsg" -Level 'ERROR'
                }
            }

            # Ensure we have a valid process ID
            if (-not $details.ProcessIdDecimal) {
                Write-Log "Could not determine process ID from event, using timestamp instead" -Level 'WARNING'
                $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), (Get-Date).ToString("yyyyMMddHHmmss")
            }
            else {
                $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $details.ProcessIdDecimal
            }

            $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile

            # Check if dump file exists
            $dumpExistsInitially = Test-Path -Path $fullDumpPath

            # Send initial notification if Slack is enabled
            if (-not $NoSlack) {
                try {
                    Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                    Write-Log "Sent initial Slack notification for $friendlyName crash at $time" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to send initial Slack notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                }
            }
            else {
                Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
            }

            # If dump doesn't exist initially, wait for it
            if (-not $dumpExistsInitially) {
                $dumpAvailable = Wait-ForDumpFile -DumpPath $fullDumpPath -TimeoutMinutes $DumpWaitTimeoutMinutes

                # Send follow-up notification if Slack is enabled
                if (-not $NoSlack) {
                    try {
                        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false

                        if ($dumpAvailable) {
                            Write-Log "Sent second Slack notification for $friendlyName dump availability at $time" -Level 'INFO'
                        }
                        else {
                            Write-Log "Sent notification that $friendlyName dump did not appear after timeout at $time" -Level 'WARNING'
                        }
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-Log "Failed to send follow-up Slack notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                    }
                }
                else {
                    if ($dumpAvailable) {
                        Write-Log "Dump file for $friendlyName is now available at: $fullDumpPath" -Level 'INFO'
                    }
                    else {
                        Write-Log "Dump file for $friendlyName did not appear after waiting $DumpWaitTimeoutMinutes minutes" -Level 'WARNING'
                    }
                }
            }
        }
        else {
            # This crash is not for one of our monitored services
            Write-Log "Skipping event - faulting application '$($details.FaultingApp)' is not in the monitored services list" -Level 'DEBUG'
        }
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error processing real-time event: $errorMsg" -Level 'ERROR'
    }
}

function Start-EventMonitoring {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [int]$HeartbeatIntervalMinutes = 15,

        [Parameter(Mandatory = $false)]
        [switch]$RunAsService
    )

    try {
        # Store service configs globally for event handlers to access
        $script:ServiceConfigs = $ServiceConfigs

        # Register event subscriptions
        $success = Register-EventSubscriptions -ServiceConfigs $ServiceConfigs
        if (-not $success) {
            Write-Log "Failed to register event subscriptions, aborting real-time monitoring" -Level 'ERROR'
            return $false
        }

        # Set up heartbeat timer if running as a service
        if ($RunAsService) {
            # Register event log source if it doesn't exist
            if (-not [System.Diagnostics.EventLog]::SourceExists($EventLogSource)) {
                try {
                    [System.Diagnostics.EventLog]::CreateEventSource($EventLogSource, "Application")
                    Write-Log "Created event log source: $EventLogSource" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to create event log source: $errorMsg" -Level 'WARNING'
                }
            }

            # Create a timer for heartbeat logging
            $heartbeatIntervalMs = $HeartbeatIntervalMinutes * 60 * 1000
            $script:HeartbeatTimer = New-Object System.Timers.Timer
            $script:HeartbeatTimer.Interval = $heartbeatIntervalMs
            $script:HeartbeatTimer.AutoReset = $true

            # Define the heartbeat action
            $heartbeatAction = {
                $script:LastHeartbeatTime = Get-Date
                $uptime = (Get-Date) - $script:scriptStartTime
                $uptimeStr = "{0:D2}d:{1:D2}h:{2:D2}m:{3:D2}s" -f $uptime.Days, $uptime.Hours, $uptime.Minutes, $uptime.Seconds

                Write-Log "Heartbeat: CVDemoCrashAlert is running (Uptime: $uptimeStr)" -Level 'INFO'

                # Write to event log if running as a service
                if ($RunAsService) {
                    try {
                        [System.Diagnostics.EventLog]::WriteEntry(
                            $EventLogSource,
                            "CVDemoCrashAlert heartbeat - Service is running (Uptime: $uptimeStr)",
                            [System.Diagnostics.EventLogEntryType]::Information,
                            1000
                        )
                    }
                    catch {
                        # Ignore event log errors in the heartbeat
                    }
                }

                # Perform health checks
                Test-ServiceStatus -ServiceConfigs $script:ServiceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack
            }

            # Register the heartbeat event
            $script:HeartbeatTimer.Elapsed.Register($heartbeatAction)
            $script:HeartbeatTimer.Start()

            Write-Log "Heartbeat timer started with interval of $HeartbeatIntervalMinutes minutes" -Level 'INFO'
        }

        # Register Ctrl+C handler for graceful shutdown
        [Console]::TreatControlCAsInput = $true

        Write-Log "Real-time event monitoring started. Press Ctrl+C to stop." -Level 'INFO'

        # Main loop to keep the script running
        while ($script:IsRunning) {
            # Check for Ctrl+C
            if ([Console]::KeyAvailable) {
                $key = [Console]::ReadKey($true)
                if (($key.Modifiers -band [ConsoleModifiers]::Control) -and ($key.Key -eq 'C')) {
                    Write-Log "Ctrl+C detected, shutting down..." -Level 'INFO'
                    $script:IsRunning = $false
                    break
                }
            }

            # Sleep to reduce CPU usage
            Start-Sleep -Milliseconds 500
        }

        # Cleanup before exiting
        if ($script:HeartbeatTimer) {
            $script:HeartbeatTimer.Stop()
            $script:HeartbeatTimer.Dispose()
        }

        Unregister-EventSubscriptions

        Write-Log "Real-time event monitoring stopped" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error in event monitoring: $errorMsg" -Level 'ERROR'

        # Cleanup on error
        if ($script:HeartbeatTimer) {
            $script:HeartbeatTimer.Stop()
            $script:HeartbeatTimer.Dispose()
        }

        Unregister-EventSubscriptions

        return $false
    }
}

function Install-ServiceTask {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$TaskName = "CVDemoCrashAlert_Monitor",

        [Parameter(Mandatory = $false)]
        [string]$Description = "CompleteView Crash Monitoring Service",

        [Parameter(Mandatory = $false)]
        [string]$ScriptPath = $PSCommandPath
    )

    try {
        Write-Log "Installing scheduled task for service mode: $TaskName" -Level 'INFO'

        # Check if the task already exists
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue

        if ($existingTask) {
            Write-Log "Task already exists, removing it first" -Level 'INFO'
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false -ErrorAction Stop
        }

        # Create the action to run the PowerShell script
        $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -File `"$ScriptPath`" -RunAsService"

        # Create a trigger to run at system startup
        $trigger = New-ScheduledTaskTrigger -AtStartup

        # Set the principal to run with highest privileges
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

        # Create settings to allow the task to run indefinitely and restart if it fails
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -RestartCount 3 -RestartInterval (New-TimeSpan -Minutes 1) -ExecutionTimeLimit (New-TimeSpan -Hours 0)

        # Register the scheduled task
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description $Description -ErrorAction Stop

        Write-Log "Successfully installed scheduled task: $TaskName" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error installing scheduled task: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Uninstall-ServiceTask {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$TaskName = "CVDemoCrashAlert_Monitor"
    )

    try {
        Write-Log "Uninstalling scheduled task: $TaskName" -Level 'INFO'

        # Check if the task exists
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue

        if ($existingTask) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false -ErrorAction Stop
            Write-Log "Successfully uninstalled scheduled task: $TaskName" -Level 'INFO'
            return $true
        }
        else {
            Write-Log "Task does not exist: $TaskName" -Level 'WARNING'
            return $false
        }
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error uninstalling scheduled task: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Test-ServiceStatus {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking service status for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        # Map the executable name to the actual Windows service name
        $windowsServiceName = switch ($config.AppName) {
            "RecordingServer64.exe" { "CVRecordingServer" }
            "AdminService64.exe" { "CVAdminService" }
            "ManagementServer.exe" { "CVManagementServer" }
            default { $null }
        }

        if ($windowsServiceName) {
            try {
                $service = Get-Service -Name $windowsServiceName -ErrorAction Stop

                if ($service.Status -ne 'Running') {
                    Write-Log "$friendlyName service is not running (Status: $($service.Status))" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Service Not Running*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Status:* $($service.Status)
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent service status notification for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send service status notification for $friendlyName`: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
                else {
                    Write-Log "$friendlyName service is running" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error checking service status for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Could not determine Windows service name for $friendlyName" -Level 'WARNING'
        }
    }
}

function Measure-ResourceUsage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [int]$MemoryThresholdMB = 2048,  # 2GB

        [Parameter(Mandatory = $false)]
        [int]$CpuThresholdPercent = 80
    )

    Write-Log "Checking resource usage for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $appName = $config.AppName
        $friendlyName = $config.FriendlyName
        $processName = $appName -replace '\.exe$', ''

        try {
            $process = Get-Process -Name $processName -ErrorAction SilentlyContinue

            if ($process) {
                # Check memory usage
                $memoryUsageMB = [Math]::Round($process.WorkingSet64 / 1MB, 2)
                Write-Log "$friendlyName memory usage: $memoryUsageMB MB" -Level 'INFO'

                # Check CPU usage (requires multiple samples)
                $cpuSamples = @()
                for ($i = 0; $i -lt 3; $i++) {
                    $startCPU = $process.TotalProcessorTime
                    $startTime = Get-Date
                    Start-Sleep -Seconds 2
                    $process.Refresh()
                    $endCPU = $process.TotalProcessorTime
                    $endTime = Get-Date
                    $cpuUsage = [Math]::Round(($endCPU - $startCPU).TotalSeconds / ($endTime - $startTime).TotalSeconds * 100 / [Environment]::ProcessorCount, 2)
                    $cpuSamples += $cpuUsage
                }

                $avgCpuUsage = ($cpuSamples | Measure-Object -Average).Average
                Write-Log "$friendlyName CPU usage: $avgCpuUsage%" -Level 'INFO'

                # Check if thresholds are exceeded
                $memoryExceeded = $memoryUsageMB -gt $MemoryThresholdMB
                $cpuExceeded = $avgCpuUsage -gt $CpuThresholdPercent

                if ($memoryExceeded -or $cpuExceeded) {
                    $alertMessage = "$friendlyName resource usage alert:"
                    if ($memoryExceeded) { $alertMessage += " Memory: $memoryUsageMB MB (threshold: $MemoryThresholdMB MB)" }
                    if ($cpuExceeded) { $alertMessage += " CPU: $avgCpuUsage% (threshold: $CpuThresholdPercent%)" }

                    Write-Log $alertMessage -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *High Resource Usage*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
*Memory:* $memoryUsageMB MB (threshold: $MemoryThresholdMB MB)
*CPU:* $avgCpuUsage% (threshold: $CpuThresholdPercent%)"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent resource usage alert for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send resource usage alert for $friendlyName`: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
            }
            else {
                Write-Log "Process $processName not found - service may not be running" -Level 'WARNING'
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Error checking resource usage for ${friendlyName}: $errorMsg" -Level 'ERROR'
        }
    }
}

function Test-DatabaseConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConnectionString = "",

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    # If no connection string is provided, try to get it from CompleteView config
    if ([string]::IsNullOrEmpty($ConnectionString)) {
        try {
            # Path to CompleteView config file that contains DB connection info
            $configPath = "C:\Program Files\Salient Security Platform\CompleteView\Management Server\appsettings.json"

            if (Test-Path $configPath) {
                $config = Get-Content $configPath -Raw | ConvertFrom-Json
                $ConnectionString = $config.ConnectionString
            }

            if ([string]::IsNullOrEmpty($ConnectionString)) {
                Write-Log "Could not find database connection string in appsettings.json" -Level 'WARNING'
                return $false
            }

            Write-Log "Successfully retrieved database connection string from appsettings.json" -Level 'INFO'
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Error retrieving database connection string: $errorMsg" -Level 'ERROR'
            return $false
        }
    }

    try {
        # Extract server and database name from connection string
        $serverPattern = "Server=([^;]+)"
        $dbPattern = "Database=([^;]+)"

        $server = if ($ConnectionString -match $serverPattern) { $matches[1] } else { $null }
        $database = if ($ConnectionString -match $dbPattern) { $matches[1] } else { $null }

        if (-not $server -or -not $database) {
            Write-Log "Could not parse server or database from connection string" -Level 'WARNING'
            return $false
        }

        # Test connection using SQL Server PowerShell module if available
        if (Get-Module -ListAvailable -Name SqlServer) {
            Import-Module SqlServer
            $result = Invoke-Sqlcmd -ServerInstance $server -Database $database -Query "SELECT 1 AS IsConnected" -ConnectionTimeout 10 -ErrorAction Stop
            $connected = $result.IsConnected -eq 1
        }
        else {
            # Fallback to .NET SqlClient
            $conn = New-Object System.Data.SqlClient.SqlConnection
            $conn.ConnectionString = $ConnectionString
            $conn.Open()
            $connected = $conn.State -eq [System.Data.ConnectionState]::Open
            $conn.Close()
        }

        if ($connected) {
            Write-Log "Successfully connected to database $database on $server" -Level 'INFO'
            return $true
        }
        else {
            Write-Log "Failed to connect to database $database on $server" -Level 'WARNING'

            # Send notification if Slack is enabled
            if (-not $NoSlack -and $WebhookUrl) {
                $messageText = ":warning: *Database Connection Failure*

*Host:* $env:COMPUTERNAME
*Server:* $server
*Database:* $database
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                try {
                    Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                    Write-Log "Sent database connection failure notification" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to send database connection failure notification: $errorMsg" -Level 'ERROR'
                }
            }

            return $false
        }
    }
    catch {
        Write-Log "Error testing database connection: $_" -Level 'ERROR'

        # Send notification if Slack is enabled
        if (-not $NoSlack -and $WebhookUrl) {
            $errorMsg = $_.Exception.Message
            $messageText = ":warning: *Database Connection Error*

*Host:* $env:COMPUTERNAME
*Server:* $server
*Database:* $database
*Error:* $errorMsg
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            try {
                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                Write-Log "Sent database connection error notification" -Level 'INFO'
            }
            catch {
                $slackErrorMsg = $_.Exception.Message
                Write-Log "Failed to send database connection error notification: $slackErrorMsg" -Level 'ERROR'
            }
        }

        return $false
    }
}

function Test-DiskSpace {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string[]]$PathsToCheck = @(
            "C:\Program Files\Salient Security Platform",
            "C:\ProgramData\Salient Security Platform"
        ),

        [Parameter(Mandatory = $false)]
        [int]$ThresholdPercent = 10,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking disk space for critical paths" -Level 'INFO'

    $checkedDrives = @{}

    foreach ($path in $PathsToCheck) {
        if (Test-Path $path) {
            $drive = (Get-Item $path).PSDrive.Name

            # Skip if we've already checked this drive
            if ($checkedDrives.ContainsKey($drive)) {
                continue
            }

            $checkedDrives[$drive] = $true

            try {
                $driveInfo = Get-PSDrive -Name $drive -PSProvider FileSystem
                $freeSpaceGB = [Math]::Round($driveInfo.Free / 1GB, 2)
                $totalSpaceGB = [Math]::Round(($driveInfo.Free + $driveInfo.Used) / 1GB, 2)
                $freePercent = [Math]::Round(($driveInfo.Free / ($driveInfo.Free + $driveInfo.Used)) * 100, 2)

                Write-Log "Drive ${drive}: has $freeSpaceGB GB free ($freePercent%)" -Level 'INFO'

                if ($freePercent -lt $ThresholdPercent) {
                    Write-Log "Low disk space on drive ${drive}: ($freePercent% free, threshold: $ThresholdPercent%)" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Low Disk Space*

*Host:* $env:COMPUTERNAME
*Drive:* ${drive}:
*Free Space:* $freeSpaceGB GB of $totalSpaceGB GB ($freePercent%)
*Threshold:* $ThresholdPercent%
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent low disk space notification for drive ${drive}:" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send low disk space notification for drive ${drive}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
            }
            catch {
                Write-Log "Error checking disk space for drive ${drive}: $_" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Path not found: $path" -Level 'WARNING'
        }
    }
}

function Search-LogFiles {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [int]$LookbackMinutes = 60
    )

    Write-Log "Analyzing log files for critical errors" -Level 'INFO'

    $logPaths = @{
        "RecordingServer" = "C:\ProgramData\Salient Security Platform\Logs\RecordingServer*.log"
        "AdminService" = "C:\ProgramData\Salient Security Platform\Logs\AdminService*.log"
        "ManagementServer" = "C:\ProgramData\Salient Security Platform\Logs\ManagementServer*.log"
    }

    $errorPatterns = @(
        "CRITICAL",
        "FATAL",
        "Exception",
        "Error",
        "Failed to",
        "Failure",
        "Unable to"
    )

    $startTime = (Get-Date).AddMinutes(-$LookbackMinutes)

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        if ($logPaths.ContainsKey($serviceName)) {
            $logPath = $logPaths[$serviceName]

            try {
                $logFiles = Get-ChildItem -Path $logPath -ErrorAction SilentlyContinue

                if ($logFiles.Count -eq 0) {
                    Write-Log "No log files found for $friendlyName at path: $logPath" -Level 'WARNING'
                    continue
                }

                $criticalErrors = @()

                foreach ($logFile in $logFiles) {
                    # Only check files modified since our lookback time
                    if ($logFile.LastWriteTime -ge $startTime) {
                        $content = Get-Content -Path $logFile.FullName -ErrorAction SilentlyContinue

                        foreach ($line in $content) {
                            # Try to extract timestamp from the line
                            $hasTimestamp = $line -match '\[([\d\-]+\s[\d:]+)'
                            $timestamp = if ($hasTimestamp) { [datetime]::Parse($matches[1]) } else { $null }

                            # Skip if we have a timestamp and it's before our lookback period
                            if ($timestamp -and $timestamp -lt $startTime) {
                                continue
                            }

                            # Check for error patterns
                            foreach ($pattern in $errorPatterns) {
                                if ($line -match $pattern) {
                                    $criticalErrors += [PSCustomObject]@{
                                        Service = $friendlyName
                                        LogFile = $logFile.Name
                                        Timestamp = if ($timestamp) { $timestamp.ToString("yyyy-MM-dd HH:mm:ss") } else { "Unknown" }
                                        Message = $line.Trim()
                                    }
                                    break  # Once we've matched a pattern, no need to check others
                                }
                            }
                        }
                    }
                }

                # Report critical errors
                if ($criticalErrors.Count -gt 0) {
                    Write-Log "Found $($criticalErrors.Count) critical errors in $friendlyName logs" -Level 'WARNING'

                    # Group by log file to avoid too many notifications
                    $groupedErrors = $criticalErrors | Group-Object -Property LogFile

                    foreach ($group in $groupedErrors) {
                        $logFileName = $group.Name
                        $errorCount = $group.Count
                        $sampleErrors = $group.Group | Select-Object -First 3

                        $errorSamples = ($sampleErrors | ForEach-Object { "- [$($_.Timestamp)] $($_.Message)" }) -join "`n"

                        # Send notification if Slack is enabled
                        if (-not $NoSlack -and $WebhookUrl) {
                            $messageText = ":warning: *Critical Errors in Logs*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Log File:* $logFileName
*Error Count:* $errorCount
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

*Sample Errors:*
$errorSamples"

                            if ($errorCount -gt 3) {
                                $messageText += "`n_...and $($errorCount - 3) more errors_"
                            }

                            try {
                                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                                Write-Log "Sent log error notification for $friendlyName ($logFileName)" -Level 'INFO'
                            }
                            catch {
                                $errorMsg = $_.Exception.Message
                                Write-Log "Failed to send log error notification for $friendlyName ($logFileName): $errorMsg" -Level 'ERROR'
                            }
                        }
                    }
                }
                else {
                    Write-Log "No critical errors found in $friendlyName logs" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error analyzing log files for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "No log path defined for $friendlyName" -Level 'DEBUG'
        }
    }
}

function Initialize-Environment {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$DefaultBasePath = "C:\ProgramData\Salient Security Platform\CrashDumps"
    )

    try {
        # Validate and set state file path
        if (-not $StateFilePath) {
            $StateFilePath = Join-Path -Path $DefaultBasePath -ChildPath "CVCrashMonitor.state"
            Write-Log "Using default state file path: $StateFilePath" -Level 'INFO'
        }
        else {
            Write-Log "Using provided state file path: $StateFilePath" -Level 'INFO'
        }

        # Validate and set log path
        if (-not $LogPath) {
            $LogPath = Join-Path -Path $DefaultBasePath -ChildPath "CVCrashMonitor.log"
            Write-Log "Using default log path: $LogPath" -Level 'INFO'
        }
        else {
            Write-Log "Using provided log path: $LogPath" -Level 'INFO'
        }

        # Ensure log directory exists
        $logDir = Split-Path -Path $LogPath -Parent
        if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
            try {
                New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                Write-Log "Created log directory: $logDir" -Level 'INFO'
            }
            catch {
                # If we can't create the directory, try to use a fallback location
                Write-Log "Failed to create log directory: $_" -Level 'ERROR'
                $tempPath = [System.IO.Path]::GetTempPath()
                $LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
                Write-Log "Using fallback log path: $LogPath" -Level 'WARNING'
            }
        }

        # Check if we can write to the log file
        try {
            $testContent = "Test log write"
            $testContent | Out-File -FilePath $LogPath -Append -ErrorAction Stop
            Write-Log "Verified log file is writable" -Level 'INFO'
        }
        catch {
            Write-Log "Cannot write to log file: $_" -Level 'ERROR'
            $tempPath = [System.IO.Path]::GetTempPath()
            $LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
            Write-Log "Using fallback log path: $LogPath" -Level 'WARNING'

            # Test the fallback path
            try {
                $testContent | Out-File -FilePath $LogPath -Append -ErrorAction Stop
            }
            catch {
                Write-Log "Cannot write to fallback log file either: $_" -Level 'ERROR'
                # At this point, we'll just use console logging
            }
        }

        # Check if we can write to the state file directory
        $stateDir = Split-Path -Path $StateFilePath -Parent
        if (-not (Test-Path -Path $stateDir -ErrorAction SilentlyContinue)) {
            try {
                New-Item -Path $stateDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                Write-Log "Created state file directory: $stateDir" -Level 'INFO'
            }
            catch {
                Write-Log "Failed to create state file directory: $_" -Level 'ERROR'
                $tempPath = [System.IO.Path]::GetTempPath()
                $StateFilePath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.state"
                Write-Log "Using fallback state file path: $StateFilePath" -Level 'WARNING'
            }
        }

        # Define service configurations
        $serviceConfigs = @{}

        # Recording Server configuration
        if ($MonitorRecordingServer) {
            $recordingServerConfig = @{
                AppName = "RecordingServer64.exe"
                DumpPath = Join-Path -Path $DefaultBasePath -ChildPath "RecordingServer"
                InstallPath = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\RecordingServer64.exe"
                FriendlyName = "CompleteView Recording Server"
            }
            $serviceConfigs["RecordingServer"] = $recordingServerConfig
        }

        # Administrative Service configuration
        if ($MonitorAdminService) {
            $adminServiceConfig = @{
                AppName = "AdminService64.exe"
                DumpPath = Join-Path -Path $DefaultBasePath -ChildPath "AdminService"
                InstallPath = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\AdminService64.exe"
                FriendlyName = "CompleteView Administrative Service"
            }
            $serviceConfigs["AdminService"] = $adminServiceConfig
        }

        # Management Server configuration
        if ($MonitorManagementServer) {
            $managementServerConfig = @{
                AppName = "ManagementServer.exe"
                DumpPath = Join-Path -Path $DefaultBasePath -ChildPath "ManagementServer"
                InstallPath = "C:\Program Files\Salient Security Platform\CompleteView\Management Server\ManagementServer.exe"
                FriendlyName = "CompleteView Management Server"
            }
            $serviceConfigs["ManagementServer"] = $managementServerConfig
        }

        # Ensure dump directories exist for each service
        foreach ($serviceName in $serviceConfigs.Keys) {
            $config = $serviceConfigs[$serviceName]
            $dumpDir = $config.DumpPath

            if (-not (Test-Path -Path $dumpDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $dumpDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created dump directory for $($config.FriendlyName): $dumpDir" -Level 'INFO'
                }
                catch {
                    Write-Log "Failed to create dump directory for $($config.FriendlyName): $_" -Level 'ERROR'
                }
            }
        }

        # Return the configuration
        return @{
            LogPath = $LogPath
            StateFilePath = $StateFilePath
            BaseDirectory = $DefaultBasePath
            ServiceConfigs = $serviceConfigs
        }
    }
    catch {
        Write-Log "Critical error in Initialize-Environment: $_" -Level 'ERROR'

        # Use temp directory as last resort
        $tempPath = [System.IO.Path]::GetTempPath()
        return @{
            LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
            StateFilePath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.state"
            BaseDirectory = $tempPath
            ServiceConfigs = @{}
        }
    }
}

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('INFO', 'WARNING', 'ERROR', 'DEBUG')]
        [string]$Level = 'INFO',

        [Parameter(Mandatory = $false)]
        [int]$MaxLogSizeMB = 10,

        [Parameter(Mandatory = $false)]
        [switch]$NoConsole
    )

    try {
        # Check if we should log this message based on the global LogLevel
        $logLevelPriority = @{
            'ERROR' = 0
            'WARNING' = 1
            'INFO' = 2
            'DEBUG' = 3
        }

        # Skip logging if the message level is less important than the global level
        if ($logLevelPriority[$Level] -gt $logLevelPriority[$script:LogLevel]) {
            return
        }

        # Format timestamp and message
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logMessage = "[$timestamp] [$Level] $Message"

        # Write to console with appropriate color
        if (-not $NoConsole) {
            switch ($Level) {
                'ERROR' {
                    Write-Host $logMessage -ForegroundColor Red
                }
                'WARNING' {
                    Write-Host $logMessage -ForegroundColor Yellow
                }
                'INFO' {
                    Write-Host $logMessage -ForegroundColor White
                }
                'DEBUG' {
                    if ($VerbosePreference -eq 'Continue' -or $script:LogLevel -eq 'DEBUG') {
                        Write-Host $logMessage -ForegroundColor Gray
                    }
                }
                default {
                    Write-Host $logMessage
                }
            }
        }

        # Write to log file if path is defined
        if ($LogPath) {
            # Check if log rotation is needed
            if ((Test-Path $LogPath -ErrorAction SilentlyContinue) -and
                ((Get-Item $LogPath -ErrorAction SilentlyContinue).Length -gt ($MaxLogSizeMB * 1MB))) {

                # Rotate logs - keep up to 3 backups
                try {
                    if (Test-Path "$LogPath.2.bak" -ErrorAction SilentlyContinue) {
                        Remove-Item "$LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$LogPath.1.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$LogPath.1.bak" "$LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$LogPath.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$LogPath.bak" "$LogPath.1.bak" -Force -ErrorAction SilentlyContinue
                    }

                    Rename-Item $LogPath "$LogPath.bak" -Force -ErrorAction SilentlyContinue
                }
                catch {
                    # If rotation fails, just append to the existing log
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to rotate log files: $errorMsg" -ForegroundColor Yellow
                }
            }

            # Ensure log directory exists
            $logDir = Split-Path -Path $LogPath -Parent
            if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                }
                catch {
                    # If we can't create the directory, just write to console
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to create log directory: $errorMsg" -ForegroundColor Red
                    return
                }
            }

            # Write to log file with error handling
            try {
                Add-Content -Path $LogPath -Value $logMessage -ErrorAction Stop
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Host "Failed to write to log file: $errorMsg" -ForegroundColor Red
            }
        }
    }
    catch {
        # Last resort error handling - just try to output something
        $errorMsg = $_.Exception.Message
        Write-Host "Error in Write-Log function: $errorMsg" -ForegroundColor Red
        Write-Host $Message -ForegroundColor Cyan
    }
}

function Send-SlackMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $true)]
        [string]$MessageText,

        [Parameter(Mandatory = $false)]
        [int]$MaxRetries = 2,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutSec = 30
    )

    # Validate webhook URL
    if (-not $WebhookUrl -or -not $WebhookUrl.StartsWith("https://hooks.slack.com/")) {
        throw "Invalid Slack webhook URL format"
    }

    try {
        # Convert to JSON with proper escaping
        $payload = @{
            text = $MessageText
        } | ConvertTo-Json -ErrorAction Stop -Depth 3 -Compress:$false

        # Add timeout and error handling for network issues
        $params = @{
            Uri = $WebhookUrl
            Method = 'Post'
            Body = $payload
            ContentType = 'application/json'
            TimeoutSec = $TimeoutSec
            ErrorAction = 'Stop'
        }

        # Add proxy support if system is configured to use a proxy
        try {
            $systemProxy = [System.Net.WebRequest]::GetSystemWebProxy()
            if ($systemProxy -and $systemProxy.GetProxy([uri]$WebhookUrl) -ne $WebhookUrl) {
                $proxyUrl = $systemProxy.GetProxy([uri]$WebhookUrl)
                Write-Log "Using system proxy for Slack API request: $proxyUrl" -Level 'DEBUG'
                $params.UseDefaultCredentials = $true
                # Only set the proxy if we have a valid URI
                if ($proxyUrl -and $proxyUrl.AbsoluteUri) {
                    $params.Proxy = $proxyUrl.AbsoluteUri
                    $params.ProxyUseDefaultCredentials = $true
                }
            }
        }
        catch {
            Write-Log "Error detecting proxy settings: $($_.Exception.Message)" -Level 'DEBUG'
            # Continue without proxy settings
        }

        # Send the notification with retry logic for transient errors
        $retryCount = 0
        $success = $false

        do {
            try {
                $response = Invoke-RestMethod @params

                # Slack webhooks return "ok" when successful
                if ($response -ne "ok") {
                    Write-Log "Slack API returned unexpected response: $response" -Level 'WARNING'
                }

                Write-Log "Slack notification sent successfully" -Level 'DEBUG'
                $success = $true
                return $true
            }
            catch [System.Net.WebException] {
                $statusCode = [int]$_.Exception.Response.StatusCode

                # Handle specific HTTP status codes
                switch ($statusCode) {
                    429 {
                        # Rate limiting - wait and retry
                        $retryCount++
                        $waitTime = [Math]::Min(10 * $retryCount, 30)  # Exponential backoff with max 30 seconds
                        Write-Log "Slack API rate limit exceeded. Retrying in $waitTime seconds..." -Level 'WARNING'
                        Start-Sleep -Seconds $waitTime
                    }
                    500 {
                        # Server error - wait and retry
                        $retryCount++
                        $waitTime = [Math]::Min(5 * $retryCount, 15)  # Exponential backoff with max 15 seconds
                        Write-Log "Slack API server error. Retrying in $waitTime seconds..." -Level 'WARNING'
                        Start-Sleep -Seconds $waitTime
                    }
                    default {
                        # Other errors - log and throw
                        Write-Log "Slack API error (HTTP $statusCode): $($_.Exception.Message)" -Level 'ERROR'
                        throw "Failed to send Slack notification: HTTP $statusCode - $($_.Exception.Message)"
                    }
                }
            }
            catch {
                # General error - log and throw
                Write-Log "Error sending Slack notification: $($_.Exception.Message)" -Level 'ERROR'
                throw "Failed to send Slack notification: $($_.Exception.Message)"
            }
        } while (-not $success -and $retryCount -lt $MaxRetries)

        # If we get here, we've exhausted retries
        if (-not $success) {
            Write-Log "Failed to send Slack notification after $MaxRetries retries" -Level 'ERROR'
            throw "Failed to send Slack notification after $MaxRetries retries"
        }

        return $true
    }
    catch {
        Write-Log "Error in Send-SlackMessage: $($_.Exception.Message)" -Level 'ERROR'
        throw "Failed to send Slack notification: $($_.Exception.Message)"
    }
}

function Send-SlackNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$AppName,

        [Parameter(Mandatory = $true)]
        [string]$Hostname,

        [Parameter(Mandatory = $true)]
        [string]$CrashTime,

        [Parameter(Mandatory = $false)]
        [string]$FaultingModule = "Unknown",

        [Parameter(Mandatory = $false)]
        [string]$ExceptionCode = "Unknown",

        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$DumpAvailable = $false,

        [Parameter(Mandatory = $false)]
        [bool]$InitialAlert = $true
    )

    try {
        # Check if URL is accessible (optional validation)
        try {
            $testConnection = Test-NetConnection -ComputerName "hooks.slack.com" -Port 443 -InformationLevel Quiet -ErrorAction SilentlyContinue -WarningAction SilentlyContinue
            if (-not $testConnection) {
                Write-Log "Warning: Cannot connect to Slack API (hooks.slack.com:443)" -Level 'WARNING'
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Could not test connection to Slack: $errorMsg" -Level 'WARNING'
        }

        # Sanitize inputs to prevent JSON formatting issues
        $AppName = $AppName -replace '["\r\n]', ' '
        $Hostname = $Hostname -replace '["\r\n]', ' '
        $CrashTime = $CrashTime -replace '["\r\n]', ' '
        $FaultingModule = $FaultingModule -replace '["\r\n]', ' '
        $ExceptionCode = $ExceptionCode -replace '["\r\n]', ' '
        # For the dump path, only remove quotes and newlines, but keep backslashes
        $DumpPath = $DumpPath -replace '["\r\n]', ' '

        # Prepare message text
        # For Slack, we don't need to escape backslashes when using code formatting with backticks
        # Just use the path as-is
        $formattedPath = $DumpPath

        # Use a single backtick for Slack code formatting
        $backtick = [char]96

        if ($DumpAvailable) {
            $dumpText = "*Dump File:* " + $backtick + $formattedPath + $backtick
        } else {
            $dumpText = "*Dump File:* _Not available_" + "`n" + "Expected path: " + $backtick + $formattedPath + $backtick
        }

        if ($InitialAlert) {
            $title = "$AppName Crash Detected"
            $titleEmoji = ":rotating_light:"
        } else {
            $title = "Dump File Status Update"
            $titleEmoji = ":information_source:"
        }

        # Set emoji based on dump availability
        $statusEmoji = if ($DumpAvailable) { ":white_check_mark:" } else { ":x:" }

        # Create the message with proper line breaks for Slack
        $messageText = "$titleEmoji *$title*

*Host:* $Hostname
*Time:* $CrashTime
*Event Type:* $($details.EventType)
*Description:* $($details.Description)
*Faulting Module:* $FaultingModule
*Exception Code:* $ExceptionCode

$statusEmoji $dumpText"

        # Use the helper function to send the message
        return Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error in Send-SlackNotification: $errorMsg" -Level 'ERROR'
        throw "Failed to send Slack notification: $errorMsg"
    }
}

function Wait-ForDumpFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $false)]
        [int]$DelaySeconds = 3,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMinutes = 5,

        [Parameter(Mandatory = $false)]
        [int]$MinimumSizeKB = 1
    )

    try {
        # Validate parameters
        if ($DelaySeconds -lt 1) {
            Write-Log "DelaySeconds must be at least 1, setting to default of 3" -Level 'WARNING'
            $DelaySeconds = 3
        }

        if ($TimeoutMinutes -lt 1) {
            Write-Log "TimeoutMinutes must be at least 1, setting to default of 5" -Level 'WARNING'
            $TimeoutMinutes = 5
        }

        # Calculate timeout
        $startTime = Get-Date
        $endTime = $startTime.AddMinutes($TimeoutMinutes)
        $minimumSizeBytes = $MinimumSizeKB * 1KB

        Write-Log "Waiting for dump file: $DumpPath (timeout: $TimeoutMinutes minutes)" -Level 'INFO'

        # Wait loop with timeout
        while ((Get-Date) -lt $endTime) {
            # Check if file exists
            if (Test-Path -Path $DumpPath -ErrorAction SilentlyContinue) {
                try {
                    # Check if file is accessible and has content
                    $fileInfo = Get-Item -Path $DumpPath -ErrorAction Stop

                    # Check if file is still being written to
                    $initialSize = $fileInfo.Length
                    Start-Sleep -Seconds 1
                    $fileInfo.Refresh()

                    if ($fileInfo.Length -ge $minimumSizeBytes) {
                        # If file size hasn't changed, it's likely complete
                        if ($fileInfo.Length -eq $initialSize) {
                            $fileSizeKB = [Math]::Round($fileInfo.Length / 1KB, 2)
                            Write-Log "Dump file found and ready: $DumpPath ($fileSizeKB KB)" -Level 'INFO'
                            return $true
                        }
                        else {
                            Write-Log "Dump file found but still being written to: $DumpPath" -Level 'INFO'
                        }
                    }
                    else {
                        Write-Log "Dump file found but size is too small: $($fileInfo.Length) bytes" -Level 'INFO'
                    }
                }
                catch {
                    Write-Log "Error accessing dump file: $_" -Level 'WARNING'
                }
            }

            # Calculate and display remaining time periodically
            $elapsed = (Get-Date) - $startTime
            $remaining = $TimeoutMinutes * 60 - $elapsed.TotalSeconds

            if ($remaining -le 0) {
                break
            }

            if ($elapsed.TotalSeconds % 30 -lt $DelaySeconds) {
                Write-Log "Still waiting for dump file: $DumpPath ($('{0:N0}' -f $remaining) seconds remaining)" -Level 'INFO'
            }

            Start-Sleep -Seconds $DelaySeconds
        }

        Write-Log "Timeout waiting for dump file after $TimeoutMinutes minutes." -Level 'WARNING'
        return $false
    }
    catch {
        Write-Log "Error in Wait-ForDumpFile: $_" -Level 'ERROR'
        return $false
    }
}

function Get-CrashEvents {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [DateTime]$StartTime,

        [Parameter(Mandatory = $false)]
        [string]$LogName = 'Application',

        [Parameter(Mandatory = $false)]
        [int[]]$EventIDs = @(1000, 1001, 7031, 7034, 2004, 6008),

        [Parameter(Mandatory = $false)]
        [string[]]$AppNames = @(),

        [Parameter(Mandatory = $false)]
        [string]$ProviderName = '*'
    )

    try {
        Write-Log "Searching for crash events since $($StartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        Write-Log "Monitoring event IDs: $($EventIDs -join ', ')" -Level 'DEBUG'

        if ($AppNames.Count -gt 0) {
            Write-Log "Filtering for applications: $($AppNames -join ', ')" -Level 'DEBUG'
        }

        # Build filter hashtable
        $filterHash = @{
            LogName = $LogName
            ID = $EventIDs
            StartTime = $StartTime
        }

        # Add provider name if specified
        if ($ProviderName -ne '*') {
            $filterHash.ProviderName = $ProviderName
        }

        # Get events with error handling
        $events = Get-WinEvent -FilterHashtable $filterHash -ErrorAction SilentlyContinue -Verbose

        if ($null -eq $events -or $events.Count -eq 0) {
            Write-Log "No crash events found" -Level 'INFO'
            return $null
        }

        # Filter by application names if specified
        if ($AppNames.Count -gt 0) {
            $filteredEvents = @()

            foreach ($eventItem in $events) {
                $message = $eventItem.Message
                $matchFound = $false

                foreach ($appName in $AppNames) {
                    if ($message -match [regex]::Escape($appName)) {
                        $matchFound = $true
                        break
                    }
                }

                if ($matchFound) {
                    $filteredEvents += $eventItem
                }
            }

            $events = $filteredEvents

            if ($events.Count -eq 0) {
                Write-Log "No events found matching the specified applications" -Level 'INFO'
                return $null
            }
        }

        # Log event ID distribution
        $eventCounts = $events | Group-Object -Property Id | Select-Object Name, Count
        foreach ($eventType in $eventCounts) {
            Write-Log "Found $($eventType.Count) events with ID $($eventType.Name)" -Level 'INFO'
        }

        Write-Log "Found $($events.Count) total potential crash/error events" -Level 'INFO'
        return $events
    }
    catch [System.InvalidOperationException] {
        # This exception is thrown when no events match the filter
        Write-Log "No events found matching the criteria" -Level 'INFO'
        return $null
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error retrieving crash events: $errorMsg" -Level 'ERROR'

        # Try an alternative approach if the primary method fails
        try {
            Write-Log "Attempting alternative event retrieval method" -Level 'INFO'

            $allEvents = @()
            foreach ($eventId in $EventIDs) {
                $events = Get-EventLog -LogName $LogName -After $StartTime -EntryType Error,Warning -ErrorAction SilentlyContinue |
                          Where-Object { $_.EventID -eq $eventId }

                if ($null -ne $events -and $events.Count -gt 0) {
                    $allEvents += $events
                }
            }

            # Filter by application names if specified
            if ($AppNames.Count -gt 0 -and $allEvents.Count -gt 0) {
                $filteredEvents = @()

                foreach ($eventItem in $allEvents) {
                    $message = $eventItem.Message
                    $matchFound = $false

                    foreach ($appName in $AppNames) {
                        if ($message -match [regex]::Escape($appName)) {
                            $matchFound = $true
                            break
                        }
                    }

                    if ($matchFound) {
                        $filteredEvents += $eventItem
                    }
                }

                $allEvents = $filteredEvents
            }

            if ($allEvents.Count -gt 0) {
                Write-Log "Found $($allEvents.Count) events using alternative method" -Level 'INFO'
                return $allEvents
            }
            else {
                Write-Log "No events found using alternative method" -Level 'INFO'
                return $null
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Alternative event retrieval also failed: $errorMsg" -Level 'ERROR'
            return $null
        }
    }
}

function Format-EventDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Diagnostics.Eventing.Reader.EventLogRecord]$LogEvent
    )

    try {
        # Extract message safely
        $message = $LogEvent.Message
        if ([string]::IsNullOrEmpty($message)) {
            Write-Log "Event message is empty or null" -Level 'WARNING'
            $message = ""
        }

        # Initialize properties
        $props = @{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = $LogEvent.TimeCreated.ToString("yyyy-MM-dd HH:mm:ss")
            Computer = $env:COMPUTERNAME
            FaultingApp = $null
            FaultingModule = "Unknown"
            ExceptionCode = "Unknown"
            ProcessIdDecimal = $null
            EventType = "Unknown"
            Description = ""
        }

        # Process based on event ID
        switch ($LogEvent.Id) {
            1000 {
                # Application crash
                $props.EventType = "Application Crash"

                # Extract details using regex with error handling
                try {
                    if ($message -match "Faulting application name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }

                    if ($message -match "Faulting module name: ([^,]+)") {
                        $props.FaultingModule = $Matches[1].Trim()
                    }

                    if ($message -match "Exception code: (0x[0-9a-fA-F]+)") {
                        $props.ExceptionCode = $Matches[1].Trim()
                    }

                    if ($message -match "Faulting process id: (0x[0-9a-fA-F]+)") {
                        $hexId = $Matches[1].Trim()
                        try {
                            $props.ProcessIdDecimal = [Convert]::ToInt32($hexId, 16)
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to convert process ID '$hexId' to decimal: $errorMsg" -Level 'WARNING'
                        }
                    }

                    $props.Description = "Application crash detected"
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 1000 message: $errorMsg" -Level 'ERROR'
                }
            }

            1001 {
                # Windows Error Reporting
                $props.EventType = "Error Report"

                try {
                    # Try to extract the application name from WER message
                    if ($message -match "Fault bucket [^,]+, type [^,]+, process name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }
                    elseif ($message -match "process: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }

                    # Extract any additional details
                    if ($message -match "Fault bucket ([^,]+)") {
                        $props.ExceptionCode = $Matches[1].Trim()
                    }

                    $props.Description = "Windows Error Reporting details"
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 1001 message: $errorMsg" -Level 'ERROR'
                }
            }

            7031 {
                # Service crashed and was recovered
                $props.EventType = "Service Recovery"

                try {
                    # Extract service name
                    if ($message -match "The ([^(]+) service terminated unexpectedly") {
                        $serviceName = $Matches[1].Trim()
                        $props.FaultingApp = $serviceName
                        $props.Description = "Service terminated unexpectedly and was restarted"
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 7031 message: $errorMsg" -Level 'ERROR'
                }
            }

            7034 {
                # Service terminated unexpectedly
                $props.EventType = "Service Termination"

                try {
                    # Extract service name
                    if ($message -match "The ([^(]+) service terminated unexpectedly") {
                        $serviceName = $Matches[1].Trim()
                        $props.FaultingApp = $serviceName
                        $props.Description = "Service terminated unexpectedly"
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 7034 message: $errorMsg" -Level 'ERROR'
                }
            }

            2004 {
                # Memory resource notification
                $props.EventType = "Low Memory"
                $props.Description = "System is running low on memory"

                # We don't have a specific app for this, but it affects all running apps
                $props.FaultingApp = "System"
            }

            6008 {
                # Unexpected shutdown
                $props.EventType = "System Shutdown"
                $props.Description = "The system was unexpectedly shut down"
                $props.FaultingApp = "System"
            }

            default {
                # Unknown event type
                $props.EventType = "Unknown Event"
                $props.Description = "Unrecognized event type"

                # Try to extract any app name from the message
                if ($message -match "application name: ([^,]+)") {
                    $props.FaultingApp = $Matches[1].Trim()
                }
                elseif ($message -match "process name: ([^,]+)") {
                    $props.FaultingApp = $Matches[1].Trim()
                }
                elseif ($message -match "service ([^,]+)") {
                    $props.FaultingApp = $Matches[1].Trim()
                }
            }
        }

        # Log the extracted details
        $detailsLog = "Event details - ID: $($props.EventID), Type: $($props.EventType), App: $($props.FaultingApp), Module: $($props.FaultingModule), Exception: $($props.ExceptionCode), PID: $($props.ProcessIdDecimal)"
        Write-Log $detailsLog -Level 'INFO'

        return [PSCustomObject]$props
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error formatting event details: $errorMsg" -Level 'ERROR'

        # Return a minimal object to prevent null reference exceptions
        return [PSCustomObject]@{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            Computer = $env:COMPUTERNAME
            FaultingApp = $null
            FaultingModule = "Unknown"
            ExceptionCode = "Unknown"
            ProcessIdDecimal = $null
            EventType = "Unknown"
            Description = "Error processing event details"
        }
    }
}

function Get-LastRunTime {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath,

        [Parameter(Mandatory = $true)]
        [int]$DefaultLookbackMinutes
    )

    try {
        # Check if state file exists and is readable
        if (Test-Path -Path $StateFilePath -ErrorAction SilentlyContinue) {
            try {
                $content = Get-Content $StateFilePath -Raw -ErrorAction Stop

                if (-not [string]::IsNullOrWhiteSpace($content)) {
                    $lastRunTime = [DateTime]::Parse($content)

                    # Validate the date is reasonable (not in the future, not too far in the past)
                    $now = Get-Date

                    if ($lastRunTime -gt $now) {
                        Write-Log "Last run time from state file is in the future, using default lookback" -Level 'WARNING'
                        return $now.AddMinutes(-$DefaultLookbackMinutes)
                    }

                    # If the last run time is more than 7 days ago, use the default lookback
                    if ($now.Subtract($lastRunTime).TotalDays -gt 7) {
                        Write-Log "Last run time from state file is more than 7 days old, using default lookback" -Level 'WARNING'
                        return $now.AddMinutes(-$DefaultLookbackMinutes)
                    }

                    Write-Log "Using last run time from state file: $($lastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
                    return $lastRunTime
                }
            }
            catch {
                Write-Log "Error parsing last run time from state file: $_" -Level 'WARNING'
            }
        }

        # If we get here, either the file doesn't exist or we couldn't parse the date
        $defaultTime = (Get-Date).AddMinutes(-$DefaultLookbackMinutes)
        Write-Log "Using default lookback time: $($defaultTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        return $defaultTime
    }
    catch {
        Write-Log "Error in Get-LastRunTime: $_" -Level 'ERROR'
        return (Get-Date).AddMinutes(-$DefaultLookbackMinutes)
    }
}

function Save-LastRunTime {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath,

        [Parameter(Mandatory = $true)]
        [DateTime]$LastRunTime
    )

    try {
        # Ensure directory exists
        $stateDir = Split-Path -Path $StateFilePath -Parent
        if (-not (Test-Path -Path $stateDir -ErrorAction SilentlyContinue)) {
            New-Item -Path $stateDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
            Write-Log "Created state file directory: $stateDir" -Level 'INFO'
        }

        # Create a temporary file first, then move it to avoid partial writes
        $tempFile = [System.IO.Path]::GetTempFileName()
        $LastRunTime.ToString('o') | Out-File -FilePath $tempFile -Force -ErrorAction Stop

        # Move the temp file to the final location
        Move-Item -Path $tempFile -Destination $StateFilePath -Force -ErrorAction Stop

        Write-Log "Saved last run time: $($LastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        return $true
    }
    catch {
        Write-Log "Error saving last run time: $_" -Level 'ERROR'
        return $false
    }
}

#endregion

#region Main

# Main script execution
try {
    # Script start time for execution tracking
    $scriptStartTime = Get-Date
    $script:scriptVersion = "4.0"
    $script:LogLevel = $LogLevel  # Store the log level as a script variable

    # Display script banner
    Write-Log "CVDemoCrashAlert v$script:scriptVersion - CompleteView Services Crash Monitor" -Level 'INFO'
    Write-Log "=======================================================================" -Level 'INFO'
    Write-Log "Log level set to: $script:LogLevel" -Level 'INFO'

    # Determine operating mode
    if ($RunAsService) {
        Write-Log "Running in SERVICE mode (real-time monitoring)" -Level 'INFO'
    }
    elseif ($LegacyMode) {
        Write-Log "Running in LEGACY mode (polling)" -Level 'INFO'
    }
    else {
        Write-Log "Running in REAL-TIME mode" -Level 'INFO'
    }

    # Test mode banner
    if ($TestMode) {
        Write-Log "RUNNING IN TEST MODE - No state will be saved" -Level 'WARNING'
    }

    # Initialize environment and get configuration
    $config = Initialize-Environment
    $LogPath = $config.LogPath
    $StateFilePath = $config.StateFilePath
    $serviceConfigs = $config.ServiceConfigs

    # Check if any services are being monitored
    if ($serviceConfigs.Count -eq 0) {
        Write-Log "No services selected for monitoring. Please enable at least one service." -Level 'WARNING'
        exit 0
    }

    # Log which services are being monitored
    $monitoredServices = $serviceConfigs.Keys -join ", "
    Write-Log "Monitoring the following services: $monitoredServices" -Level 'INFO'
    Write-Log "Log Path: $LogPath" -Level 'INFO'
    Write-Log "State File: $StateFilePath" -Level 'INFO'

    # Check if Slack notifications are disabled
    if ($NoSlack) {
        Write-Log "Slack notifications are disabled" -Level 'WARNING'
    }
    elseif ([string]::IsNullOrEmpty($SlackWebhookUrl)) {
        Write-Log "Slack webhook URL is not configured, notifications will be disabled" -Level 'WARNING'
        $NoSlack = $true
    }

    # Special handling for service installation/uninstallation
    if ($RunAsService -and $PSBoundParameters.ContainsKey('Install')) {
        $result = Install-ServiceTask
        if ($result) {
            Write-Log "Service installation completed successfully. The service will start automatically at next system boot." -Level 'INFO'
            Write-Log "To start the service immediately, run: Start-ScheduledTask -TaskName 'CVDemoCrashAlert_Monitor'" -Level 'INFO'
        }
        else {
            Write-Log "Service installation failed." -Level 'ERROR'
        }
        return
    }
    elseif ($RunAsService -and $PSBoundParameters.ContainsKey('Uninstall')) {
        $result = Uninstall-ServiceTask
        if ($result) {
            Write-Log "Service uninstallation completed successfully." -Level 'INFO'
        }
        else {
            Write-Log "Service uninstallation failed or service was not installed." -Level 'WARNING'
        }
        return
    }

    # Perform health monitoring checks
    Write-Log "Starting health monitoring checks" -Level 'INFO'

    # Check service status
    Test-ServiceStatus -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Check resource usage
    Measure-ResourceUsage -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Check disk space
    Test-DiskSpace -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Check database connection (only for Management Server)
    if ($MonitorManagementServer) {
        Test-DatabaseConnection -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack
    }

    # Analyze log files
    Search-LogFiles -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack -LookbackMinutes $LookbackMinutes

    # Branch based on operating mode
    if ($LegacyMode) {
        # Legacy polling mode - continue with the original flow

        # Get last run time and crash events
        $lastRunTime = Get-LastRunTime -StateFilePath $StateFilePath -DefaultLookbackMinutes $LookbackMinutes
        Write-Log "Looking for events since $($lastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'

        # If in test mode, we can simulate a crash event for each service
        if ($TestMode) {
            Write-Log "Test mode: Simulating crash events for monitored services" -Level 'INFO'

            # Process each service in test mode
            foreach ($serviceName in $serviceConfigs.Keys) {
                $serviceConfig = $serviceConfigs[$serviceName]
                $appName = $serviceConfig.AppName
                $dumpFolder = $serviceConfig.DumpPath
                $friendlyName = $serviceConfig.FriendlyName

                Write-Log "Test mode: Simulating crash event for $friendlyName ($appName)" -Level 'INFO'

                # Create a simulated event details object
                $simulatedDetails = [PSCustomObject]@{
                    EventID = 1000
                    Source = "Application Error"
                    Time = (Get-Date).AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss")
                    Computer = $env:COMPUTERNAME
                    FaultingApp = $appName
                    FaultingModule = "unknown.dll"
                    ExceptionCode = "0xc0000005"
                    ProcessIdDecimal = 4660  # 0x1234 in decimal
                }

                # Process the simulated event
                try {
                    $hostname = $env:COMPUTERNAME
                    $time = $simulatedDetails.Time
                    $faultingModule = $simulatedDetails.FaultingModule
                    $exceptionCode = $simulatedDetails.ExceptionCode

                    # Create dump file name
                    $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $simulatedDetails.ProcessIdDecimal
                    $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile

                    # In test mode, we'll say the dump doesn't exist initially
                    $dumpExistsInitially = $false

                    # Send initial notification if Slack is enabled
                    if (-not $NoSlack) {
                        Write-Log "Sending initial Slack notification for simulated crash of $friendlyName" -Level 'INFO'
                        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                        Write-Log "Sent initial Slack notification for simulated crash at $time" -Level 'INFO'
                    }
                    else {
                        Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
                    }

                    # Simulate waiting for dump file
                    Write-Log "Simulating wait for dump file..." -Level 'INFO'
                    Start-Sleep -Seconds 2

                    # In test mode, we'll say the dump becomes available
                    $dumpAvailable = $true

                    # Send follow-up notification if Slack is enabled
                    if (-not $NoSlack) {
                        Write-Log "Sending follow-up Slack notification for simulated crash" -Level 'INFO'
                        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false
                        Write-Log "Sent second Slack notification for simulated dump availability at $time" -Level 'INFO'
                    }
                    else {
                        Write-Log "Dump file is now available at: $fullDumpPath (simulated)" -Level 'INFO'
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error processing simulated event for ${friendlyName}: $errorMsg" -Level 'ERROR'
                }
            }

            # Skip the normal event processing
            $events = $null
        }
        else {
            # Get real events
            $appNames = $serviceConfigs.Values | ForEach-Object { $_.AppName }
            $events = Get-CrashEvents -StartTime $lastRunTime -AppNames $appNames
        }

        if ($null -eq $events -or $events.Count -eq 0) {
            Write-Log "No events found in the specified time range" -Level 'INFO'
        }
        else {
            Write-Log "Found $($events.Count) event(s) to process" -Level 'INFO'

            foreach ($eventItem in $events) {
                try {
                    $details = Format-EventDetails -LogEvent $eventItem

                    # Skip if we couldn't extract the faulting app
                    if (-not $details.FaultingApp) {
                        Write-Log "Skipping event - could not determine faulting application" -Level 'WARNING'
                        continue
                    }

                    # Check if the faulting app matches any of our monitored services
                    $matchedService = $null

                    foreach ($serviceName in $serviceConfigs.Keys) {
                        $serviceConfig = $serviceConfigs[$serviceName]
                        if ($details.FaultingApp -eq $serviceConfig.AppName) {
                            $matchedService = $serviceConfig
                            break
                        }
                    }

                    # If we found a matching service, process the event
                    if ($matchedService) {
                        $appName = $matchedService.AppName
                        $friendlyName = $matchedService.FriendlyName
                        $dumpFolder = $matchedService.DumpPath

                        Write-Log "Processing crash event for $friendlyName ($appName)" -Level 'INFO'

                        $hostname = $env:COMPUTERNAME
                        $time = $details.Time
                        $faultingModule = if ($null -eq $details.FaultingModule) { "Unknown" } else { $details.FaultingModule }
                        $exceptionCode = if ($null -eq $details.ExceptionCode) { "Unknown" } else { $details.ExceptionCode }

                        # Ensure dump directory exists
                        if (-not (Test-Path -Path $dumpFolder -ErrorAction SilentlyContinue)) {
                            try {
                                New-Item -Path $dumpFolder -ItemType Directory -Force -ErrorAction Stop | Out-Null
                                Write-Log "Created dump directory for $friendlyName - $dumpFolder" -Level 'INFO'
                            }
                            catch {
                                $errorMsg = $_.Exception.Message
                                Write-Log "Failed to create dump directory for ${friendlyName}: $errorMsg" -Level 'ERROR'
                            }
                        }

                        # Ensure we have a valid process ID
                        if (-not $details.ProcessIdDecimal) {
                            Write-Log "Could not determine process ID from event, using timestamp instead" -Level 'WARNING'
                            $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), (Get-Date).ToString("yyyyMMddHHmmss")
                        }
                        else {
                            $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $details.ProcessIdDecimal
                        }

                        $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile

                        # Check if dump file exists
                        $dumpExistsInitially = Test-Path -Path $fullDumpPath

                        # Send initial notification if Slack is enabled
                        if (-not $NoSlack) {
                            try {
                                Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                                Write-Log "Sent initial Slack notification for $friendlyName crash at $time" -Level 'INFO'
                            }
                            catch {
                                $errorMsg = $_.Exception.Message
                                Write-Log "Failed to send initial Slack notification for ${friendlyName} after retries: $errorMsg" -Level 'ERROR'
                                # Continue execution - we'll try again with the follow-up notification
                            }
                        }
                        else {
                            Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
                        }

                        # If dump doesn't exist initially, wait for it
                        if (-not $dumpExistsInitially) {
                            $dumpAvailable = Wait-ForDumpFile -DumpPath $fullDumpPath -TimeoutMinutes $DumpWaitTimeoutMinutes

                            # Send follow-up notification if Slack is enabled
                            if (-not $NoSlack) {
                                try {
                                    Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false

                                    if ($dumpAvailable) {
                                        Write-Log "Sent second Slack notification for $friendlyName dump availability at $time" -Level 'INFO'
                                    }
                                    else {
                                        Write-Log "Sent notification that $friendlyName dump did not appear after timeout at $time" -Level 'WARNING'
                                    }
                                }
                                catch {
                                    $errorMsg = $_.Exception.Message
                                    Write-Log "Failed to send follow-up Slack notification for ${friendlyName} after retries: $errorMsg" -Level 'ERROR'
                                }
                            }
                            else {
                                if ($dumpAvailable) {
                                    Write-Log "Dump file for $friendlyName is now available at: $fullDumpPath" -Level 'INFO'
                                }
                                else {
                                    Write-Log "Dump file for $friendlyName did not appear after waiting $DumpWaitTimeoutMinutes minutes" -Level 'WARNING'
                                }
                            }
                        }
                    }
                    else {
                        # This crash is not for one of our monitored services
                        Write-Log "Skipping event - faulting application '$($details.FaultingApp)' is not in the monitored services list" -Level 'DEBUG'
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error processing event: $errorMsg" -Level 'ERROR'
                    # Continue with next event even if this one fails
                    continue
                }
            }
        }

        # Save the last run time (unless in test mode)
        if (-not $TestMode) {
            try {
                Save-LastRunTime -StateFilePath $StateFilePath -LastRunTime (Get-Date)
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Failed to save last run time: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Test mode: Skipping save of last run time" -Level 'INFO'
        }
    }

    # Branch based on operating mode
    if ($LegacyMode) {
        # Legacy polling mode - continue with the original flow

        # Calculate and log execution time
        $executionTime = (Get-Date) - $scriptStartTime
        Write-Log "Legacy mode crash monitoring completed in $($executionTime.TotalSeconds.ToString('0.00')) seconds" -Level 'INFO'
    }
    else {
        # Real-time monitoring mode
        Write-Log "Starting real-time event monitoring" -Level 'INFO'

        # Start the event monitoring loop
        if ($RunAsService) {
            # Service mode with heartbeat and watchdog
            Start-EventMonitoring -ServiceConfigs $serviceConfigs -HeartbeatIntervalMinutes $HeartbeatIntervalMinutes -RunAsService
        }
        else {
            # Interactive mode
            Start-EventMonitoring -ServiceConfigs $serviceConfigs
        }

        # Calculate and log execution time when monitoring ends
        $executionTime = (Get-Date) - $scriptStartTime
        Write-Log "Real-time monitoring ended after $($executionTime.TotalSeconds.ToString('0.00')) seconds" -Level 'INFO'
    }
}
catch {
    # Global error handler
    $errorMsg = $_.Exception.Message
    Write-Log "Critical error in script execution: $errorMsg" -Level 'ERROR'

    # Try to send an alert about the script failure if possible
    if (-not $NoSlack -and -not [string]::IsNullOrEmpty($SlackWebhookUrl)) {
        try {
            $errorMessage = $_.Exception.Message
            $errorDetails = if ($_.Exception.StackTrace) { $_.Exception.StackTrace } else { "No stack trace available" }

            $messageText = ":x: *CVDemoCrashAlert Script Error*

*Host:* $env:COMPUTERNAME
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
*Error:* $errorMessage

*Details:*
```
$errorDetails
```"

            # Use our helper function to send the message
            Send-SlackMessage -WebhookUrl $SlackWebhookUrl -MessageText $messageText | Out-Null
            Write-Log "Sent error notification to Slack" -Level 'INFO'
        }
        catch {
            $slackErrorMsg = $_.Exception.Message
            Write-Log "Failed to send error notification to Slack: $slackErrorMsg" -Level 'ERROR'
        }
    }

    # Clean up any event subscriptions if we're in real-time mode
    if (-not $LegacyMode) {
        try {
            Unregister-EventSubscriptions
            Write-Log "Cleaned up event subscriptions" -Level 'INFO'
        }
        catch {
            # Ignore errors during cleanup
        }
    }

    # Exit with non-zero code to indicate failure
    if (-not $TestMode) {
        exit 1
    }
}
finally {
    # This block always executes, even if there are errors
    $executionTime = (Get-Date) - $scriptStartTime
    $executionTimeStr = [math]::Round($executionTime.TotalSeconds, 2)
    Write-Host "Script execution finished in $executionTimeStr seconds"
}

#endregion
