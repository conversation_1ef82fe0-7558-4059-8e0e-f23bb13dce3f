# AutoCertLE.psm1

# Script-level variables
$script:logFile = Join-Path -Path $PSScriptRoot -ChildPath "AutoCertLE.log"
$script:RevokedCertsFile = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Posh-ACME\revoked_certs.json"

# Import all private functions
$privateFunctions = Get-ChildItem -Path "$PSScriptRoot\Private\*.ps1" -ErrorAction SilentlyContinue
foreach ($function in $privateFunctions) {
    try {
        . $function.FullName
        Write-Verbose "Imported private function: $($function.BaseName)"
    } catch {
        Write-Error "Failed to import private function $($function.FullName): $_"
    }
}

# Import all public functions
$publicFunctions = Get-ChildItem -Path "$PSScriptRoot\Public\*.ps1" -ErrorAction SilentlyContinue
foreach ($function in $publicFunctions) {
    try {
        . $function.FullName
        Write-Verbose "Imported public function: $($function.BaseName)"
    } catch {
        Write-Error "Failed to import public function $($function.FullName): $_"
    }
}

# Initialize the module
function Initialize-Module {
    # Ensure the ACME server is set
    if (Get-Command Get-PAServer -ErrorAction SilentlyContinue) {
        if (-not (Get-PAServer)) {
            Set-PAServer LE_PROD
        }
    } else {
        Write-Warning "Posh-ACME module not found. Some functions may not work correctly."
    }
}

# Export public functions
Export-ModuleMember -Function $publicFunctions.BaseName

# Initialize the module when imported
Initialize-Module
