# Function to load and parse the public suffix list
function Get-PublicSuffixList {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$Url = "https://publicsuffix.org/list/public_suffix_list.dat"
    )
    $cacheDir = "$env:LOCALAPPDATA\PoshACME"
    $cachePath = "$cacheDir\public_suffix_list.dat"

    # Ensure the cache directory exists
    if (-not (Test-Path $cacheDir)) {
        New-Item -ItemType Directory -Path $cacheDir -Force | Out-Null
    }

    if (-not (Test-Path $cachePath -PathType Leaf) -or ((Get-Date) - (Get-Item $cachePath).LastWriteTime).TotalDays -gt 7) {
        Write-ProgressHelper -Activity "Updating Public Suffix List" -Status "Downloading latest list..."
        try {
            $wc = New-Object System.Net.WebClient
            $wc.DownloadProgressChanged = {
                param($send, $e)
                Write-ProgressHelper -Activity "Downloading Public Suffix List" `
                    -Status "Downloaded: $([math]::Round($e.BytesReceived/1KB, 2)) KB" `
                    -PercentComplete $e.ProgressPercentage
            }
            $wc.DownloadFileTaskAsync($Url, $cachePath).Wait()
        } catch {
            Write-Error "Failed to download public suffix list: $($_)"
            Write-Log "Failed to download public suffix list: $($_)" -Level 'Error'
            return @()
        }
    }

    try {
        $suffixes = Get-Content -Path $cachePath | Where-Object {
            $_ -and -not $_.StartsWith("//")
        }
        return $suffixes
    } catch {
        Write-Error "Failed to load public suffix list: $($_)"
        Write-Log "Failed to load public suffix list: $($_)" -Level 'Error'
        return @()
    }
}

# Function to extract the base domain using the public suffix list
function Get-BaseDomain {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$domainName,
        
        [Parameter(Mandatory = $true)]
        [string[]]$Suffixes
    )
    if ([string]::IsNullOrWhiteSpace($domainName)) {
        Write-Warning "Domain name is empty."
        return $null
    }
    if ($null -eq $Suffixes -or $Suffixes.Count -eq 0) {
        Write-Warning "Suffixes list is empty."
        return $domainName
    }
    $domainLabels = $domainName.ToLower().Split('.')
    for ($i = 0; $i -lt $domainLabels.Length; $i++) {
        $candidate = ($domainLabels[$i..($domainLabels.Length - 1)] -join '.')
        if ($Suffixes -contains $candidate) {
            if ($i -gt 0) {
                $registeredDomain = ($domainLabels[($i - 1)..($domainLabels.Length - 1)] -join '.')
                return $registeredDomain
            } else {
                return $domainName
            }
        }
    }
    return $domainName
}
