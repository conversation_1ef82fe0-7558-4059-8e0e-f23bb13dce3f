<#
.SYNOPSIS
    Registers a new TLS certificate using the specified plugin.

.DESCRIPTION
    Handles the registration of a new TLS certificate, including domain validation
    and plugin configuration.

.PARAMETER Force
    Forces the registration even if a certificate already exists.

.EXAMPLE
    Register-Certificate

.EXAMPLE
    Register-Certificate -Force
#>
function Register-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
    param (
        [Parameter()]
        [switch]$Force
    )

    # Ensure the ACME server is set
    Initialize-ACMEServer

    # Load the public suffix list
    $publicSuffixes = Get-PublicSuffixList

    # Prompt for domain name
    $domain = Read-Host "`nEnter the domain name (e.g., server.domain.com) or 0 to go back"
    if ($domain -eq '0') {
        return
    }

    # Validate domain name format
    if (-not (Test-ValidDomain -Domain $domain)) {
        return
    }

    # Extract base domain using public suffix list
    $baseDomain = Get-BaseDomain -domainName $domain -Suffixes $publicSuffixes

    # Initialize variables
    $mainDomain = $domain
    $domains = @()

    # Ask if the user wants a server-specific certificate or a wildcard certificate
    while ($true) {
        Write-Host "`nSelect the type of certificate you want to create:"
        Write-Host "1) Server-specific certificate for $domain"
        Write-Host "2) Wildcard certificate for *.$baseDomain"
        Write-Host "0) Back"
        $certTypeChoice = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice (0-2)" -ValidOptions 1,2
        if ($certTypeChoice -eq 0) {
            return
        } elseif ($certTypeChoice -eq 1) {
            $mainDomain = $domain
            $domains = @($mainDomain)
            break
        } elseif ($certTypeChoice -eq 2) {
            $mainDomain = "*.$baseDomain"
            $domains = @($mainDomain)
            break
        }
    }

    # Attempt to auto-detect DNS provider using enhanced detection
    Write-Host "`nDetecting DNS provider for $baseDomain..." -ForegroundColor Cyan
    try {
        $dnsInfo = Get-SuggestedDNSPlugin -Domain $baseDomain

        if ($dnsInfo.Confidence -ge 70) {
            $nsProvider = $dnsInfo.DetectedProvider
            $plugin = $dnsInfo.SuggestedPlugin

            Write-Host "`nDetected DNS provider: $nsProvider (Confidence: $($dnsInfo.Confidence)%)" -ForegroundColor Green
            Write-Host "Suggested plugin: $plugin" -ForegroundColor Green

            if ($dnsInfo.NameServers -and $dnsInfo.NameServers.Count -gt 0) {
                Write-Host "Nameservers: $($dnsInfo.NameServers -join ', ')" -ForegroundColor Cyan
            }

            if ($dnsInfo.AdditionalProviders -and $dnsInfo.AdditionalProviders.Count -gt 0) {
                Write-Host "`nAdditional possible providers:" -ForegroundColor Yellow
                foreach ($provider in $dnsInfo.AdditionalProviders) {
                    Write-Host "  - $($provider.Provider) (Confidence: $($provider.Confidence)%)" -ForegroundColor Yellow
                }
            }

            # If we have required parameters, show them
            if ($dnsInfo.RequiredParameters -and $dnsInfo.RequiredParameters.Count -gt 0) {
                Write-Host "`nRequired parameters for $plugin plugin:" -ForegroundColor Cyan
                foreach ($param in $dnsInfo.RequiredParameters) {
                    Write-Host "  - $param" -ForegroundColor Cyan
                }
            }

            # Show notes if available
            if ($dnsInfo.Notes) {
                Write-Host "`nNotes: $($dnsInfo.Notes)" -ForegroundColor Cyan
            }

            # Ask user to confirm the detected provider
            $confirmProvider = Read-Host "`nUse the detected provider '$nsProvider' with plugin '$plugin'? (Y/N)"
            if ($confirmProvider -notmatch '^[Yy]$') {
                Write-Host "Manual provider selection will be used instead." -ForegroundColor Yellow
                $plugin = $null
            } else {
                Write-Log "Detected DNS provider: $nsProvider with plugin: $plugin (Confidence: $($dnsInfo.Confidence)%)"
            }
        } else {
            Write-Warning "`nDNS provider could not be automatically detected with high confidence."
            if ($dnsInfo.DetectedProvider -ne "Manual") {
                Write-Host "Best guess: $($dnsInfo.DetectedProvider) (Confidence: $($dnsInfo.Confidence)%)" -ForegroundColor Yellow
            }
            $plugin = $null
        }
    } catch {
        Write-Warning "`nFailed to detect DNS provider for ${baseDomain}: $($_)"
        Write-Log "Failed to detect DNS provider for ${baseDomain}: $($_)" -Level 'Warning'
        $plugin = $null
    }

    # If DNS provider not detected, prompt user to select the challenge plugin
    if (-not $plugin) {
        $pluginSelected = $false
        while (-not $pluginSelected) {
            Write-Host "`nSelect the challenge plugin:"
            Write-Host "1) DNS - Cloudflare"
            Write-Host "2) DNS - AWS Route53"
            Write-Host "3) DNS - Azure"
            Write-Host "4) Manual (default)"
            Write-Host "5) Other DNS Plugin"
            Write-Host "0) Back"
            $pluginOption = Get-ValidatedInput -Prompt "`nEnter the corresponding number (0-5)" -ValidOptions 1,2,3,4,5
            switch ($pluginOption) {
                0 { return }
                1 { $plugin = 'Cloudflare'; $pluginSelected = $true }
                2 { $plugin = 'Route53'; $pluginSelected = $true }
                3 { $plugin = 'Azure'; $pluginSelected = $true }
                4 { $plugin = 'Manual'; $pluginSelected = $true }
                5 {
                    # List all available DNS plugins
                    $plugins = @(Get-PAPlugin | Where-Object { $_.ChallengeType -eq 'dns-01' })
                    if ($plugins.Count -eq 0) {
                        Write-Warning "`nNo DNS plugins are available."
                        $plugin = 'Manual'
                        $pluginSelected = $true
                    } else {
                        $dnsPluginSelected = $false
                        while (-not $dnsPluginSelected) {
                            Write-Host "`nAvailable DNS Plugins:"
                            $i = 1
                            foreach ($p in $plugins) {
                                Write-Host "$i) $($p.Name)"
                                $i++
                            }
                            Write-Host "0) Back"
                            $pluginSelection = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice" -ValidOptions (1..$plugins.Count)
                            if ($pluginSelection -eq 0) {
                                break
                            } else {
                                $plugin = $plugins[$pluginSelection - 1].Name
                                $pluginSelected = $true
                                $dnsPluginSelected = $true
                            }
                        }
                    }
                }
            }
        }
    }

    # Ensure an ACME account exists
    if (-not (Get-PAAccount)) {
        Write-Host "`nNo ACME account found. Creating a new account..."
        $email = Read-Host "`nEnter your email address for Let's Encrypt notifications or 0 to go back"
        if ($email -eq '0') {
            return
        }
        if (-not (Test-ValidEmail -Email $email)) {
            return
        }
        try {
            New-PAAccount -AcceptTOS -Contact $email -ErrorAction Stop
            Write-Host "`nACME account created."
            Write-Log "ACME account created with email: $email"
        } catch {
            Write-Error "Failed to create ACME account: $($_)"
            Write-Log "Failed to create ACME account: $($_)" -Level 'Error'
            return
        }
    }

    # Initialize plugin arguments
    $pluginArgs = @{}

    # Handle plugin-specific authentication
    if ($plugin -eq 'Manual') {
        Write-Host "`nManual challenge selected. You will need to create DNS TXT records manually."
        $pluginArgs = @{}
    } else {
        # Get plugin parameters
        $pluginParams = Get-DNSPluginParameters -PluginName $plugin

        # Initialize an empty hashtable for plugin arguments
        $pluginArgs = @{}

        # Handle common plugins with special authentication flows
        if ($plugin -eq 'Cloudflare') {
            Write-Host "`nSetting up Cloudflare authentication..." -ForegroundColor Cyan

            # Check for existing credentials
            $cred = Get-SecureCredential -ProviderName 'Cloudflare'
            if ($null -eq $cred) {
                # Provide guidance for creating API token
                Write-Host "`nCloudflare credentials not found. Opening browser for API token creation..."
                Start-Process "https://dash.cloudflare.com/profile/api-tokens"
                Write-Host "`nPlease follow these steps to create an API Token:`n" -ForegroundColor Cyan
                Write-Host "1. Log in to your Cloudflare account."
                Write-Host "2. Navigate to 'My Profile' > 'API Tokens'."
                Write-Host "3. Click 'Create Token'."
                Write-Host "4. Select 'Create Custom Token'."
                Write-Host "5. In the 'Permissions' section, add the following permissions:"
                Write-Host "   - Zone:DNS:Edit"
                Write-Host "6. In the 'Zone Resources' section, select the appropriate option:"
                Write-Host "   - Include: All zones or specify the specific zone."
                Write-Host "7. Give your token a name and click 'Continue to summary'."
                Write-Host "8. Review the details and click 'Create Token'."
                Write-Host "9. Copy the API Token displayed. This will be used in the script."

                $cfToken = Read-Host "`nEnter your Cloudflare API Token or 0 to go back" -AsSecureString
                if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($cfToken)) -eq '0') {
                    return
                }
                $cfCredential = New-Object System.Management.Automation.PSCredential ('CFToken', $cfToken)
                Set-SecureCredential -ProviderName 'Cloudflare' -Credential $cfCredential
            } else {
                $cfToken = $cred.Password
                Write-Host "Using stored Cloudflare credentials." -ForegroundColor Green
            }
            $pluginArgs = @{ CFToken = $cfToken }

            # Ask for Zone ID if needed
            if ($pluginParams.Required -contains 'CFZoneID') {
                $cfZoneID = Read-Host "`nEnter your Cloudflare Zone ID for $baseDomain or 0 to go back"
                if ($cfZoneID -eq '0') {
                    return
                }
                $pluginArgs['CFZoneID'] = $cfZoneID
            }
        } elseif ($plugin -eq 'Route53') {
            Write-Host "`nSetting up AWS Route53 authentication..." -ForegroundColor Cyan

            # Check for AWS CLI profile or ask for credentials
            $awsProfileOption = Read-Host "`nDo you want to use an AWS CLI profile? (Y/N)"
            if ($awsProfileOption -match '^[Yy]$') {
                $awsProfile = Read-Host "`nEnter your AWS profile name (leave blank for default) or 0 to go back"
                if ($awsProfile -eq '0') {
                    return
                }
                if ($awsProfile) {
                    $pluginArgs = @{ ProfileName = $awsProfile }
                } else {
                    $pluginArgs = @{ ProfileName = 'default' }
                }
            } else {
                $awsAccessKey = Read-Host "`nEnter your AWS Access Key ID or 0 to go back"
                if ($awsAccessKey -eq '0') {
                    return
                }

                $awsSecretKey = Read-Host "`nEnter your AWS Secret Access Key or 0 to go back" -AsSecureString
                if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($awsSecretKey)) -eq '0') {
                    return
                }

                $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($awsSecretKey)
                $awsSecretKeyPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

                $pluginArgs = @{
                    R53AccessKey = $awsAccessKey
                    R53SecretKey = $awsSecretKeyPlain
                }

                # Ask for region if needed
                $awsRegion = Read-Host "`nEnter your AWS Region (leave blank for us-east-1) or 0 to go back"
                if ($awsRegion -eq '0') {
                    return
                }
                if ($awsRegion) {
                    $pluginArgs['R53Region'] = $awsRegion
                }
            }
        } elseif ($plugin -eq 'Azure') {
            Write-Host "`nSetting up Azure authentication..." -ForegroundColor Cyan

            # Use interactive authentication for Azure
            if (-not (Get-Module -ListAvailable -Name Az.Accounts)) {
                Write-Host "`nAz.Accounts module not found. Installing..."
                try {
                    Install-Module -Name Az.Accounts -Scope CurrentUser -Force -ErrorAction Stop
                    Import-Module Az.Accounts
                } catch {
                    $msg = "Failed to install Az.Accounts module. NuGet provider status: $($_.Exception.Message)"
                    Write-Error $msg
                    Write-Log $msg -Level 'Error'
                    return
                }
            } else {
                Import-Module Az.Accounts
            }

            try {
                Invoke-WithRetry -ScriptBlock {
                    Connect-AzAccount -UseDeviceAuthentication -ErrorAction Stop
                } -MaxAttempts 3 -InitialDelaySeconds 5 `
                  -OperationName "Azure authentication" `
                  -SuccessCondition { Get-AzContext }

                $azContext = Get-AzContext
                $pluginArgs = @{
                    AZTenantId = $azContext.Tenant.Id
                    AZSubscriptionId = $azContext.Subscription.Id
                }

                # Ask for Resource Group
                $azResourceGroup = Read-Host "`nEnter your Azure Resource Group name or 0 to go back"
                if ($azResourceGroup -eq '0') {
                    return
                }
                $pluginArgs['AZResourceGroup'] = $azResourceGroup

                # For service principal authentication, we would need to ask for client ID and secret
                $useServicePrincipal = Read-Host "`nDo you want to use a Service Principal instead of interactive login? (Y/N)"
                if ($useServicePrincipal -match '^[Yy]$') {
                    $azClientId = Read-Host "`nEnter your Azure Client ID or 0 to go back"
                    if ($azClientId -eq '0') {
                        return
                    }

                    $azClientSecret = Read-Host "`nEnter your Azure Client Secret or 0 to go back" -AsSecureString
                    if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($azClientSecret)) -eq '0') {
                        return
                    }

                    $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($azClientSecret)
                    $azClientSecretPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                    [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

                    $pluginArgs['AZClientId'] = $azClientId
                    $pluginArgs['AZClientSecret'] = $azClientSecretPlain
                }
            } catch {
                Write-Error "Failed to authenticate with Azure after multiple attempts: $($_)"
                Write-Log "Failed to authenticate with Azure after multiple attempts: $($_)" -Level 'Error'
                return
            }
        } else {
            # Handle other plugins
            Write-Host "`nSetting up $plugin plugin..." -ForegroundColor Cyan

            # Prompt to view the plugin's guide
            $viewGuide = Read-Host "`nWould you like to view the $plugin plugin guide? (Y/N)"
            if ($viewGuide -match '^[Yy]$') {
                $guideUrl = "https://poshac.me/docs/v4/Plugins/$plugin/"
                Start-Process $guideUrl
            }

            # Get required parameters from our mapping or from Posh-ACME directly
            $requiredParams = $pluginParams.Required
            if (-not $requiredParams -or $requiredParams.Count -eq 0) {
                # Fallback to getting parameters directly from Posh-ACME
                $pluginInfo = Get-PAPlugin -Plugin $plugin
                $requiredParams = $pluginInfo.Params
            }

            if ($requiredParams -and $requiredParams.Count -gt 0) {
                Write-Host "`nThe $plugin plugin requires the following parameters:" -ForegroundColor Cyan
                foreach ($param in $requiredParams) {
                    Write-Host "  - $param" -ForegroundColor Cyan
                }

                # Loop through each parameter and prompt the user for input
                foreach ($param in $requiredParams) {
                    do {
                        # Check if parameter might contain sensitive information
                        $isSecret = $param -like "*Secret*" -or $param -like "*Password*" -or $param -like "*Key*" -or $param -like "*Token*"

                        if ($isSecret) {
                            $paramValue = Read-Host "`nEnter value for '$param' or 0 to go back" -AsSecureString
                            if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($paramValue)) -eq '0') {
                                return
                            }

                            $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($paramValue)
                            $paramValuePlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                            [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

                            $pluginArgs[$param] = $paramValuePlain
                        } else {
                            $paramValue = Read-Host "`nEnter value for '$param' or 0 to go back"
                            if ($paramValue -eq '0') { return }
                            $pluginArgs[$param] = $paramValue
                        }

                        # Validate parameters if possible
                        if (Get-Command -Name Test-PluginParameters -ErrorAction SilentlyContinue) {
                            if (-not (Test-PluginParameters -Plugin $plugin -Parameters $pluginArgs)) {
                                Write-Warning "Invalid parameter format. Please try again."
                                $pluginArgs.Remove($param)
                                continue
                            }
                        }
                        break
                    } while ($true)
                }

                # Handle optional parameters
                if ($pluginParams.Optional -and $pluginParams.Optional.Count -gt 0) {
                    Write-Host "`nThe $plugin plugin also supports these optional parameters:" -ForegroundColor Cyan
                    foreach ($param in $pluginParams.Optional) {
                        Write-Host "  - $param" -ForegroundColor Cyan
                    }

                    $useOptional = Read-Host "`nDo you want to specify any optional parameters? (Y/N)"
                    if ($useOptional -match '^[Yy]$') {
                        foreach ($param in $pluginParams.Optional) {
                            $specifyParam = Read-Host "`nDo you want to specify '$param'? (Y/N)"
                            if ($specifyParam -match '^[Yy]$') {
                                $isSecret = $param -like "*Secret*" -or $param -like "*Password*" -or $param -like "*Key*" -or $param -like "*Token*"

                                if ($isSecret) {
                                    $paramValue = Read-Host "`nEnter value for '$param' or 0 to skip" -AsSecureString
                                    if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($paramValue)) -eq '0') {
                                        continue
                                    }

                                    $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($paramValue)
                                    $paramValuePlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                                    [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

                                    $pluginArgs[$param] = $paramValuePlain
                                } else {
                                    $paramValue = Read-Host "`nEnter value for '$param' or 0 to skip"
                                    if ($paramValue -eq '0') { continue }
                                    $pluginArgs[$param] = $paramValue
                                }
                            }
                        }
                    }
                }
            } else {
                Write-Host "`nThe $plugin plugin does not require any parameters." -ForegroundColor Green
            }
        }
    }

    # Show a summary of the plugin arguments (masking sensitive values)
    if ($pluginArgs.Count -gt 0) {
        Write-Host "`nPlugin arguments summary:" -ForegroundColor Cyan
        foreach ($key in $pluginArgs.Keys) {
            $value = $pluginArgs[$key]
            # Mask sensitive values
            if ($key -like "*Secret*" -or $key -like "*Password*" -or $key -like "*Key*" -or $key -like "*Token*") {
                $maskedValue = "********"
                Write-Host "  - $key = $maskedValue" -ForegroundColor Cyan
            } else {
                Write-Host "  - $key = $value" -ForegroundColor Cyan
            }
        }
    }

    # Submit the certificate order
    Write-Host "`nRequesting certificate for domain(s): $($domains -join ', ')"
    Write-Log "Requesting certificate for domain(s): $($domains -join ', ')"
    try {
        if ($plugin -eq 'Manual') {
            # Manual challenge handling
            $cert = New-PACertificate -Domain $mainDomain -Plugin $plugin -DnsSleep 0 -Verbose

            Write-Host "`nPlease create the following DNS TXT records:"
            Write-Host "------------------------------------------"
            foreach ($authz in $cert.Authorization) {
                foreach ($challenge in $authz.Challenges) {
                    if ($challenge.Type -eq 'dns-01') {
                        $dnsName = "_acme-challenge." + $authz.Identifier
                        $txtValue = $challenge.DnsDigest
                        Write-Host "$dnsName -> $txtValue"
                    }
                }
            }
            Write-Host "------------------------------------------"

            while ($true) {
                $continue = Read-Host "`nPress Enter when you have created the DNS records and they have propagated, or type '0' to cancel"
                if ($continue -eq '0') {
                    Write-Warning "`nOperation canceled by the user."
                    return
                }

                # Test DNS record propagation with retry
                $allRecordsPresent = $true
                foreach ($authz in $cert.Authorization) {
                    $dnsName = "_acme-challenge." + $authz.Identifier
                    try {
                        $dnsResult = Invoke-WithRetry -ScriptBlock {
                            Resolve-DnsName -Name $dnsName -Type TXT -ErrorAction Stop
                        } -MaxAttempts 10 -InitialDelaySeconds 30 `
                          -OperationName "DNS propagation check for $dnsName" `
                          -SuccessCondition { $_.Strings -and $_.Strings.Count -gt 0 }

                        Write-Host "`nFound DNS TXT record for ${dnsName}: $($dnsResult.Strings)" -ForegroundColor Green
                    } catch {
                        Write-Warning "`nDNS TXT record not found for $dnsName after multiple attempts."
                        $allRecordsPresent = $false
                        break
                    }
                }

                if ($allRecordsPresent) {
                    # Proceed with validation
                    try {
                        $validationResult = Complete-ChallengeWithRetry -AuthChain $cert -MaxAttempts 5 -PropagationWait 30
                        if (-not $validationResult) {
                            Write-Error "Failed to validate domain ownership after multiple attempts"
                            Write-Log "Failed to validate domain ownership after multiple attempts" -Level 'Error'
                            return
                        }

                        # Check the authorization status after challenge completion
                        $validationSuccess = $true
                        foreach ($authz in $cert.Authorization) {
                            $status = Get-PAAuthorization -AuthUrl $authz.location -Verbose
                            if ($status.status -ne 'valid') {
                                $validationSuccess = $false
                                Write-Error "`nAuthorization failed for domain $($authz.Identifier). Status: $($status.status)"
                                Write-Log "Authorization failed for domain $($authz.Identifier). Status: $($status.status)" -Level 'Error'
                                break
                            }
                        }

                        if ($validationSuccess) {
                            Write-Host "`nAll domain validations completed successfully." -ForegroundColor Green
                            Write-Log "All domain validations completed successfully."
                            break
                        } else {
                            $retry = Read-Host "`nWould you like to retry validation? (Y/N)"
                            if ($retry -notmatch '^(Y|y)$') {
                                Write-Warning "`nReturning to the main menu"
                                return
                            }
                        }
                    } catch {
                        Write-Error "`nValidation failed:`n$($_)"
                        Write-Log "Validation failed during manual challenge: $($_)" -Level 'Error'
                        $retry = Read-Host "`nWould you like to retry validation? (Y/N)"
                        if ($retry -notmatch '^(Y|y)$') {
                            Write-Warning "`nReturning to the main menu"
                            return
                        }
                    }
                } else {
                    Write-Warning "`nPlease wait a few minutes for DNS propagation and try again."
                    $wait = Read-Host "`nPress Enter to retry or type '0' to cancel"
                    if ($wait -eq '0') {
                        Write-Warning "`nOperation canceled by the user."
                        return
                    }
                }
            }

            # Wait for certificate issuance and verify the result
            Write-Host "`nWaiting for certificate issuance..."
            try {
                $cert = Submit-OrderFinalization -Order $cert.Order -Verbose

                # Verify the certificate was issued successfully
                if (-not $cert.Certificate) {
                    Write-Error "`nCertificate was not issued. Please check the Let's Encrypt logs for details."
                    Write-Log "Certificate was not issued for $mainDomain" -Level 'Error'
                    Read-Host "`nPress Enter to return to the main menu"
                    return
                }

                # Verify the certificate properties
                $certDetails = Get-PACertificate -MainDomain $mainDomain
                if ($null -eq $certDetails) {
                    Write-Error "`nUnable to retrieve certificate details."
                    Write-Log "Unable to retrieve certificate details for $mainDomain" -Level 'Error'
                    Read-Host "`nPress Enter to return to the main menu"
                    return
                }

                Write-Host "`nCertificate issued successfully:" -ForegroundColor Green
                Write-Host "Subject: $($certDetails.Certificate.Subject)"
                Write-Host "Issuer: $($certDetails.Certificate.Issuer)"
                Write-Host "Valid Until: $($certDetails.Certificate.NotAfter)"
                Write-Log "Certificate issued successfully for $mainDomain, valid until $($certDetails.Certificate.NotAfter)"
            } catch {
                Write-Error "`nFailed to finalize certificate order: $($_)"
                Write-Log "Failed to finalize certificate order for ${mainDomain}: " + $_ -Level 'Error'
                Read-Host "`nPress Enter to return to the main menu"
                return
            }
        } else {
            # Automated challenge handling

            # Added -Force to overwrite existing orders
            $cert = New-PACertificate -Domain $mainDomain -Plugin $plugin -PluginArgs $pluginArgs -Force -Verbose

            # Check if the certificate was obtained successfully
            if (-not $cert.CertFile -and -not $cert.FullChainFile -and -not $cert.PfxFile) {
                Write-Error "`nFailed to obtain the certificate. Please check the output above for errors."
                Write-Log "Failed to obtain the certificate for $mainDomain" -Level 'Error'
                Read-Host "`nPress Enter to return to the main menu"
                return
            }
        }

        # Call the Install-Certificate function to handle installation options
        Install-Certificate -PACertificate $cert

    } catch {
        Write-Error "`nAn error occurred during certificate request: $($_)"
        Write-Log "An error occurred during certificate request: $($_)" -Level 'Error'
        Read-Host "`nPress Enter to return to the main menu"
    }
    Read-Host "`nPress Enter to return to the main menu"
}
