# Function to submit certificate renewal
function Submit-Renewal {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain,
        
        [Parameter()]
        [switch]$Force
    )
    
    try {
        Write-Verbose "Renewing certificate for $MainDomain"
        
        # Get the original order to maintain plugin settings
        $order = Get-PAOrder -MainDomain $MainDomain
        if (-not $order) {
            throw "No existing order found for $MainDomain"
        }
        
        # Use the same plugin and plugin args from the original order
        $plugin = $order.Plugin
        $pluginArgs = $order.PluginArgs
        
        Write-Verbose "Using plugin: $plugin with original configuration"
        
        # Renew the certificate using New-PACertificate with the -Renew parameter
        $cert = New-PACertificate -MainDomain $MainDomain -Plugin $plugin -PluginArgs $pluginArgs -Renew -Force:$Force -Verbose
        
        if ($cert) {
            Write-Verbose "Certificate renewal successful for $MainDomain"
            Write-Log "Certificate renewal successful for $MainDomain" -Level 'Success'
        } else {
            Write-Warning "Certificate renewal returned no certificate for $MainDomain"
            Write-Log "Certificate renewal returned no certificate for $MainDomain" -Level 'Warning'
        }
        
        return $cert
    } catch {
        $errorMessage = "Failed to renew certificate for $MainDomain: $($_)"
        Write-Error $errorMessage
        Write-Log $errorMessage -Level 'Error'
        return $null
    }
}

# Function to check if certificate needs renewal
function Test-CertificateRenewalNeeded {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain,
        
        [Parameter()]
        [int]$RenewalThresholdDays = 30,
        
        [Parameter()]
        [switch]$Force
    )
    
    try {
        if ($Force) {
            Write-Verbose "Force renewal requested for $MainDomain"
            return $true
        }
        
        # Get certificate details
        $cert = Get-CachedPACertificate -MainDomain $MainDomain -Force
        if (-not $cert) {
            Write-Warning "Certificate not found for $MainDomain"
            return $false
        }
        
        # Check if renewal is needed based on expiration date
        $renewalThreshold = (Get-Date).AddDays($RenewalThresholdDays)
        $needsRenewal = $cert.Certificate.NotAfter -lt $renewalThreshold
        
        if ($needsRenewal) {
            Write-Verbose "Certificate for $MainDomain expires on $($cert.Certificate.NotAfter), renewal needed"
        } else {
            Write-Verbose "Certificate for $MainDomain is still valid until $($cert.Certificate.NotAfter), renewal not needed"
        }
        
        return $needsRenewal
    } catch {
        Write-Error "Error checking if certificate needs renewal for ${MainDomain}: $($_)"
        Write-Log "Error checking if certificate needs renewal for ${MainDomain}: $($_)" -Level 'Error'
        return $false
    }
}
