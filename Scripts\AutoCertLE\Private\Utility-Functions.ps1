# Function to confirm actions
function Confirm-Action {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message
    )
    $response = Read-Host "$Message (Y/N)"
    return $response -match '^[Yy]$'
}

# Function for progress reporting
function Write-ProgressHelper {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Activity,
        [Parameter()]
        [string]$Status = "In Progress",
        [Parameter()]
        [int]$PercentComplete,
        [Parameter()]
        [string]$CurrentOperation,
        [Parameter()]
        [int]$StepNumber,
        [Parameter()]
        [int]$TotalSteps
    )
    
    if ($StepNumber -and $TotalSteps) {
        $PercentComplete = ($StepNumber / $TotalSteps) * 100
    }

    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete -CurrentOperation $CurrentOperation
}
