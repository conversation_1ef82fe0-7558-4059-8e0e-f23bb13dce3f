{"pagination": {"GetApis": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetAuthorizers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetDeployments": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetDomainNames": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetIntegrationResponses": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetIntegrations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetModels": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetRouteResponses": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetRoutes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}, "GetStages": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Items"}}}