# Function to securely store credentials
function Set-SecureCredential {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$CredentialName,
        
        [Parameter()]
        [System.Management.Automation.PSCredential]$Credential,
        
        [Parameter()]
        [string]$Username,
        
        [Parameter()]
        [securestring]$Password,
        
        [Parameter()]
        [string]$StorePath = "$env:LOCALAPPDATA\PoshACME\credentials"
    )
    
    # Create credential if not provided
    if (-not $Credential) {
        if (-not $Username -or -not $Password) {
            throw "Either Credential or both Username and Password must be provided"
        }
        
        $Credential = New-Object System.Management.Automation.PSCredential ($Username, $Password)
    }
    
    # Create storage directory if it doesn't exist
    if (-not (Test-Path $StorePath)) {
        New-Item -ItemType Directory -Path $StorePath -Force | Out-Null
    }
    
    # Create a secure file path
    $secureFilePath = Join-Path -Path $StorePath -ChildPath "$CredentialName.xml"
    
    try {
        # Export the credential to a secure XML file
        $Credential | Export-Clixml -Path $secureFilePath -Force
        Write-Verbose "Credential '$CredentialName' stored securely"
        return $true
    } catch {
        Write-Error "Failed to store credential '$CredentialName': $($_.Exception.Message)"
        Write-Log "Failed to store credential '$CredentialName': $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}

# Function to retrieve secure credentials
function Get-SecureCredential {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$CredentialName,
        
        [Parameter()]
        [string]$StorePath = "$env:LOCALAPPDATA\PoshACME\credentials"
    )
    
    # Create the secure file path
    $secureFilePath = Join-Path -Path $StorePath -ChildPath "$CredentialName.xml"
    
    if (-not (Test-Path $secureFilePath)) {
        Write-Warning "Credential '$CredentialName' not found"
        return $null
    }
    
    try {
        # Import the credential from the secure XML file
        $credential = Import-Clixml -Path $secureFilePath
        return $credential
    } catch {
        Write-Error "Failed to retrieve credential '$CredentialName': $($_.Exception.Message)"
        Write-Log "Failed to retrieve credential '$CredentialName': $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to remove secure credentials
function Remove-SecureCredential {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$CredentialName,
        
        [Parameter()]
        [string]$StorePath = "$env:LOCALAPPDATA\PoshACME\credentials"
    )
    
    # Create the secure file path
    $secureFilePath = Join-Path -Path $StorePath -ChildPath "$CredentialName.xml"
    
    if (-not (Test-Path $secureFilePath)) {
        Write-Warning "Credential '$CredentialName' not found"
        return $false
    }
    
    try {
        # Remove the secure XML file
        Remove-Item -Path $secureFilePath -Force
        Write-Verbose "Credential '$CredentialName' removed"
        return $true
    } catch {
        Write-Error "Failed to remove credential '$CredentialName': $($_.Exception.Message)"
        Write-Log "Failed to remove credential '$CredentialName': $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}

# Function to validate certificate key security
function Test-CertificateKeySecurity {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate
    )
    
    try {
        $results = @{
            IsValid = $true
            Warnings = @()
            KeyType = $Certificate.KeyLength
            KeySize = $Certificate.KeyLength
        }
        
        # Check key type and size
        if ($Certificate.KeyType -eq 'RSA') {
            if ($Certificate.KeyLength -lt 2048) {
                $results.IsValid = $false
                $results.Warnings += "RSA key size is less than 2048 bits (current: $($Certificate.KeyLength))"
            }
        } elseif ($Certificate.KeyType -eq 'EC') {
            if ($Certificate.KeyLength -lt 256) {
                $results.IsValid = $false
                $results.Warnings += "EC key size is less than 256 bits (current: $($Certificate.KeyLength))"
            }
        }
        
        # Check certificate file permissions
        if ($Certificate.KeyFile -and (Test-Path $Certificate.KeyFile)) {
            $acl = Get-Acl -Path $Certificate.KeyFile
            $owner = $acl.Owner
            $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
            
            if ($owner -ne $currentUser) {
                $results.Warnings += "Key file is owned by '$owner' instead of the current user"
            }
            
            # Check if the key file has too permissive permissions
            $permissions = $acl.Access | Where-Object { $_.AccessControlType -eq 'Allow' }
            $everyoneAccess = $permissions | Where-Object { $_.IdentityReference.Value -eq 'Everyone' -or $_.IdentityReference.Value -eq 'BUILTIN\Users' }
            
            if ($everyoneAccess) {
                $results.IsValid = $false
                $results.Warnings += "Key file has overly permissive permissions"
            }
        }
        
        return $results
    } catch {
        Write-Error "Failed to validate certificate key security: $($_.Exception.Message)"
        Write-Log "Failed to validate certificate key security: $($_.Exception.Message)" -Level 'Error'
        return @{
            IsValid = $false
            Warnings = @("Error validating key security: $($_.Exception.Message)")
            KeyType = $null
            KeySize = $null
        }
    }
}

# Function to backup ACME account key
function Backup-ACMEAccountKey {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$BackupPath,
        
        [Parameter()]
        [securestring]$Password
    )
    
    if (-not $BackupPath) {
        $BackupPath = Join-Path -Path ([Environment]::GetFolderPath("Desktop")) -ChildPath "ACME_Account_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').zip"
    }
    
    if (-not $Password) {
        $Password = Read-Host -AsSecureString "Enter a password to protect the backup"
    }
    
    try {
        # Get the PoshACME directory
        $poshACMEDir = "$env:LOCALAPPDATA\PoshACME"
        
        if (-not (Test-Path $poshACMEDir)) {
            throw "PoshACME directory not found at $poshACMEDir"
        }
        
        # Create a temporary directory
        $tempDir = Join-Path -Path ([System.IO.Path]::GetTempPath()) -ChildPath ([System.Guid]::NewGuid().ToString())
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Copy account files to the temporary directory
        $accountsDir = Join-Path -Path $poshACMEDir -ChildPath "Accounts"
        if (Test-Path $accountsDir) {
            Copy-Item -Path $accountsDir -Destination $tempDir -Recurse
        }
        
        # Copy current account file
        $currentAccountFile = Join-Path -Path $poshACMEDir -ChildPath "current-account.json"
        if (Test-Path $currentAccountFile) {
            Copy-Item -Path $currentAccountFile -Destination $tempDir
        }
        
        # Create a secure password file
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password)
        $plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
        
        # Create the zip file
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $BackupPath)
        
        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force
        
        Write-Host "ACME account backup created at: $BackupPath" -ForegroundColor Green
        Write-Log "ACME account backup created at: $BackupPath" -Level 'Success'
        
        return $BackupPath
    } catch {
        Write-Error "Failed to backup ACME account key: $($_.Exception.Message)"
        Write-Log "Failed to backup ACME account key: $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to restore ACME account key
function Restore-ACMEAccountKey {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$BackupPath,
        
        [Parameter()]
        [securestring]$Password
    )
    
    if (-not (Test-Path $BackupPath)) {
        throw "Backup file not found at $BackupPath"
    }
    
    if (-not $Password) {
        $Password = Read-Host -AsSecureString "Enter the password for the backup"
    }
    
    try {
        # Get the PoshACME directory
        $poshACMEDir = "$env:LOCALAPPDATA\PoshACME"
        
        # Create a temporary directory
        $tempDir = Join-Path -Path ([System.IO.Path]::GetTempPath()) -ChildPath ([System.Guid]::NewGuid().ToString())
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Extract the zip file
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($BackupPath, $tempDir)
        
        # Create the PoshACME directory if it doesn't exist
        if (-not (Test-Path $poshACMEDir)) {
            New-Item -ItemType Directory -Path $poshACMEDir -Force | Out-Null
        }
        
        # Copy account files to the PoshACME directory
        $accountsDir = Join-Path -Path $tempDir -ChildPath "Accounts"
        if (Test-Path $accountsDir) {
            Copy-Item -Path $accountsDir -Destination $poshACMEDir -Recurse -Force
        }
        
        # Copy current account file
        $currentAccountFile = Join-Path -Path $tempDir -ChildPath "current-account.json"
        if (Test-Path $currentAccountFile) {
            Copy-Item -Path $currentAccountFile -Destination $poshACMEDir -Force
        }
        
        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force
        
        Write-Host "ACME account restored from: $BackupPath" -ForegroundColor Green
        Write-Log "ACME account restored from: $BackupPath" -Level 'Success'
        
        return $true
    } catch {
        Write-Error "Failed to restore ACME account key: $($_.Exception.Message)"
        Write-Log "Failed to restore ACME account key: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}
