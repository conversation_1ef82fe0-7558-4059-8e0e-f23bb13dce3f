# ================================================
# Automate Differential Procdumps
# ================================================
# Author: <EMAIL>
# Date: 09.13.2024
# Description:
# This script automates the capturing of process dumps using Procdump when the system memory usage reaches a specified threshold.
# It sets up itself to run as a scheduled task from C:\scripts\ and handles all necessary setup automatically.
# After setup, it deletes the original script and procdump64.exe from the initial location.

# Variables
$desiredScriptDirectory = "C:\scripts"
$scriptName = "AutoProcDump.ps1"
$procdumpExecutable = "procdump64.exe"
$processName = "RecordingServer64.exe"
$memoryThreshold = 70    # Memory usage threshold percentage
$sleepInterval = 30      # Sleep interval in seconds

# Functions
function Invoke-Procdump {
    param (
        [string]$processName,
        [string]$outputDirectory
    )
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $dumpFile = Join-Path $outputDirectory "$processName-$timestamp.dmp"
    
    # Run Procdump with -ma for full dump
    $procdumpResult = & "$desiredScriptDirectory\$procdumpExecutable" -ma $processName $dumpFile 2>&1
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -ne 0) {
        Write-Error "Procdump failed with exit code $exitCode. Output: $procdumpResult"
        return $null
    }
    return $dumpFile
}

function Register-AlertOnLogon {
    param (
        [string]$message
    )
    # Define the action script to display the alert and clean up
    $alertScriptContent = @"
Add-Type -AssemblyName System.Windows.Forms
[System.Windows.Forms.MessageBox]::Show(
    '$message',
    'Alert',
    [System.Windows.Forms.MessageBoxButtons]::OK,
    [System.Windows.Forms.MessageBoxIcon]::Warning
)
# Clean up: Delete the scheduled task and the alert script
\$taskName = 'ShowAlertOnLogon'
Unregister-ScheduledTask -TaskName \$taskName -Confirm:\$false
Remove-Item -Path `"\$($MyInvocation.MyCommand.Path)`" -Force
"@
    
    # Save the alert script to the scripts directory
    $alertScriptPath = Join-Path $desiredScriptDirectory "ShowAlert.ps1"
    Set-Content -Path $alertScriptPath -Value $alertScriptContent -Encoding UTF8

    # Create a scheduled task to run the script at user logon
    $taskName = "ShowAlertOnLogon"
    $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File `"$alertScriptPath`""
    $trigger = New-ScheduledTaskTrigger -AtLogOn
    $principal = New-ScheduledTaskPrincipal -GroupId "BUILTIN\Users" -LogonType Interactive -RunLevel Highest

    # Register the task
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Description "Displays an alert message at user logon and cleans up" -Force

    Write-Host "An alert has been queued to display on next user logon."
}

# Main Script

# Step 1: Check if the script is running from the desired directory
$currentScriptPath = $MyInvocation.MyCommand.Path
$currentScriptDirectory = Split-Path -Parent $currentScriptPath

if ($currentScriptDirectory -ne $desiredScriptDirectory) {
    # We need to move the script and procdump64.exe to C:\scripts
    Write-Host "Moving script and procdump64.exe to $desiredScriptDirectory..."

    # Create the desired directory if it doesn't exist
    if (-not (Test-Path -Path $desiredScriptDirectory)) {
        New-Item -Path $desiredScriptDirectory -ItemType Directory | Out-Null
    }

    # Copy the script to the desired directory
    Copy-Item -Path $currentScriptPath -Destination $desiredScriptDirectory -Force

    # Check if procdump64.exe exists in the current directory
    $procdumpPath = Join-Path $currentScriptDirectory $procdumpExecutable
    if (-not (Test-Path -Path $procdumpPath)) {
        Write-Error "$procdumpExecutable not found in $currentScriptDirectory. Please ensure it is present."
        exit 1
    }

    # Copy procdump64.exe to the desired directory
    Copy-Item -Path $procdumpPath -Destination $desiredScriptDirectory -Force

    # Create the scheduled task to run the script from the desired directory
    Write-Host "Creating scheduled task to run the script from $desiredScriptDirectory..."

    $taskName = "MemoryMonitor"
    $scriptFullPath = Join-Path $desiredScriptDirectory $scriptName
    $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -File `"$scriptFullPath`""
    $trigger = New-ScheduledTaskTrigger -AtStartup
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

    # Register the scheduled task
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Description "Monitors memory usage and triggers procdump when threshold is reached." -Force

    Write-Host "Scheduled task 'MemoryMonitor' has been created."

    # Start the scheduled task
    Start-ScheduledTask -TaskName $taskName

    # Delete the original script and procdump64.exe from the initial location
    Write-Host "Deleting original script and procdump64.exe from $currentScriptDirectory..."

    try {
        # Delete the original script
        Remove-Item -Path $currentScriptPath -Force -ErrorAction Stop

        # Delete the original procdump64.exe
        Remove-Item -Path $procdumpPath -Force -ErrorAction Stop

        Write-Host "Original files deleted successfully."
    }
    catch {
        Write-Warning "Could not delete original files: $_"
    }

    Write-Host "The script will now exit. The scheduled task will run the script from $desiredScriptDirectory."
    exit
}

# If we are running from the desired directory, proceed with the monitoring logic

# Prepare a list to store dump file paths
$dumpFiles = @()

# Check if procdump64.exe exists in the desired directory
$procdumpPath = Join-Path $desiredScriptDirectory $procdumpExecutable
if (-not (Test-Path $procdumpPath)) {
    Write-Error "$procdumpExecutable not found in $desiredScriptDirectory. Please ensure it is present."
    exit 1
}

# Trigger the first procdump on script start
$firstDump = Invoke-Procdump -processName $processName -outputDirectory $desiredScriptDirectory

if ($null -eq $firstDump) {
    Write-Error "Failed to create the first dump. Exiting script."
    exit 1
}

$dumpFiles += $firstDump

# Monitor memory usage
while ($true) {
    # Get the operating system object once
    $os = Get-CimInstance Win32_OperatingSystem
    $totalMemory = $os.TotalVisibleMemorySize
    $freeMemory = $os.FreePhysicalMemory
    $usedMemory = $totalMemory - $freeMemory
    $memUsagePercent = [math]::Round(($usedMemory / $totalMemory) * 100, 2)
    
    Write-Host "Current memory usage: $memUsagePercent%"

    # Check if memory usage exceeds threshold
    if ($memUsagePercent -ge $memoryThreshold) {
        # Trigger the second procdump
        $secondDump = Invoke-Procdump -processName $processName -outputDirectory $desiredScriptDirectory

        if ($null -eq $secondDump) {
            Write-Error "Failed to create the second dump."
            exit 1
        }

        $dumpFiles += $secondDump

        # Prepare the alert message
        $alertMessage = "Memory Consumption has reached $memoryThreshold%. Procdump has been triggered."

        # Queue the alert to show on next user logon
        Register-AlertOnLogon -message $alertMessage

        # Compress the dump files into a zip file in the scripts directory
        $timestamp = Get-Date -Format "yyyyMMddHHmmss"
        $zipFileName = "dumps-$timestamp.zip"
        $zipFilePath = Join-Path $desiredScriptDirectory $zipFileName
        Compress-Archive -Path $dumpFiles -DestinationPath $zipFilePath -Force

        Write-Host "Dump files compressed to $zipFilePath."

        # Delete the original dump files
        foreach ($dumpFile in $dumpFiles) {
            Remove-Item -Path $dumpFile -Force -ErrorAction SilentlyContinue
        }

        # Exit the script
        break
    }

    # Wait before checking again
    Start-Sleep -Seconds $sleepInterval
}

