#requires -version 5.1
<#
.SYNOPSIS
    Real-time monitoring for CompleteView service crashes with Slack notifications.

.DESCRIPTION
    This script monitors CompleteView services for crashes in real-time using WMI event subscriptions.
    When a crash is detected, it sends notifications to <PERSON>lack and tracks dump file creation.

    This is the real-time version of the CVDemoCrashAlert.ps1 script, which uses WMI event
    subscriptions instead of polling for better responsiveness and lower resource usage.

.PARAMETER SlackWebhookUrl
    The Slack webhook URL to send notifications to.

.PARAMETER LogPath
    Path to the log file. Default is "C:\ProgramData\Salient Security Platform\CrashDumps\CVCrashMonitor.log".

.PARAMETER NoSlack
    If specified, no Slack notifications will be sent.

.PARAMETER TestMode
    If specified, the script will simulate crash events for testing.

.PARAMETER MonitorRecordingServer
    If specified, the script will monitor the CompleteView Recording Server.

.PARAMETER MonitorAdminService
    If specified, the script will monitor the CompleteView Administrative Service.

.PARAMETER MonitorManagementServer
    If specified, the script will monitor the CompleteView Management Server.

.PARAMETER LogLevel
    The logging level to use. Valid values are 'ERROR', 'WARNING', 'INFO', and 'DEBUG'. Default is 'INFO'.

.PARAMETER RunAsService
    If specified, the script will run as a Windows service.

.PARAMETER HeartbeatIntervalMinutes
    The interval in minutes between heartbeat checks. Default is 15 minutes.

.PARAMETER EventLogSource
    The event log source to use for service mode. Default is "CVDemoCrashAlert".

.PARAMETER Install
    If specified with -RunAsService, installs the script as a Windows service.

.PARAMETER Uninstall
    If specified with -RunAsService, uninstalls the script from Windows services.

.EXAMPLE
    .\CVDemoCrashAlert-RealTime.ps1 -MonitorRecordingServer -MonitorAdminService -MonitorManagementServer

.EXAMPLE
    .\CVDemoCrashAlert-RealTime.ps1 -RunAsService -Install

.NOTES
    Author: Salient Systems
    Version: 1.0
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidatePattern('^https://hooks\.slack\.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$')]
    [string]$SlackWebhookUrl = "*******************************************************************************",

    [Parameter(Mandatory = $false)]
    [string]$LogPath = "C:\ProgramData\Salient Security Platform\CrashDumps\CVCrashMonitor.log",

    [Parameter(Mandatory = $false)]
    [switch]$NoSlack,

    [Parameter(Mandatory = $false)]
    [switch]$TestMode,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorRecordingServer,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorAdminService,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorManagementServer,

    [Parameter(Mandatory = $false)]
    [ValidateSet('ERROR', 'WARNING', 'INFO', 'DEBUG')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [switch]$RunAsService,

    [Parameter(Mandatory = $false)]
    [int]$HeartbeatIntervalMinutes = 15,

    [Parameter(Mandatory = $false)]
    [string]$EventLogSource = "CVDemoCrashAlert",

    [Parameter(Mandatory = $false)]
    [switch]$Install,

    [Parameter(Mandatory = $false)]
    [switch]$Uninstall,

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 60)]
    [int]$DumpWaitTimeoutMinutes = 5
)

# Global variables
$script:EventSubscriptions = @()
$script:IsRunning = $true
$script:ServiceConfigs = @{}
$script:HeartbeatTimer = $null
$script:LastHeartbeatTime = Get-Date
$script:scriptVersion = "1.0"
$script:LogLevel = $LogLevel

#region Functions

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('INFO', 'WARNING', 'ERROR', 'DEBUG')]
        [string]$Level = 'INFO',

        [Parameter(Mandatory = $false)]
        [int]$MaxLogSizeMB = 10,

        [Parameter(Mandatory = $false)]
        [switch]$NoConsole
    )

    try {
        # Check if we should log this message based on the global LogLevel
        $logLevelPriority = @{
            'ERROR' = 0
            'WARNING' = 1
            'INFO' = 2
            'DEBUG' = 3
        }

        # Skip logging if the message level is less important than the global level
        if ($logLevelPriority[$Level] -gt $logLevelPriority[$script:LogLevel]) {
            return
        }

        # Format timestamp and message
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logMessage = "[$timestamp] [$Level] $Message"

        # Write to console with appropriate color
        if (-not $NoConsole) {
            switch ($Level) {
                'ERROR' {
                    Write-Host $logMessage -ForegroundColor Red
                }
                'WARNING' {
                    Write-Host $logMessage -ForegroundColor Yellow
                }
                'INFO' {
                    Write-Host $logMessage -ForegroundColor White
                }
                'DEBUG' {
                    if ($VerbosePreference -eq 'Continue' -or $script:LogLevel -eq 'DEBUG') {
                        Write-Host $logMessage -ForegroundColor Gray
                    }
                }
                default {
                    Write-Host $logMessage
                }
            }
        }

        # Write to log file if path is defined
        if ($LogPath) {
            # Check if log rotation is needed
            if ((Test-Path $LogPath -ErrorAction SilentlyContinue) -and
                ((Get-Item $LogPath -ErrorAction SilentlyContinue).Length -gt ($MaxLogSizeMB * 1MB))) {

                # Rotate logs - keep up to 3 backups
                try {
                    if (Test-Path "$LogPath.2.bak" -ErrorAction SilentlyContinue) {
                        Remove-Item "$LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$LogPath.1.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$LogPath.1.bak" "$LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$LogPath.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$LogPath.bak" "$LogPath.1.bak" -Force -ErrorAction SilentlyContinue
                    }

                    Rename-Item $LogPath "$LogPath.bak" -Force -ErrorAction SilentlyContinue
                }
                catch {
                    # If rotation fails, just append to the existing log
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to rotate log files: $errorMsg" -ForegroundColor Yellow
                }
            }

            # Ensure log directory exists
            $logDir = Split-Path -Path $LogPath -Parent
            if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                }
                catch {
                    # If we can't create the directory, just write to console
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to create log directory: $errorMsg" -ForegroundColor Red
                    return
                }
            }

            # Write to log file with error handling
            try {
                Add-Content -Path $LogPath -Value $logMessage -ErrorAction Stop
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Host "Failed to write to log file: $errorMsg" -ForegroundColor Red
            }
        }
    }
    catch {
        # Last resort error handling - just try to output something
        $errorMsg = $_.Exception.Message
        Write-Host "Error in Write-Log function: $errorMsg" -ForegroundColor Red
        Write-Host $Message -ForegroundColor Cyan
    }
}

function Wait-ForDumpFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMinutes = 5,

        [Parameter(Mandatory = $false)]
        [int]$CheckIntervalSeconds = 5
    )

    try {
        Write-Log "Waiting for dump file to appear at: $DumpPath" -Level 'INFO'
        Write-Log "Timeout set to $TimeoutMinutes minutes" -Level 'DEBUG'

        $startTime = Get-Date
        $timeoutTime = $startTime.AddMinutes($TimeoutMinutes)

        while ((Get-Date) -lt $timeoutTime) {
            if (Test-Path -Path $DumpPath) {
                $fileInfo = Get-Item -Path $DumpPath
                Write-Log "Dump file found: $DumpPath (Size: $([Math]::Round($fileInfo.Length / 1MB, 2)) MB)" -Level 'INFO'
                return $true
            }

            # Wait before checking again
            Start-Sleep -Seconds $CheckIntervalSeconds
        }

        # If we get here, the timeout expired
        $elapsedTime = [Math]::Round(((Get-Date) - $startTime).TotalMinutes, 2)
        Write-Log "Timeout ($elapsedTime minutes) expired waiting for dump file: $DumpPath" -Level 'WARNING'
        return $false
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error waiting for dump file: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Format-EventDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSObject]$LogEvent
    )

    try {
        # Extract message safely
        $message = $LogEvent.Message
        if ([string]::IsNullOrEmpty($message)) {
            Write-Log "Event message is empty or null" -Level 'WARNING'
            $message = ""
        }

        # Initialize properties
        $props = @{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = $LogEvent.TimeCreated.ToString("yyyy-MM-dd HH:mm:ss")
            Computer = $env:COMPUTERNAME
            FaultingApp = $null
            FaultingModule = "Unknown"
            ExceptionCode = "Unknown"
            ProcessIdDecimal = $null
            EventType = "Unknown"
            Description = ""
        }

        # Process based on event ID
        switch ($LogEvent.Id) {
            1000 {
                # Application crash
                $props.EventType = "Application Crash"

                # Extract details using regex with error handling
                try {
                    if ($message -match "Faulting application name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }

                    if ($message -match "Faulting module name: ([^,]+)") {
                        $props.FaultingModule = $Matches[1].Trim()
                    }

                    if ($message -match "Exception code: (0x[0-9a-fA-F]+)") {
                        $props.ExceptionCode = $Matches[1].Trim()
                    }

                    if ($message -match "Faulting process id: (0x[0-9a-fA-F]+)") {
                        $hexId = $Matches[1].Trim()
                        try {
                            $props.ProcessIdDecimal = [Convert]::ToInt32($hexId, 16)
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to convert process ID '$hexId' to decimal: $errorMsg" -Level 'WARNING'
                        }
                    }

                    $props.Description = "Application crash detected"
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 1000 message: $errorMsg" -Level 'ERROR'
                }
            }

            1001 {
                # Windows Error Reporting
                $props.EventType = "Error Report"

                try {
                    # Try to extract the application name from WER message
                    if ($message -match "Fault bucket [^,]+, type [^,]+, process name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }
                    elseif ($message -match "process: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }

                    # Extract any additional details
                    if ($message -match "Fault bucket ([^,]+)") {
                        $props.ExceptionCode = $Matches[1].Trim()
                    }

                    $props.Description = "Windows Error Reporting details"
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 1001 message: $errorMsg" -Level 'ERROR'
                }
            }

            7031 {
                # Service crashed and was recovered
                $props.EventType = "Service Recovery"

                try {
                    # Extract service name
                    if ($message -match "The ([^(]+) service terminated unexpectedly") {
                        $serviceName = $Matches[1].Trim()
                        $props.FaultingApp = $serviceName
                        $props.Description = "Service terminated unexpectedly and was restarted"
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 7031 message: $errorMsg" -Level 'ERROR'
                }
            }

            7034 {
                # Service terminated unexpectedly
                $props.EventType = "Service Termination"

                try {
                    # Extract service name
                    if ($message -match "The ([^(]+) service terminated unexpectedly") {
                        $serviceName = $Matches[1].Trim()
                        $props.FaultingApp = $serviceName
                        $props.Description = "Service terminated unexpectedly"
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 7034 message: $errorMsg" -Level 'ERROR'
                }
            }

            2004 {
                # Memory resource notification
                $props.EventType = "Low Memory"
                $props.Description = "System is running low on memory"

                # We don't have a specific app for this, but it affects all running apps
                $props.FaultingApp = "System"
            }

            6008 {
                # Unexpected shutdown
                $props.EventType = "System Shutdown"
                $props.Description = "The system was unexpectedly shut down"
                $props.FaultingApp = "System"
            }

            default {
                # Unknown event type
                $props.EventType = "Unknown Event"
                $props.Description = "Unrecognized event type"

                # Try to extract any app name from the message
                if ($message -match "application name: ([^,]+)") {
                    $props.FaultingApp = $Matches[1].Trim()
                }
                elseif ($message -match "process name: ([^,]+)") {
                    $props.FaultingApp = $Matches[1].Trim()
                }
                elseif ($message -match "service ([^,]+)") {
                    $props.FaultingApp = $Matches[1].Trim()
                }
            }
        }

        # Log the extracted details
        $detailsLog = "Event details - ID: $($props.EventID), Type: $($props.EventType), App: $($props.FaultingApp), Module: $($props.FaultingModule), Exception: $($props.ExceptionCode), PID: $($props.ProcessIdDecimal)"
        Write-Log $detailsLog -Level 'INFO'

        return [PSCustomObject]$props
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error formatting event details: $errorMsg" -Level 'ERROR'

        # Return a minimal object to prevent null reference exceptions
        return [PSCustomObject]@{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            Computer = $env:COMPUTERNAME
            FaultingApp = $null
            FaultingModule = "Unknown"
            ExceptionCode = "Unknown"
            ProcessIdDecimal = $null
            EventType = "Unknown"
            Description = "Error processing event details"
        }
    }
}

function Send-SlackMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $true)]
        [string]$MessageText,

        [Parameter(Mandatory = $false)]
        [int]$MaxRetries = 2,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutSec = 30
    )

    # Validate webhook URL
    if (-not $WebhookUrl -or -not $WebhookUrl.StartsWith("https://hooks.slack.com/")) {
        throw "Invalid Slack webhook URL format"
    }

    try {
        # Convert to JSON with proper escaping
        $payload = @{
            text = $MessageText
        } | ConvertTo-Json -ErrorAction Stop -Depth 3 -Compress:$false

        # Add timeout and error handling for network issues
        $params = @{
            Uri = $WebhookUrl
            Method = 'Post'
            Body = $payload
            ContentType = 'application/json'
            TimeoutSec = $TimeoutSec
            ErrorAction = 'Stop'
        }

        # Add proxy support if system is configured to use a proxy
        try {
            $systemProxy = [System.Net.WebRequest]::GetSystemWebProxy()
            if ($systemProxy -and $systemProxy.GetProxy([uri]$WebhookUrl) -ne $WebhookUrl) {
                $proxyUrl = $systemProxy.GetProxy([uri]$WebhookUrl)
                Write-Log "Using system proxy for Slack API request: $proxyUrl" -Level 'DEBUG'
                $params.UseDefaultCredentials = $true
                # Only set the proxy if we have a valid URI
                if ($proxyUrl -and $proxyUrl.AbsoluteUri) {
                    $params.Proxy = $proxyUrl.AbsoluteUri
                    $params.ProxyUseDefaultCredentials = $true
                }
            }
        }
        catch {
            Write-Log "Error detecting proxy settings: $($_.Exception.Message)" -Level 'DEBUG'
            # Continue without proxy settings
        }

        # Send the notification with retry logic for transient errors
        $retryCount = 0
        $success = $false

        do {
            try {
                $response = Invoke-RestMethod @params

                # Slack webhooks return "ok" when successful
                if ($response -ne "ok") {
                    Write-Log "Slack API returned unexpected response: $response" -Level 'WARNING'
                }

                Write-Log "Slack notification sent successfully" -Level 'DEBUG'
                $success = $true
                return $true
            }
            catch [System.Net.WebException] {
                $statusCode = [int]$_.Exception.Response.StatusCode

                # Handle specific HTTP status codes
                switch ($statusCode) {
                    429 {
                        # Rate limiting - wait and retry
                        $retryCount++
                        $waitTime = [Math]::Min(10 * $retryCount, 30)  # Exponential backoff with max 30 seconds
                        Write-Log "Slack API rate limit exceeded. Retrying in $waitTime seconds..." -Level 'WARNING'
                        Start-Sleep -Seconds $waitTime
                    }
                    500 {
                        # Server error - wait and retry
                        $retryCount++
                        $waitTime = [Math]::Min(5 * $retryCount, 15)  # Exponential backoff with max 15 seconds
                        Write-Log "Slack API server error. Retrying in $waitTime seconds..." -Level 'WARNING'
                        Start-Sleep -Seconds $waitTime
                    }
                    default {
                        # Other errors - log and throw
                        Write-Log "Slack API error (HTTP $statusCode): $($_.Exception.Message)" -Level 'ERROR'
                        throw "Failed to send Slack notification: HTTP $statusCode - $($_.Exception.Message)"
                    }
                }
            }
            catch {
                # General error - log and throw
                Write-Log "Error sending Slack notification: $($_.Exception.Message)" -Level 'ERROR'
                throw "Failed to send Slack notification: $($_.Exception.Message)"
            }
        } while (-not $success -and $retryCount -lt $MaxRetries)

        # If we get here, we've exhausted retries
        if (-not $success) {
            Write-Log "Failed to send Slack notification after $MaxRetries retries" -Level 'ERROR'
            throw "Failed to send Slack notification after $MaxRetries retries"
        }

        return $true
    }
    catch {
        Write-Log "Error in Send-SlackMessage: $($_.Exception.Message)" -Level 'ERROR'
        throw "Failed to send Slack notification: $($_.Exception.Message)"
    }
}

function Send-SlackNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$AppName,

        [Parameter(Mandatory = $true)]
        [string]$Hostname,

        [Parameter(Mandatory = $true)]
        [string]$CrashTime,

        [Parameter(Mandatory = $true)]
        [string]$FaultingModule,

        [Parameter(Mandatory = $true)]
        [string]$ExceptionCode,

        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$DumpAvailable = $false,

        [Parameter(Mandatory = $false)]
        [bool]$InitialAlert = $true
    )

    try {
        # Check if URL is accessible (optional validation)
        try {
            $testConnection = Test-NetConnection -ComputerName "hooks.slack.com" -Port 443 -InformationLevel Quiet -ErrorAction SilentlyContinue -WarningAction SilentlyContinue
            if (-not $testConnection) {
                Write-Log "Warning: Cannot connect to Slack API (hooks.slack.com:443)" -Level 'WARNING'
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Could not test connection to Slack: $errorMsg" -Level 'WARNING'
        }

        # Sanitize inputs to prevent JSON formatting issues
        $AppName = $AppName -replace '["\r\n]', ' '
        $Hostname = $Hostname -replace '["\r\n]', ' '
        $CrashTime = $CrashTime -replace '["\r\n]', ' '
        $FaultingModule = $FaultingModule -replace '["\r\n]', ' '
        $ExceptionCode = $ExceptionCode -replace '["\r\n]', ' '
        # For the dump path, only remove quotes and newlines, but keep backslashes
        $DumpPath = $DumpPath -replace '["\r\n]', ' '

        # Prepare message text
        # For Slack, we don't need to escape backslashes when using code formatting with backticks
        # Just use the path as-is
        $formattedPath = $DumpPath

        # Use a single backtick for Slack code formatting
        $backtick = [char]96

        if ($DumpAvailable) {
            $dumpText = "*Dump File:* " + $backtick + $formattedPath + $backtick
        } else {
            $dumpText = "*Dump File:* _Not available_" + "`n" + "Expected path: " + $backtick + $formattedPath + $backtick
        }

        if ($InitialAlert) {
            $title = "$AppName Crash Detected"
            $titleEmoji = ":rotating_light:"
        } else {
            $title = "Dump File Status Update"
            $titleEmoji = ":information_source:"
        }

        # Set emoji based on dump availability
        $statusEmoji = if ($DumpAvailable) { ":white_check_mark:" } else { ":x:" }

        # Create the message with proper line breaks for Slack
        $messageText = "$titleEmoji *$title*

*Host:* $Hostname
*Time:* $CrashTime
*Faulting Module:* $FaultingModule
*Exception Code:* $ExceptionCode

$statusEmoji $dumpText"

        # Use the helper function to send the message
        return Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error in Send-SlackNotification: $errorMsg" -Level 'ERROR'
        throw "Failed to send Slack notification: $errorMsg"
    }
}

function Register-EventSubscriptions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [int[]]$EventIDs = @(1000, 1001, 7031, 7034, 2004, 6008),

        [Parameter(Mandatory = $false)]
        [string]$LogName = "Application"
    )

    try {
        Write-Log "Registering WMI event subscriptions for event IDs: $($EventIDs -join ', ')" -Level 'INFO'

        # Create a list to store all app names we want to monitor
        $appNames = $ServiceConfigs.Values | ForEach-Object { $_.AppName }
        Write-Log "Monitoring applications: $($appNames -join ', ')" -Level 'INFO'

        # Create event queries for each event ID
        foreach ($eventId in $EventIDs) {
            $query = "SELECT * FROM __InstanceCreationEvent WITHIN 2 WHERE " +
                     "TargetInstance ISA 'Win32_NTLogEvent' AND " +
                     "TargetInstance.LogFile = '$LogName' AND " +
                     "TargetInstance.EventCode = $eventId"

            Write-Log "Registering subscription for Event ID $eventId" -Level 'DEBUG'

            # Create the action script block
            $action = {
                param($eventObj, $eventSubscriptionId)

                # Get the event details
                $eventRecord = $eventObj.SourceEventArgs.NewEvent.TargetInstance

                # Convert to a format compatible with our existing processing
                $eventObj = [PSCustomObject]@{
                    Id = $eventRecord.EventCode
                    Message = $eventRecord.Message
                    TimeCreated = [DateTime]::ParseExact($eventRecord.TimeGenerated.Split('.')[0], 'yyyyMMddHHmmss', $null)
                    ProviderName = $eventRecord.SourceName
                }

                # Process the event
                Process-RealTimeEvent -LogEvent $eventObj
            }

            # Register the event subscription
            $subscription = Register-WmiEvent -Query $query -Action $action -SourceIdentifier "CVCrashAlert_Event_$eventId" -ErrorAction Stop

            # Store the subscription for later cleanup
            $script:EventSubscriptions += $subscription

            Write-Log "Successfully registered subscription for Event ID $eventId" -Level 'INFO'
        }

        Write-Log "All event subscriptions registered successfully" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error registering event subscriptions: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Unregister-EventSubscriptions {
    [CmdletBinding()]
    param()

    try {
        Write-Log "Unregistering all event subscriptions" -Level 'INFO'

        foreach ($subscription in $script:EventSubscriptions) {
            try {
                Unregister-Event -SourceIdentifier $subscription.Name -Force -ErrorAction SilentlyContinue
                Write-Log "Unregistered subscription: $($subscription.Name)" -Level 'DEBUG'
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error unregistering subscription $($subscription.Name): $errorMsg" -Level 'WARNING'
            }
        }

        # Also clean up any lingering event subscribers
        Get-EventSubscriber -ErrorAction SilentlyContinue |
            Where-Object { $_.SourceIdentifier -like "CVCrashAlert_*" } |
            ForEach-Object {
                try {
                    Unregister-Event -SourceIdentifier $_.SourceIdentifier -Force -ErrorAction SilentlyContinue
                    Write-Log "Unregistered lingering subscription: $($_.SourceIdentifier)" -Level 'DEBUG'
                }
                catch {
                    # Ignore errors for cleanup
                }
            }

        # Clear the subscriptions array
        $script:EventSubscriptions = @()

        Write-Log "All event subscriptions unregistered" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error during subscription cleanup: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Start-EventMonitoring {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [int]$HeartbeatIntervalMinutes = 15,

        [Parameter(Mandatory = $false)]
        [switch]$RunAsService
    )

    try {
        # Store service configs globally for event handlers to access
        $script:ServiceConfigs = $ServiceConfigs

        # Register event subscriptions
        $success = Register-EventSubscriptions -ServiceConfigs $ServiceConfigs
        if (-not $success) {
            Write-Log "Failed to register event subscriptions, aborting real-time monitoring" -Level 'ERROR'
            return $false
        }

        # Set up heartbeat timer if running as a service
        if ($RunAsService) {
            # Register event log source if it doesn't exist
            if (-not [System.Diagnostics.EventLog]::SourceExists($EventLogSource)) {
                try {
                    [System.Diagnostics.EventLog]::CreateEventSource($EventLogSource, "Application")
                    Write-Log "Created event log source: $EventLogSource" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to create event log source: $errorMsg" -Level 'WARNING'
                }
            }

            # Create a timer for heartbeat logging
            $heartbeatIntervalMs = $HeartbeatIntervalMinutes * 60 * 1000
            $script:HeartbeatTimer = New-Object System.Timers.Timer
            $script:HeartbeatTimer.Interval = $heartbeatIntervalMs
            $script:HeartbeatTimer.AutoReset = $true

            # Define the heartbeat action
            $heartbeatAction = {
                $script:LastHeartbeatTime = Get-Date
                $uptime = (Get-Date) - $script:scriptStartTime
                $uptimeStr = "{0:D2}d:{1:D2}h:{2:D2}m:{3:D2}s" -f $uptime.Days, $uptime.Hours, $uptime.Minutes, $uptime.Seconds

                Write-Log "Heartbeat: CVDemoCrashAlert is running (Uptime: $uptimeStr)" -Level 'INFO'

                # Write to event log if running as a service
                if ($RunAsService) {
                    try {
                        [System.Diagnostics.EventLog]::WriteEntry(
                            $EventLogSource,
                            "CVDemoCrashAlert heartbeat - Service is running (Uptime: $uptimeStr)",
                            [System.Diagnostics.EventLogEntryType]::Information,
                            1000
                        )
                    }
                    catch {
                        # Ignore event log errors in the heartbeat
                    }
                }
            }

            # Register the heartbeat event
            $script:HeartbeatTimer.Elapsed.Register($heartbeatAction)
            $script:HeartbeatTimer.Start()

            Write-Log "Heartbeat timer started with interval of $HeartbeatIntervalMinutes minutes" -Level 'INFO'
        }

        # Register Ctrl+C handler for graceful shutdown
        [Console]::TreatControlCAsInput = $true

        Write-Log "Real-time event monitoring started. Press Ctrl+C to stop." -Level 'INFO'

        # Main loop to keep the script running
        while ($script:IsRunning) {
            # Check for Ctrl+C
            if ([Console]::KeyAvailable) {
                $key = [Console]::ReadKey($true)
                if (($key.Modifiers -band [ConsoleModifiers]::Control) -and ($key.Key -eq 'C')) {
                    Write-Log "Ctrl+C detected, shutting down..." -Level 'INFO'
                    $script:IsRunning = $false
                    break
                }
            }

            # Sleep to reduce CPU usage
            Start-Sleep -Milliseconds 500
        }

        # Cleanup before exiting
        if ($script:HeartbeatTimer) {
            $script:HeartbeatTimer.Stop()
            $script:HeartbeatTimer.Dispose()
        }

        Unregister-EventSubscriptions

        Write-Log "Real-time event monitoring stopped" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error in event monitoring: $errorMsg" -Level 'ERROR'

        # Cleanup on error
        if ($script:HeartbeatTimer) {
            $script:HeartbeatTimer.Stop()
            $script:HeartbeatTimer.Dispose()
        }

        Unregister-EventSubscriptions

        return $false
    }
}

function Install-ServiceTask {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$TaskName = "CVDemoCrashAlert_Monitor",

        [Parameter(Mandatory = $false)]
        [string]$Description = "CompleteView Crash Monitoring Service",

        [Parameter(Mandatory = $false)]
        [string]$ScriptPath = $PSCommandPath
    )

    try {
        Write-Log "Installing scheduled task for service mode: $TaskName" -Level 'INFO'

        # Check if the task already exists
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue

        if ($existingTask) {
            Write-Log "Task already exists, removing it first" -Level 'INFO'
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false -ErrorAction Stop
        }

        # Create the action to run the PowerShell script
        $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -File `"$ScriptPath`" -RunAsService"

        # Create a trigger to run at system startup
        $trigger = New-ScheduledTaskTrigger -AtStartup

        # Set the principal to run with highest privileges
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

        # Create settings to allow the task to run indefinitely and restart if it fails
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -RestartCount 3 -RestartInterval (New-TimeSpan -Minutes 1) -ExecutionTimeLimit (New-TimeSpan -Hours 0)

        # Register the scheduled task
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description $Description -ErrorAction Stop

        Write-Log "Successfully installed scheduled task: $TaskName" -Level 'INFO'
        return $true
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error installing scheduled task: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Uninstall-ServiceTask {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$TaskName = "CVDemoCrashAlert_Monitor"
    )

    try {
        Write-Log "Uninstalling scheduled task: $TaskName" -Level 'INFO'

        # Check if the task exists
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue

        if ($existingTask) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false -ErrorAction Stop
            Write-Log "Successfully uninstalled scheduled task: $TaskName" -Level 'INFO'
            return $true
        }
        else {
            Write-Log "Task does not exist: $TaskName" -Level 'WARNING'
            return $false
        }
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error uninstalling scheduled task: $errorMsg" -Level 'ERROR'
        return $false
    }
}

function Process-RealTimeEvent {
    # Note: Using a non-standard verb for internal function only
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSUseApprovedVerbs', '')]
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSObject]$LogEvent
    )

    try {
        Write-Log "Processing real-time event: ID $($LogEvent.Id) from $($LogEvent.ProviderName)" -Level 'INFO'

        # Format the event details using our existing function
        $details = Format-EventDetails -LogEvent $LogEvent

        # Skip if we couldn't extract the faulting app
        if (-not $details.FaultingApp) {
            Write-Log "Skipping event - could not determine faulting application" -Level 'WARNING'
            return
        }

        # Check if the faulting app matches any of our monitored services
        $matchedService = $null

        foreach ($serviceName in $script:ServiceConfigs.Keys) {
            $serviceConfig = $script:ServiceConfigs[$serviceName]
            if ($details.FaultingApp -eq $serviceConfig.AppName) {
                $matchedService = $serviceConfig
                break
            }
        }

        # If we found a matching service, process the event
        if ($matchedService) {
            $appName = $matchedService.AppName
            $friendlyName = $matchedService.FriendlyName
            $dumpFolder = $matchedService.DumpPath

            Write-Log "Processing real-time event for $friendlyName ($appName)" -Level 'INFO'

            $hostname = $env:COMPUTERNAME
            $time = $details.Time
            $faultingModule = if ($null -eq $details.FaultingModule) { "Unknown" } else { $details.FaultingModule }
            $exceptionCode = if ($null -eq $details.ExceptionCode) { "Unknown" } else { $details.ExceptionCode }

            # Ensure dump directory exists
            if (-not (Test-Path -Path $dumpFolder -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $dumpFolder -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created dump directory for $friendlyName - $dumpFolder" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to create dump directory for ${friendlyName}: $errorMsg" -Level 'ERROR'
                }
            }

            # Ensure we have a valid process ID
            if (-not $details.ProcessIdDecimal) {
                Write-Log "Could not determine process ID from event, using timestamp instead" -Level 'WARNING'
                $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), (Get-Date).ToString("yyyyMMddHHmmss")
            }
            else {
                $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $details.ProcessIdDecimal
            }

            $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile

            # Check if dump file exists
            $dumpExistsInitially = Test-Path -Path $fullDumpPath

            # Send initial notification if Slack is enabled
            if (-not $NoSlack) {
                try {
                    Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                    Write-Log "Sent initial Slack notification for $friendlyName crash at $time" -Level 'INFO'
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Failed to send initial Slack notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                }
            }
            else {
                Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
            }

            # If dump doesn't exist initially, wait for it
            if (-not $dumpExistsInitially) {
                $dumpAvailable = Wait-ForDumpFile -DumpPath $fullDumpPath -TimeoutMinutes $DumpWaitTimeoutMinutes

                # Send follow-up notification if Slack is enabled
                if (-not $NoSlack) {
                    try {
                        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false

                        if ($dumpAvailable) {
                            Write-Log "Sent second Slack notification for $friendlyName dump availability at $time" -Level 'INFO'
                        }
                        else {
                            Write-Log "Sent notification that $friendlyName dump did not appear after timeout at $time" -Level 'WARNING'
                        }
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-Log "Failed to send follow-up Slack notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                    }
                }
                else {
                    if ($dumpAvailable) {
                        Write-Log "Dump file for $friendlyName is now available at: $fullDumpPath" -Level 'INFO'
                    }
                    else {
                        Write-Log "Dump file for $friendlyName did not appear after waiting $DumpWaitTimeoutMinutes minutes" -Level 'WARNING'
                    }
                }
            }
        }
        else {
            # This crash is not for one of our monitored services
            Write-Log "Skipping event - faulting application '$($details.FaultingApp)' is not in the monitored services list" -Level 'DEBUG'
        }
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error processing real-time event: $errorMsg" -Level 'ERROR'
    }
}

function Test-ServiceStatus {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking service status for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        # Map the executable name to the actual Windows service name
        $windowsServiceName = switch ($config.AppName) {
            "RecordingServer64.exe" { "CVRecordingServer" }
            "AdminService64.exe" { "CVAdminService" }
            "ManagementServer.exe" { "CVManagementServer" }
            default { $null }
        }

        if ($windowsServiceName) {
            try {
                $service = Get-Service -Name $windowsServiceName -ErrorAction Stop

                if ($service.Status -ne 'Running') {
                    Write-Log "$friendlyName service is not running (Status: $($service.Status))" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Service Not Running*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Status:* $($service.Status)
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent service status notification for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send service status notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
                else {
                    Write-Log "$friendlyName service is running" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error checking service status for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Could not determine Windows service name for $friendlyName" -Level 'WARNING'
        }
    }
}

#endregion Functions

#region Main

# Main script execution
try {
    # Script start time for execution tracking
    $scriptStartTime = Get-Date

    # Display script banner
    Write-Log "CVDemoCrashAlert-RealTime v$script:scriptVersion - CompleteView Services Crash Monitor" -Level 'INFO'
    Write-Log "=======================================================================" -Level 'INFO'
    Write-Log "Log level set to: $script:LogLevel" -Level 'INFO'

    # Determine operating mode
    if ($RunAsService) {
        Write-Log "Running in SERVICE mode (real-time monitoring)" -Level 'INFO'
    }
    else {
        Write-Log "Running in REAL-TIME mode" -Level 'INFO'
    }

    # Test mode banner
    if ($TestMode) {
        Write-Log "RUNNING IN TEST MODE - For testing purposes only" -Level 'WARNING'
    }

    # Special handling for service installation/uninstallation
    if ($RunAsService -and $Install) {
        $result = Install-ServiceTask
        if ($result) {
            Write-Log "Service installation completed successfully. The service will start automatically at next system boot." -Level 'INFO'
            Write-Log "To start the service immediately, run: Start-ScheduledTask -TaskName 'CVDemoCrashAlert_Monitor'" -Level 'INFO'
        }
        else {
            Write-Log "Service installation failed." -Level 'ERROR'
        }
        exit 0
    }
    elseif ($RunAsService -and $Uninstall) {
        $result = Uninstall-ServiceTask
        if ($result) {
            Write-Log "Service uninstallation completed successfully." -Level 'INFO'
        }
        else {
            Write-Log "Service uninstallation failed or service was not installed." -Level 'WARNING'
        }
        exit 0
    }

    # Initialize service configurations
    $serviceConfigs = @{}

    # Add Recording Server if enabled
    if ($MonitorRecordingServer) {
        $serviceConfigs["RecordingServer"] = @{
            AppName = "RecordingServer64.exe"
            FriendlyName = "CompleteView Recording Server"
            DumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\RecordingServer"
        }
    }

    # Add Administrative Service if enabled
    if ($MonitorAdminService) {
        $serviceConfigs["AdminService"] = @{
            AppName = "AdminService64.exe"
            FriendlyName = "CompleteView Administrative Service"
            DumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\AdminService"
        }
    }

    # Add Management Server if enabled
    if ($MonitorManagementServer) {
        $serviceConfigs["ManagementServer"] = @{
            AppName = "ManagementServer.exe"
            FriendlyName = "CompleteView Management Server"
            DumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\ManagementServer"
        }
    }

    # Validate that at least one service is enabled
    if ($serviceConfigs.Count -eq 0) {
        Write-Log "No services selected for monitoring. Please specify at least one of: -MonitorRecordingServer, -MonitorAdminService, -MonitorManagementServer" -Level 'ERROR'
        exit 1
    }

    # Log the services being monitored
    Write-Log "Monitoring the following services:" -Level 'INFO'
    foreach ($serviceName in $serviceConfigs.Keys) {
        $config = $serviceConfigs[$serviceName]
        Write-Log "- $($config.FriendlyName) ($($config.AppName))" -Level 'INFO'
    }

    # Perform initial health check
    Test-ServiceStatus -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Start the event monitoring loop
    if ($RunAsService) {
        # Service mode with heartbeat
        Start-EventMonitoring -ServiceConfigs $serviceConfigs -HeartbeatIntervalMinutes $HeartbeatIntervalMinutes -RunAsService
    }
    else {
        # Interactive mode
        Start-EventMonitoring -ServiceConfigs $serviceConfigs
    }

    # Calculate and log execution time when monitoring ends
    $executionTime = (Get-Date) - $scriptStartTime
    Write-Log "Real-time monitoring ended after $($executionTime.TotalSeconds.ToString('0.00')) seconds" -Level 'INFO'
}
catch {
    # Global error handler
    $errorMsg = $_.Exception.Message
    Write-Log "Critical error in script execution: $errorMsg" -Level 'ERROR'

    # Try to send an alert about the script failure if possible
    if (-not $NoSlack -and -not [string]::IsNullOrEmpty($SlackWebhookUrl)) {
        try {
            $errorMessage = $_.Exception.Message
            $errorDetails = if ($_.Exception.StackTrace) { $_.Exception.StackTrace } else { "No stack trace available" }

            $messageText = ":x: *CVDemoCrashAlert Script Error*

*Host:* $env:COMPUTERNAME
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
*Error:* $errorMessage

*Details:*
```
$errorDetails
```"

            # Use our helper function to send the message
            $null = Send-SlackMessage -WebhookUrl $SlackWebhookUrl -MessageText $messageText
            # Log the success
            $logMessage = "Error notification sent to Slack"
            Write-Log $logMessage -Level 'INFO'
        }
        catch {
            $slackErrorMsg = $_.Exception.Message
            Write-Log "Failed to send error notification to Slack: $slackErrorMsg" -Level 'ERROR'
        }
    }

    # Clean up any event subscriptions
    try {
        Unregister-EventSubscriptions
        Write-Log "Cleaned up event subscriptions" -Level 'INFO'
    }
    catch {
        # Ignore errors during cleanup
    }

    # Exit with non-zero code to indicate failure
    exit 1
}
finally {
    # This block always executes, even if there are errors
    $executionTime = (Get-Date) - $scriptStartTime
    $seconds = [math]::Round($executionTime.TotalSeconds, 2)
    # Log the execution time
    Write-Log "Script execution finished in $seconds seconds." -Level 'INFO'
}

#endregion Main
