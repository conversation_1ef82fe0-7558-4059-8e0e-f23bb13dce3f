<#
.SYNOPSIS
    Toggles CompleteView Desktop Client to Alarm View using UI automation.

.DESCRIPTION
    This script finds the CompleteView Desktop Client window, brings it to the foreground,
    maximizes it, and clicks the Alarm View button. Designed to be run from Task Scheduler
    to periodically refresh the Alarm View.

.NOTES
    Version:        1.1
    Author:         <EMAIL>
    Creation Date:  2024-02-20
    
    Requirements:
    - CompleteView Desktop Client must be running
    - <PERSON><PERSON><PERSON> must run with UI access rights
    - Designed for 1280x720 resolution (scales automatically)

.EXAMPLE
    powershell.exe -WindowStyle Hidden -File "path\to\DCTabToggle.ps1"
#>

# ---------------------------------------------------------------------------
#  Assemblies & Win32 interop
# ---------------------------------------------------------------------------
Add-Type -AssemblyName System.Windows.Forms

if (-not ([System.Management.Automation.PSTypeName]'Win32').Type) {
    Add-Type @"
    using System;
    using System.Runtime.InteropServices;

    public static class Win32 {
        [DllImport("user32.dll", SetLastError = true)]
        public static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        public static extern void mouse_event(uint dwFlags, uint dx, uint dy,
                                             uint dwData, int dwExtraInfo);

        [DllImport("user32.dll")]
        public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        public const uint MOUSEEVENTF_LEFTDOWN = 0x0002;
        public const uint MOUSEEVENTF_LEFTUP   = 0x0004;

        public const int  SW_MAXIMIZE = 3;
    }
"@
}

# ---------------------------------------------------------------------------
#  Alarm-monitor settings
# ---------------------------------------------------------------------------
$logFiles = 'P','Q','R','S','T','U','V','W','X' |
            ForEach-Object { "$_:\RecordingServer.log" }

$alarmPattern = [regex]'Event\s+Type:\s+Alarm(?<Action>Start|End)\s+Camera:\s*(?<Cam>\d+)'

function Get-ActiveAlarms {
    param([string[]]$Files)

    $active = @{}

    foreach ($file in $Files) {
        if (-not (Test-Path $file)) { continue }

        $content = Get-Content $file -Raw
        foreach ($m in $alarmPattern.Matches($content)) {
            $cam   = $m.Groups['Cam'].Value
            $event = $m.Groups['Action'].Value   # Start | End

            switch ($event) {
                'Start' { $active[$cam] = ($active[$cam] + 1) }
                'End'   { if ($active[$cam]) { $active[$cam]-- } }
            }
        }
    }

    # return cameras with unmatched AlarmStart
    return $active.GetEnumerator() | Where-Object { $_.Value -gt 0 }
}

# Wait until no alarms are active
while ($true) {
    $active = Get-ActiveAlarms -Files $logFiles
    if ($active.Count -eq 0) { break }

    $camList = ($active | ForEach-Object { $_.Key } | Sort-Object) -join ', '
    Write-Host "Active alarms detected: $camList. Waiting 5 seconds..."
    Start-Sleep -Seconds 5
}

# ---------------------------------------------------------------------------
#  UI-automation settings
# ---------------------------------------------------------------------------
$config = @{
    ReferenceResolution = @{ Width = 1280; Height = 720 }
    ClickCoordinates    = @{ AlarmView = @{ X = 250; Y = 38 } }
    LogPath             = "C:\Logs\AlarmViewMonitor.log"
}

function Write-Log {
    param([string]$Message)
    $ts = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    "$ts - $Message" | Out-File $config.LogPath -Append -Encoding utf8
}

# Create log folder if missing
$logDir = Split-Path $config.LogPath -Parent
if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
}

function Get-TargetProcess {
    $procs = Get-Process DesktopClient -ErrorAction SilentlyContinue |
             Where-Object { $_.MainWindowHandle -ne 0 }

    if (-not $procs) {
        Write-Log "No DesktopClient process with a visible window found"
        return $null
    }

    if ($procs.Count -gt 1) {
        Write-Log "Multiple DesktopClient instances found; using most recent"
        return $procs | Sort-Object StartTime -Descending | Select-Object -First 1
    }

    return $procs
}

function Get-ScaledCoordinates {
    param([int]$OriginalX, [int]$OriginalY)

    $screen = [System.Windows.Forms.Screen]::PrimaryScreen
    $adj = 0.75  # Adjust if UI scaling is off

    $scaleX = $screen.Bounds.Width  / $config.ReferenceResolution.Width  * $adj
    $scaleY = $screen.Bounds.Height / $config.ReferenceResolution.Height * $adj

    [pscustomobject]@{
        X = [math]::Round($OriginalX * $scaleX)
        Y = [math]::Round($OriginalY * $scaleY)
    }
}

function Invoke-MouseClick {
    param([int]$x, [int]$y)

    Write-Log "Moving cursor to $x,$y"
    [System.Windows.Forms.Cursor]::Position = [System.Drawing.Point]::new($x,$y)
    Start-Sleep -Milliseconds 200

    [Win32]::mouse_event([Win32]::MOUSEEVENTF_LEFTDOWN,0,0,0,0)
    Start-Sleep -Milliseconds 75
    [Win32]::mouse_event([Win32]::MOUSEEVENTF_LEFTUP,0,0,0,0)
    Start-Sleep -Milliseconds 100
}

# ---------------------------------------------------------------------------
#  Main sequence
# ---------------------------------------------------------------------------
Write-Log "Starting view toggle"

try {
    Write-Log "Searching for DesktopClient process..."
    $process = Get-TargetProcess
    if (-not $process) { return }

    Write-Log ("Found DesktopClient process with ID: {0}, hWnd: 0x{1:X}" -f $process.Id, $process.MainWindowHandle)

    # Bring to foreground
    for ($i = 1; $i -le 3; $i++) {
        if ([Win32]::SetForegroundWindow($process.MainWindowHandle)) {
            Write-Log "Window brought to foreground"
            break
        }
        Write-Log "Foreground attempt $i failed"
        Start-Sleep -Milliseconds 200
    }

    # Maximize
    [Win32]::ShowWindow($process.MainWindowHandle, [Win32]::SW_MAXIMIZE) | Out-Null
    Write-Log "Window maximized"
    Start-Sleep -Milliseconds 250

    # Click Alarm View
    $pt = Get-ScaledCoordinates -OriginalX $config.ClickCoordinates.AlarmView.X `
                                -OriginalY $config.ClickCoordinates.AlarmView.Y

    Write-Log ("Clicking Alarm View at {0},{1}" -f $pt.X, $pt.Y)
    $null = Invoke-MouseClick -x $pt.X -y $pt.Y

    Write-Log "Alarm View refreshed successfully"
}
catch {
    Write-Log ("ERROR: {0}" -f $_.Exception.Message)
    Write-Log ("STACK: {0}" -f $_.ScriptStackTrace)
}
