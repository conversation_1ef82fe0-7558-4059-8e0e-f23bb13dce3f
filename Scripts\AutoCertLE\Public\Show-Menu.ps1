<#
.SYNOPSIS
    Displays the main menu of the AutoCertLE module.

.DESCRIPTION
    Shows the main menu with options for certificate management.

.EXAMPLE
    Show-Menu
#>
function Show-Menu {
    [CmdletBinding()]
    param ()

    Clear-Host
    Initialize-ACMEServer
    Write-Host "`n=== Let's Encrypt Certificate Management for Salient CompleteView ===`n" -ForegroundColor Cyan
    Write-Host "Certificate Management:"
    Write-Host "1. Register a new certificate"
    Write-Host "2. Install certificate (generic)"
    Write-Host "3. Configure automatic renewal"
    Write-Host "4. View existing certificates"
    Write-Host "5. Revoke a certificate"
    Write-Host "6. Delete a certificate"

    Write-Host "`nSalient CompleteView:"
    Write-Host "7. Install certificate to Salient server"
    Write-Host "8. Distribute certificate to multiple Salient servers"

    Write-Host "`nMonitoring and Advanced:"
    Write-Host "9. Certificate monitoring dashboard"
    Write-Host "10. Advanced options"
    Write-Host "11. Exit`n"
}
