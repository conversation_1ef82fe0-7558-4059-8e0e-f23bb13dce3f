<#
.SYNOPSIS
    Automates the management of Let's Encrypt TLS certificates using Posh-ACME for CompleteView servers.

.DESCRIPTION
    This script provides a menu-driven interface for registering, installing,
    renewing, revoking, and deleting TLS certificates using the Posh-ACME module.

.NOTES
    Compatible with Posh-ACME version 4.x.
#>

#region Parameters
param(
    [switch]$RenewAll,
    [switch]$NonInteractive,
    [switch]$Force
)
#endregion

# Set up the log file
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$logFile = Join-Path -Path $scriptDir -ChildPath "AutoCertLE.log"

#region Core Utility Functions
# Function to log messages
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,
        [Parameter()]
        [ValidateSet('Info', 'Warning', 'Error', 'Success')]
        [string]$Level = 'Info'
    )

    # Only log if the message is meaningful
    if ([string]::IsNullOrWhiteSpace($Message)) {
        return
    }

    # Filter out routine informational messages
    if ($Level -eq 'Info') {
        $routinePatterns = @(
            'ACME server set to',
            'Certificate cache cleared',
            'User exited the script',
            'Selected certificate:',
            'Detecting DNS provider'
        )

        foreach ($pattern in $routinePatterns) {
            if ($Message -like "*$pattern*") {
                return
            }
        }
    }

    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    "$timestamp [$Level] $Message" | Out-File -FilePath $logFile -Append
}

# Retry operations with exponential backoff
function Invoke-WithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [scriptblock]$ScriptBlock,

        [Parameter()]
        [int]$MaxAttempts = 5,

        [Parameter()]
        [int]$InitialDelaySeconds = 2,

        [Parameter()]
        [double]$BackoffMultiplier = 2,

        [Parameter()]
        [string]$OperationName = "Operation",

        [Parameter()]
        [scriptblock]$SuccessCondition = { $true }
    )

    $attempt = 1
    $delay = $InitialDelaySeconds

    while ($attempt -le $MaxAttempts) {
        Write-Debug "Attempt $attempt of $MaxAttempts for $OperationName"
        try {
            $result = & $ScriptBlock

            if (& $SuccessCondition) {
                Write-Debug "$OperationName succeeded on attempt $attempt"
                return $result
            }

            Write-Verbose "$OperationName attempt ${attempt}: Condition not met, retrying..."
        }
        catch {
            Write-Verbose "$OperationName attempt $attempt failed: $($_.Exception.Message)"
        }

        if ($attempt -eq $MaxAttempts) {
            Write-Error "All $MaxAttempts attempts for $OperationName failed"
            throw "Failed to complete $OperationName after $MaxAttempts attempts"
        }

        Write-Debug "Waiting $delay seconds before next attempt"
        Start-Sleep -Seconds $delay
        $delay = [math]::Min($delay * $BackoffMultiplier, 60) # Cap at 60 seconds
        $attempt++
    }
}

# Ensure the ACME server is set
function Initialize-ACMEServer {
    Write-Debug "Checking ACME server configuration"
    if (-not (Get-PAServer)) {
        Write-Verbose "No ACME server configured. Setting to Let's Encrypt Production."
        Set-PAServer LE_PROD
        Write-Debug "ACME server set successfully"
    } else {
        Write-Debug "ACME server already configured"
    }
}

# Function to securely store credentials
function Set-SecureCredential {
    [CmdletBinding()]
    param (
        [string]$ProviderName,
        [pscredential]$Credential
    )
    $credDir = "$env:APPDATA\PoshACME\Creds"
    if (-not (Test-Path $credDir)) {
        New-Item -ItemType Directory -Path $credDir -Force | Out-Null
    }
    $credPath = "$credDir\$ProviderName.cred"
    try {
        $Credential | Export-Clixml -Path $credPath
    } catch {
        $msg = "Failed to save credentials for ${ProviderName} to '$credPath': $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
    }
}

# Function to retrieve secure credentials
function Get-SecureCredential {
    [CmdletBinding()]
    param (
        [string]$ProviderName
    )
    $credPath = "$env:APPDATA\PoshACME\Creds\$ProviderName.cred"
    if (Test-Path $credPath) {
        try {
            $cred = Import-Clixml -Path $credPath
            if ($null -eq $cred) { return $null }
            return $cred
        } catch {
            $msg = "Failed to import credentials for ${ProviderName} from '$credPath': $($_.Exception.Message)"
            Write-Error $msg
            Write-Log $msg -Level 'Error'
            return $null
        }
    }
    return $null
}

# Function to confirm actions
function Confirm-Action {
    [CmdletBinding()]
    param (
        [string]$Message
    )
    $response = Read-Host "$Message (Y/N)"
    return $response -match '^[Yy]$'
}

# Function for progress reporting
function Write-ProgressHelper {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Activity,
        [Parameter()]
        [string]$Status = "In Progress",
        [Parameter()]
        [int]$PercentComplete,
        [Parameter()]
        [string]$CurrentOperation,
        [Parameter()]
        [int]$StepNumber,
        [Parameter()]
        [int]$TotalSteps
    )

    if ($StepNumber -and $TotalSteps) {
        $PercentComplete = ($StepNumber / $TotalSteps) * 100
    }

    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete -CurrentOperation $CurrentOperation
}
#endregion

#region Validation Functions
# Function to validate integer input within a range
function Get-ValidatedInput {
    [CmdletBinding()]
    param (
        [string]$Prompt,
        [int[]]$ValidOptions
    )
    do {
        $formattedPrompt = "${Prompt}: "
        $userInput = Read-Host $formattedPrompt
        if ([int]::TryParse($userInput, [ref]$null) -and $ValidOptions -contains [int]$userInput) {
            return [int]$userInput
        } elseif ($userInput -eq '0') {
            return 0
        } else {
            $validChoices = ($ValidOptions | Sort-Object) -join ', '
            Write-Warning "Please enter a valid option ($validChoices) or 0 to go back."
        }
    } while ($true)
}

# Function to validate file paths
function Test-ValidPath {
    [CmdletBinding()]
    param (
        [string]$Path,
        [switch]$IsDirectory,
        [switch]$MustExist,
        [switch]$MustNotExist,
        [switch]$RequireWrite
    )

    try {
        # Check if path is null or empty
        if ([string]::IsNullOrWhiteSpace($Path)) {
            Write-Warning "Path cannot be empty."
            return $false
        }

        # Check for invalid characters
        $invalidChars = [System.IO.Path]::GetInvalidPathChars()
        if ($Path.IndexOfAny($invalidChars) -ge 0) {
            Write-Warning "Path contains invalid characters."
            return $false
        }

        # Check if the path exists
        if ($MustExist -and -not (Test-Path $Path)) {
            Write-Warning "Path does not exist: $Path"
            return $false
        }

        # Check if the path must not exist
        if ($MustNotExist -and (Test-Path $Path)) {
            Write-Warning "Path already exists: $Path"
            return $false
        }

        # Check if the path is a directory
        if ($IsDirectory -and -not (Test-Path $Path -PathType Container)) {
            Write-Warning "Path is not a directory: $Path"
            return $false
        }

        # Check if the path is a file
        if (-not $IsDirectory -and -not (Test-Path $Path -PathType Leaf)) {
            Write-Warning "Path is not a file: $Path"
            return $false
        }

        # Check if the path is writable
        if ($RequireWrite) {
            $testFile = [System.IO.Path]::Combine($Path, [System.IO.Path]::GetRandomFileName())
            try {
                [System.IO.File]::Create($testFile).Dispose()
                [System.IO.File]::Delete($testFile)
            } catch {
                Write-Warning "Path is not writable: $Path"
                return $false
            }
        }

        return $true
    } catch {
        Write-Warning "An error occurred while validating the path: $($_)"
        return $false
    }
}

# Function to validate email addresses
function Test-ValidEmail {
    [CmdletBinding()]
    param (
        [string]$Email
    )

    if ([string]::IsNullOrWhiteSpace($Email)) {
        Write-Warning "Email address cannot be empty."
        return $false
    }

    if ($Email -notmatch '^[\w\.-]+@[\w\.-]+\.\w+$') {
        Write-Warning "Invalid email address format: $Email"
        return $false
    }

    return $true
}

# Function to validate domain names
function Test-ValidDomain {
    [CmdletBinding()]
    param (
        [string]$Domain
    )

    if ([string]::IsNullOrWhiteSpace($Domain)) {
        Write-Warning "Domain name cannot be empty."
        return $false
    }

    if ($Domain -notmatch '^[a-zA-Z0-9.-]+$') {
        Write-Warning "Invalid domain name format: $Domain"
        return $false
    }

    return $true
}

# Function to validate plugin parameters
function Test-PluginParameters {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Plugin,
        [Parameter(Mandatory = $true)]
        [hashtable]$Parameters
    )

    $validationRules = @{
        'Cloudflare' = @{
            'CFToken' = '^[a-zA-Z0-9_-]{40,}$'
        }
        'Route53' = @{
            'ProfileName' = '^[a-zA-Z0-9_-]+$'
            'AccessKey' = '^[A-Z0-9]{20}$'
            'SecretKey' = '^[a-zA-Z0-9/+]{40}$'
        }
        'Azure' = @{
            'SubscriptionId' = '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$'
            'TenantId' = '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$'
        }
    }

    if (-not $validationRules.ContainsKey($Plugin)) {
        Write-Debug "No validation rules defined for plugin: $Plugin"
        return $true
    }

    $rules = $validationRules[$Plugin]
    $isValid = $true

    foreach ($param in $Parameters.GetEnumerator()) {
        if ($rules.ContainsKey($param.Key)) {
            if ($param.Value -notmatch $rules[$param.Key]) {
                Write-Error "Invalid format for $($param.Key) in $Plugin plugin"
                $isValid = $false
            }
        }
    }

    return $isValid
}
#endregion

#region Configuration Management
# Function to get renewal configuration
function Get-RenewalConfig {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$ConfigPath = "$env:LOCALAPPDATA\PoshACME\renewal_config.json"
    )

    if (Test-Path $ConfigPath) {
        try {
            $config = Get-Content $ConfigPath | ConvertFrom-Json
            return $config
        } catch {
            Write-Warning "Failed to load renewal configuration: $($_)"
            Write-Log "Failed to load renewal configuration: $($_)" -Level 'Warning'
        }
    }

    # Return default configuration
    return @{
        RenewalHour = 2  # 2 AM
        RenewalMinute = (Get-Random -Minimum 0 -Maximum 59)  # Random minute
        UseRandomization = $true
        RandomizationWindow = 60  # minutes
    }
}

# Function to save renewal configuration
function Save-RenewalConfig {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Config,
        [Parameter()]
        [string]$ConfigPath = "$env:LOCALAPPDATA\PoshACME\renewal_config.json"
    )

    try {
        $Config | ConvertTo-Json | Set-Content -Path $ConfigPath
        return $true
    } catch {
        $msg = "Failed to save renewal configuration to '$ConfigPath': $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        return $false
    }
}

# Function to get script settings
function Get-ScriptSettings {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\script_settings.json"
    )

    if (Test-Path $SettingsPath) {
        try {
            $settings = Get-Content $SettingsPath -Raw | ConvertFrom-Json
            return $settings
        } catch {
            Write-Warning "Failed to load settings: $($_)"
            Write-Log "Failed to load settings: $($_)" -Level 'Warning'
        }
    }

    # Return default settings
    return @{
        DefaultDNSPlugin = 'Manual'
        CloudflareToken = $null
        AWSProfile = ''
        AzureSubscriptionId = ''
        AzureTenantId = ''
        LastUsedEmail = ''
        DefaultCertPath = [Environment]::GetFolderPath("Desktop")
        AlwaysExportable = $true
        PreferredInstallLocation = 'ManagementServer'
        DefaultPEMLocation = ''
        DefaultPFXLocation = [Environment]::GetFolderPath("Desktop")
    }
}

# Function to save script settings
function Save-ScriptSettings {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Settings,
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\script_settings.json"
    )

    try {
        $Settings | ConvertTo-Json | Set-Content -Path $SettingsPath
        return $true
    } catch {
        Write-Warning "Failed to save settings: $($_)"
        Write-Log "Failed to save settings: $($_)" -Level 'Warning'
        return $false
    }
}
#endregion

#region Certificate Cache Functions
# Function to get cache file path
function Get-CacheFilePath {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain
    )
    $cacheDir = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Posh-ACME\cache"
    if (-not (Test-Path $cacheDir)) {
        New-Item -ItemType Directory -Path $cacheDir -Force | Out-Null
    }
    return Join-Path -Path $cacheDir -ChildPath "$($MainDomain.Replace('*', '_wild_')).json"
}

# Function to get cached certificate
function Get-CachedPACertificate {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain,
        [switch]$Force
    )

    Write-Debug "Attempting to retrieve certificate for $MainDomain"
    $cachePath = Get-CacheFilePath -MainDomain $MainDomain

    if (-not $Force -and (Test-Path $cachePath)) {
        try {
            $cacheData = Get-Content -Path $cachePath -Raw | ConvertFrom-Json
            if ((Get-Date) -lt [DateTime]::Parse($cacheData.ExpiryTime)) {
                Write-Verbose "Retrieved certificate from cache for $MainDomain"
                return $cacheData.Certificate
            }
        } catch {
            Write-Debug "Cache read failed: $($_.Exception.Message)"
        }
    }

    Write-Verbose "Fetching fresh certificate for $MainDomain"
    try {
        $maxAttempts = 3
        $attempt = 1
        $lastError = $null

        while ($attempt -le $maxAttempts) {
            try {
                $cert = Get-PACertificate -MainDomain $MainDomain -ErrorAction Stop
                if ($null -eq $cert) {
                    throw "Get-PACertificate returned null"
                }

                # Save to cache with file lock handling
                $cacheData = @{
                    Certificate = $cert
                    ExpiryTime = (Get-Date).AddMinutes(30).ToString('o')
                }

                Invoke-WithRetry -ScriptBlock {
                    $cacheData | ConvertTo-Json | Set-Content -Path $cachePath -Force
                } -MaxAttempts 3 -InitialDelaySeconds 1 `
                  -OperationName "Cache write for $MainDomain"

                return $cert
            } catch {
                $lastError = $_
                Write-Debug "Attempt $attempt failed: $($_.Exception.Message)"
                $attempt++
                if ($attempt -le $maxAttempts) {
                    Start-Sleep -Seconds (2 * $attempt)
                }
            }
        }
        throw "Failed to retrieve certificate after $maxAttempts attempts: $($lastError.Exception.Message)"
    } catch {
        Write-Error "Critical error retrieving certificate for ${MainDomain}: $($_.Exception.Message)"
        Write-Log "Critical error retrieving certificate for ${MainDomain}: $($_.Exception.Message)" -Level 'Error'
        throw
    }
}

# Function to clear the certificate cache
function Clear-CertificateCache {
    [CmdletBinding()]
    param ()
    Write-Debug "Clearing certificate cache"
    $cacheDir = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Posh-ACME\cache"
    if (Test-Path $cacheDir) {
        Get-ChildItem -Path $cacheDir -Filter "*.json" | Remove-Item -Force
    }
    Write-Verbose "Certificate cache cleared"
}
#endregion

#region Domain and Certificate File Functions
# Function to load and parse the public suffix list
function Get-PublicSuffixList {
    [CmdletBinding()]
    param (
        [string]$Url = "https://publicsuffix.org/list/public_suffix_list.dat"
    )
    $cacheDir = "$env:LOCALAPPDATA\PoshACME"
    $cachePath = "$cacheDir\public_suffix_list.dat"

    # Ensure the cache directory exists
    if (-not (Test-Path $cacheDir)) {
        New-Item -ItemType Directory -Path $cacheDir -Force | Out-Null
    }

    if (-not (Test-Path $cachePath -PathType Leaf) -or ((Get-Date) - (Get-Item $cachePath).LastWriteTime).TotalDays -gt 7) {
        Write-ProgressHelper -Activity "Updating Public Suffix List" -Status "Downloading latest list..."
        try {
            $wc = New-Object System.Net.WebClient
            $wc.DownloadProgressChanged = {
                param($send, $e)
                Write-ProgressHelper -Activity "Downloading Public Suffix List" `
                    -Status "Downloaded: $([math]::Round($e.BytesReceived/1KB, 2)) KB" `
                    -PercentComplete $e.ProgressPercentage
            }
            $wc.DownloadFileTaskAsync($Url, $cachePath).Wait()
        } catch {
            Write-Error "Failed to download public suffix list: $($_)"
            Write-Log "Failed to download public suffix list: $($_)" -Level 'Error'
            return @()
        }
    }

    try {
        $suffixes = Get-Content -Path $cachePath | Where-Object {
            $_ -and -not $_.StartsWith("//")
        }
        return $suffixes
    } catch {
        Write-Error "Failed to load public suffix list: $($_)"
        Write-Log "Failed to load public suffix list: $($_)" -Level 'Error'
        return @()
    }
}

# Function to extract the base domain using the public suffix list
function Get-BaseDomain {
    [CmdletBinding()]
    param (
        [string]$domainName,
        [string[]]$Suffixes
    )
    if ([string]::IsNullOrWhiteSpace($domainName)) {
        Write-Warning "Domain name is empty."
        return $null
    }
    if ($null -eq $Suffixes -or $Suffixes.Count -eq 0) {
        Write-Warning "Suffixes list is empty."
        return $domainName
    }
    $domainLabels = $domainName.ToLower().Split('.')
    for ($i = 0; $i -lt $domainLabels.Length; $i++) {
        $candidate = ($domainLabels[$i..($domainLabels.Length - 1)] -join '.')
        if ($Suffixes -contains $candidate) {
            if ($i -gt 0) {
                $registeredDomain = ($domainLabels[($i - 1)..($domainLabels.Length - 1)] -join '.')
                return $registeredDomain
            } else {
                return $domainName
            }
        }
    }
    return $domainName
}

# Get next file version from Recording Server certificate folder
function Get-NextFileVersion {
    [CmdletBinding()]
    param(
        [string]$folderPath,
        [string]$baseName, # 'cert' or 'pvkey'
        [string]$extension = ".pem"
    )
    $latestVersion = -1
    $files = Get-ChildItem -Path $folderPath -Filter "$baseName*${extension}"
    foreach ($file in $files) {
        if ($file.Name -match "${baseName}(\d+)$extension") {
            [int]$versionNumber = $Matches[1]
            if ($versionNumber -gt $latestVersion) {
                $latestVersion = $versionNumber
            }
        }
    }
    return ($latestVersion + 1).ToString("D3")
}

# Get Recording Server certificate folder path
function Get-RSCertFolder {
    [CmdletBinding()]
    param ()
    $certFolderPaths = @(
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
        "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates"
    )
    foreach ($path in $certFolderPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    Write-Error "Failed to find any predefined certificate folders."
    Write-Log "Failed to find any predefined certificate folders." -Level 'Error'
    return $null
}

# Save PEM files with auto-versioning
function Save-PEMFiles {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$directory,
        [Parameter(Mandatory = $true)]
        [string]$certContent,
        [Parameter(Mandatory = $true)]
        [string]$keyContent,
        [Parameter()]
        [switch]$NoVersioning
    )

    Write-Debug "Saving PEM files to $directory"
    if (-not (Test-Path $directory)) {
        $msg = "Certificate directory does not exist: '$directory'"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        return $null
    }

    try {
        # Get next version with retry for file system operations
        if (-not $NoVersioning) {
            $certVersion = Invoke-WithRetry -ScriptBlock {
                Get-NextFileVersion -folderPath $directory -baseName "cert"
            } -MaxAttempts 3 -InitialDelaySeconds 1 `
              -OperationName "Version number generation"

            $certOutputFile = Join-Path -Path $directory -ChildPath ("cert" + $certVersion + ".pem")
            $keyOutputFile = Join-Path -Path $directory -ChildPath ("pvkey" + $certVersion + ".pem")
        } else {
            $certOutputFile = Join-Path -Path $directory -ChildPath "cert.pem"
            $keyOutputFile = Join-Path -Path $directory -ChildPath "pvkey.pem"
        }

        # Save files with retry for locked files
        Invoke-WithRetry -ScriptBlock {
            Set-Content -Path $certOutputFile -Value $certContent -Encoding ascii -ErrorAction Stop
            Set-Content -Path $keyOutputFile -Value $keyContent -Encoding ascii -ErrorAction Stop
        } -MaxAttempts 5 -InitialDelaySeconds 2 `
          -OperationName "PEM file save" `
          -SuccessCondition { Test-Path $certOutputFile -and Test-Path $keyOutputFile }

        return @{
            CertFile = $certOutputFile
            KeyFile = $keyOutputFile
        }
    } catch {
        $msg = "Failed to save PEM files to '$directory' after multiple attempts. Certificate: '$certOutputFile', Key: '$keyOutputFile'. Error: $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        throw
    }
}

# Function to get certificate PEM content
function Get-CertificatePEMContent {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        [Parameter()]
        [switch]$IncludeKey
    )

    $result = @{
        CertContent = $null
        KeyContent = $null
        Success = $false
        ErrorMessage = $null
    }

    try {
        # Get certificate content
        if ($Certificate.CertificatePEM) {
            $result.CertContent = Get-Content -Path $Certificate.CertificatePEM -Raw
        } elseif ($Certificate.PEM) {
            $result.CertContent = $Certificate.PEM
        } elseif ($Certificate.CertFile) {
            $result.CertContent = Get-Content -Path $Certificate.CertFile -Raw
        } else {
            throw "Unable to retrieve PEM content from certificate object."
        }

        # Get key content if requested
        if ($IncludeKey) {
            if ($Certificate.KeyFile) {
                $result.KeyContent = Get-Content -Path $Certificate.KeyFile -Raw
            } else {
                throw "Unable to retrieve key content from certificate object."
            }
        }

        $result.Success = $true
    } catch {
        $result.ErrorMessage = $_.Exception.Message
        Write-Error $result.ErrorMessage
        Write-Log $result.ErrorMessage -Level 'Error'
    }

    return $result
}
#endregion

#region Revoked Certificate Management
# Path to the file storing revoked certificates
$RevokedCertsFile = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Posh-ACME\revoked_certs.json"

# Function to load revoked certificates
function Get-RevokedCertificates {
    [CmdletBinding()]
    param ()
    if (Test-Path $RevokedCertsFile) {
        try {
            $revokedCerts = Get-Content $RevokedCertsFile | ConvertFrom-Json
        } catch {
            Write-Warning "Failed to load revoked certificates: $($_)"
            Write-Log "Failed to load revoked certificates: $($_)" -Level 'Warning'
            $revokedCerts = @()
        }
    } else {
        $revokedCerts = @()
    }
    return $revokedCerts
}

# Function to save revoked certificates
function Save-RevokedCertificates {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$revokedCerts
    )
    try {
        $revokedCerts | ConvertTo-Json | Set-Content -Path $RevokedCertsFile
    } catch {
        Write-Warning "Failed to save revoked certificates: $($_)"
        Write-Log "Failed to save revoked certificates: $($_)" -Level 'Warning'
    }
}
#endregion

#region Configuration Management
# Function to get renewal configuration
function Get-RenewalConfig {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$ConfigPath = "$env:LOCALAPPDATA\PoshACME\renewal_config.json"
    )

    if (Test-Path $ConfigPath) {
        try {
            $config = Get-Content $ConfigPath | ConvertFrom-Json
            return $config
        } catch {
            Write-Warning "Failed to load renewal configuration: $($_)"
            Write-Log "Failed to load renewal configuration: $($_)" -Level 'Warning'
        }
    }

    # Return default configuration
    return @{
        RenewalHour = 2  # 2 AM
        RenewalMinute = (Get-Random -Minimum 0 -Maximum 59)  # Random minute
        UseRandomization = $true
        RandomizationWindow = 60  # minutes
    }
}

# Function to save renewal configuration
function Save-RenewalConfig {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Config,
        [Parameter()]
        [string]$ConfigPath = "$env:LOCALAPPDATA\PoshACME\renewal_config.json"
    )

    try {
        $Config | ConvertTo-Json | Set-Content -Path $ConfigPath
        return $true
    } catch {
        $msg = "Failed to save renewal configuration to '$ConfigPath': $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        return $false
    }
}

# Function to get script settings
function Get-ScriptSettings {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\script_settings.json"
    )

    if (Test-Path $SettingsPath) {
        try {
            $settings = Get-Content $SettingsPath -Raw | ConvertFrom-Json
            return $settings
        } catch {
            Write-Warning "Failed to load settings: $($_)"
            Write-Log "Failed to load settings: $($_)" -Level 'Warning'
        }
    }

    # Return default settings
    return @{
        DefaultDNSPlugin = 'Manual'
        CloudflareToken = $null
        AWSProfile = ''
        AzureSubscriptionId = ''
        AzureTenantId = ''
        LastUsedEmail = ''
        DefaultCertPath = [Environment]::GetFolderPath("Desktop")
        AlwaysExportable = $true
        PreferredInstallLocation = 'ManagementServer'
        DefaultPEMLocation = ''
        DefaultPFXLocation = [Environment]::GetFolderPath("Desktop")
    }
}

# Function to save script settings
function Save-ScriptSettings {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Settings,
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\script_settings.json"
    )

    try {
        $Settings | ConvertTo-Json | Set-Content -Path $SettingsPath
        return $true
    } catch {
        Write-Warning "Failed to save settings: $($_)"
        Write-Log "Failed to save settings: $($_)" -Level 'Warning'
        return $false
    }
}
#endregion

#region Certificate Management Functions
# Function to install a certificate
function Install-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true)]
    param(
        [Parameter()]
        [object]$PACertificate,
        [Parameter()]
        [switch]$Force
    )

    Initialize-ACMEServer

    if ($null -eq $PACertificate) {
        Clear-CertificateCache
        $orders = Get-PAOrder
        if ($null -eq $orders -or $orders.Count -eq 0) {
            Write-Warning "`nNo certificates available to install."
            Read-Host "`nPress Enter to return to the main menu"
            return
        }

        $certs = @()
        foreach ($order in $orders) {
            $cert = Get-CachedPACertificate -MainDomain $order.MainDomain
            if ($null -ne $cert) {
                $certs += $cert
            }
        }

        if ($certs.Count -eq 0) {
            Write-Warning "`nNo certificates available to install."
            Read-Host "`nPress Enter to return to the main menu"
            return
        }

        Write-Host "`nSelect the certificate you want to install:"
        $i = 1
        foreach ($cert in $certs) {
            Write-Host "$i) $($cert.MainDomain)"
            $i++
        }
        Write-Host "0) Back"
        $selection = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice (0-$($certs.Count))" -ValidOptions (1..$certs.Count)
        if ($selection -eq 0) {
            return
        } else {
            $PACertificate = $certs[$selection - 1]
        }
    }

    # Installation options
    $installed = $false
    while (-not $installed) {
        Write-Host "`nCertificate for $($PACertificate.MainDomain) selected."

        # Present the options to the user
        Write-Host "`nSelect how you want to install or save the certificate:"
        Write-Host "1) Install on Management Server (install to local computer certificate store)"
        Write-Host "2) Install on Recording Server (convert to PEM and save to appropriate directory)"
        Write-Host "3) Save certificate to Desktop (PFX file)"
        Write-Host "0) Back"
        $installChoice = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice (0-3)" -ValidOptions 1,2,3
        switch ($installChoice) {
            0 { return }
            1 {
                # Install on Management Server using Install-PACertificate
                Write-Host "`nInstalling certificate on Management Server..."

                # Prompt for whether the private key should be exportable
                $exportableChoice = Read-Host "`nDo you want the private key to be exportable? (Y/N) or 0 to go back"
                if ($exportableChoice -eq '0') {
                    continue
                }
                $isNotExportable = $exportableChoice -match '^(N|n)$'

                try {
                    # Prepare parameters for Install-PACertificate
                    $installParams = @{
                        PACertificate = $PACertificate
                        StoreLocation = 'LocalMachine'
                        Verbose       = $true
                    }
                    if ($isNotExportable) {
                        $installParams['NotExportable'] = $true
                    }
                    # Install the certificate to the LocalMachine\My store
                    Invoke-WithRetry -ScriptBlock {
                        Install-PACertificate @installParams
                    } -MaxAttempts 3 -InitialDelaySeconds 2 `
                      -OperationName "Certificate installation" `
                      -SuccessCondition { $? }

                    Write-Host "`nCertificate installed to LocalMachine\My certificate store."
                    Write-Log "Certificate installed to LocalMachine\My for $($PACertificate.MainDomain)"
                } catch {
                    $msg = "Failed to install certificate to LocalMachine\My store after multiple attempts. Certificate: $($PACertificate.MainDomain), Error: $($_.Exception.Message)"
                    Write-Error $msg
                    Write-Log $msg -Level 'Error'
                }
                $installed = $true
            }
            2 {
                # Install on Recording Server
                Write-Host "`nInstalling certificate on Recording Server..."
                $certDir = Get-RSCertFolder
                if ($null -eq $certDir) {
                    return
                }

                # Get both certificate and key content
                $pemContent = Get-CertificatePEMContent -Certificate $PACertificate -IncludeKey
                if (-not $pemContent.Success) {
                    return
                }

                # Save PEM files with auto-versioning
                $result = Save-PEMFiles -directory $certDir `
                    -certContent $pemContent.CertContent `
                    -keyContent $pemContent.KeyContent

                if ($result) {
                    Write-Host "`nCertificate and private key saved to:"
                    Write-Host "Certificate: $($result.CertFile)"
                    Write-Host "Private Key: $($result.KeyFile)"
                    Write-Log "Certificate and private key saved to Recording Server directory."
                }
                $installed = $true
            }
            3 {
                # Save certificate to Desktop as PFX
                Write-Host "`nSaving certificate to Desktop as PFX file..."
                $desktopPath = [Environment]::GetFolderPath("Desktop")
                $defaultCertPath = Join-Path $desktopPath "certificate.pfx"
                $certPath = Read-Host "`nEnter the path to save the certificate (default: $defaultCertPath) or 0 to go back"
                if ($certPath -eq '0') {
                    continue
                }
                if (-not $certPath) {
                    $certPath = $defaultCertPath
                }
                if (-not (Test-ValidPath -Path $certPath -MustNotExist)) {
                    continue
                }
                $certPassword = Read-Host "`nEnter a password for the PFX file (leave blank for no password) or 0 to go back" -AsSecureString
                if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($certPassword)) -eq '0') {
                    continue
                }
                try {
                    Export-PACertificate -MainDomain $PACertificate.MainDomain -Type PFX -Path $certPath -Password $certPassword
                    Write-Host "`nCertificate saved to $certPath"
                    Write-Log "Certificate saved to $certPath"
                } catch {
                    $msg = "Failed to export certificate as PFX to '$certPath'. Certificate: $($PACertificate.MainDomain), Error: $($_.Exception.Message)"
                    Write-Error $msg
                    Write-Log $msg -Level 'Error'
                }
                $installed = $true
            }
            default {
                Write-Warning "`nInvalid selection. Please choose 0-3."
            }
        }
    }
    return
}

# Function to register a new certificate
function Register-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
    param (
        [Parameter()]
        [switch]$Force
    )
    <#
    .SYNOPSIS
        Registers a new TLS certificate using the specified plugin.

    .DESCRIPTION
        Handles the registration of a new TLS certificate, including domain validation
        and plugin configuration.

    .EXAMPLE
        Register-Certificate
    #>

    # Ensure the ACME server is set
    Initialize-ACMEServer

    # Load the public suffix list
    $publicSuffixes = Get-PublicSuffixList

    # Prompt for domain name
    $domain = Read-Host "`nEnter the domain name (e.g., server.domain.com) or 0 to go back"
    if ($domain -eq '0') {
        return
    }

    # Validate domain name format
    if (-not (Test-ValidDomain -Domain $domain)) {
        return
    }

    # Extract base domain using public suffix list
    $baseDomain = Get-BaseDomain -domainName $domain -Suffixes $publicSuffixes

    # Initialize variables
    $mainDomain = $domain
    $domains = @()

    # Ask if the user wants a server-specific certificate or a wildcard certificate
    while ($true) {
        Write-Host "`nSelect the type of certificate you want to create:"
        Write-Host "1) Server-specific certificate for $domain"
        Write-Host "2) Wildcard certificate for *.$baseDomain"
        Write-Host "0) Back"
        $certTypeChoice = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice (0-2)" -ValidOptions 1,2
        if ($certTypeChoice -eq 0) {
            return
        } elseif ($certTypeChoice -eq 1) {
            $mainDomain = $domain
            $domains = @($mainDomain)
            break
        } elseif ($certTypeChoice -eq 2) {
            $mainDomain = "*.$baseDomain"
            $domains = @($mainDomain)
            break
        }
    }

    # Attempt to auto-detect DNS provider
    Write-Host "`nDetecting DNS provider for $baseDomain..."
    try {
        $nsRecords = (Resolve-DnsName -Name $baseDomain -Type NS -ErrorAction Stop).NameHost
        $nsProvider = $null

        foreach ($ns in $nsRecords) {
            if ($ns -like "*.cloudflare.com") {
                $nsProvider = "Cloudflare"
                $plugin = 'Cloudflare'
                break
            } elseif ($ns -like "*.awsdns-*.*.amazonaws.com") {
                $nsProvider = "AWSRoute53"
                $plugin = 'Route53'
                break
            } elseif ($ns -like "*.azure-dns*.info" -or $ns -like "*.azure-dns*.org" -or $ns -like "*.azure-dns*.com" -or $ns -like "*.azure-dns*.net") {
                $nsProvider = "Azure"
                $plugin = 'Azure'
                break
            } elseif ($ns -like "*.googledomains.com") {
                $nsProvider = "Google"
                $plugin = 'GoogleDomains'
                break
            } elseif ($ns -like "*.digitalocean.com") {
                $nsProvider = "DigitalOcean"
                $plugin = 'DigitalOcean'
                break
            } elseif ($ns -like "*.dnsmadeeasy.com") {
                $nsProvider = "DNSMadeEasy"
                $plugin = 'DNSMadeEasy'
                break
            }
            # Add more providers as needed
        }

        if ($nsProvider) {
            Write-Host "`nDetected DNS provider: $nsProvider" -ForegroundColor Green
            Write-Log "Detected DNS provider: $nsProvider"
        } else {
            Write-Warning "`nDNS provider could not be automatically detected."
            $plugin = $null
        }
    } catch {
        Write-Warning "`nFailed to retrieve NS records for ${baseDomain}: $($_)"
        Write-Log "Failed to retrieve NS records for ${baseDomain}: $($_)" -Level 'Warning'
        $plugin = $null
    }

    # If DNS provider not detected, prompt user to select the challenge plugin
    if (-not $plugin) {
        $pluginSelected = $false
        while (-not $pluginSelected) {
            Write-Host "`nSelect the challenge plugin:"
            Write-Host "1) DNS - Cloudflare"
            Write-Host "2) DNS - AWS Route53"
            Write-Host "3) DNS - Azure"
            Write-Host "4) Manual (default)"
            Write-Host "5) Other DNS Plugin"
            Write-Host "0) Back"
            $pluginOption = Get-ValidatedInput -Prompt "`nEnter the corresponding number (0-5)" -ValidOptions 1,2,3,4,5
            switch ($pluginOption) {
                0 { return }
                1 { $plugin = 'Cloudflare'; $pluginSelected = $true }
                2 { $plugin = 'Route53'; $pluginSelected = $true }
                3 { $plugin = 'Azure'; $pluginSelected = $true }
                4 { $plugin = 'Manual'; $pluginSelected = $true }
                5 {
                    # List all available DNS plugins
                    $plugins = @(Get-PAPlugin | Where-Object { $_.ChallengeType -eq 'dns-01' })
                    if ($plugins.Count -eq 0) {
                        Write-Warning "`nNo DNS plugins are available."
                        $plugin = 'Manual'
                        $pluginSelected = $true
                    } else {
                        $dnsPluginSelected = $false
                        while (-not $dnsPluginSelected) {
                            Write-Host "`nAvailable DNS Plugins:"
                            $i = 1
                            foreach ($p in $plugins) {
                                Write-Host "$i) $($p.Name)"
                                $i++
                            }
                            Write-Host "0) Back"
                            $pluginSelection = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice" -ValidOptions (1..$plugins.Count)
                            if ($pluginSelection -eq 0) {
                                break
                            } else {
                                $plugin = $plugins[$pluginSelection - 1].Name
                                $pluginSelected = $true
                                $dnsPluginSelected = $true
                            }
                        }
                    }
                }
            }
        }
    }

    # Ensure an ACME account exists
    if (-not (Get-PAAccount)) {
        Write-Host "`nNo ACME account found. Creating a new account..."
        $email = Read-Host "`nEnter your email address for Let's Encrypt notifications or 0 to go back"
        if ($email -eq '0') {
            return
        }
        if (-not (Test-ValidEmail -Email $email)) {
            return
        }
        try {
            New-PAAccount -AcceptTOS -Contact $email -ErrorAction Stop
            Write-Host "`nACME account created."
            Write-Log "ACME account created with email: $email"
        } catch {
            Write-Error "Failed to create ACME account: $($_)"
            Write-Log "Failed to create ACME account: $($_)" -Level 'Error'
            return
        }
    }

    # Initialize plugin arguments
    $pluginArgs = @{}

    # Handle plugin-specific authentication
    if ($plugin -eq 'Cloudflare') {
        $cred = Get-SecureCredential -ProviderName 'Cloudflare'
        if ($null -eq $cred) {
            Write-Host "`nCloudflare credentials not found. Opening browser for API token creation..."
            Start-Process "https://dash.cloudflare.com/profile/api-tokens"
            Write-Host "`nPlease follow these steps to create an API Token:`n" -ForegroundColor Cyan
            Write-Host "1. Log in to your Cloudflare account."
            Write-Host "2. Navigate to 'My Profile' > 'API Tokens'."
            Write-Host "3. Click 'Create Token'."
            Write-Host "4. Select 'Create Custom Token'."
            Write-Host "5. In the 'Permissions' section, add the following permissions:"
            Write-Host "   - Zone:DNS:Edit"
            Write-Host "6. In the 'Zone Resources' section, select the appropriate option:"
            Write-Host "   - Include: All zones or specify the specific zone."
            Write-Host "7. Give your token a name and click 'Continue to summary'."
            Write-Host "8. Review the details and click 'Create Token'."
            Write-Host "9. Copy the API Token displayed. This will be used in the script."

            $cfToken = Read-Host "`nEnter your Cloudflare API Token or 0 to go back" -AsSecureString
            if ([System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($cfToken)) -eq '0') {
                return
            }
            $cfCredential = New-Object System.Management.Automation.PSCredential ('CFToken', $cfToken)
            Set-SecureCredential -ProviderName 'Cloudflare' -Credential $cfCredential
        } else {
            $cfToken = $cred.Password
        }
        $pluginArgs = @{ CFToken = $cfToken }
    } elseif ($plugin -eq 'Route53') {
        $awsProfile = Read-Host "`nEnter your AWS profile name (leave blank for default) or 0 to go back"
        if ($awsProfile -eq '0') {
            return
        }
        if ($awsProfile) { $pluginArgs = @{ ProfileName = $awsProfile } }
    } elseif ($plugin -eq 'Azure') {
        # Use interactive authentication for Azure
        Write-Host "`nAuthenticating with Azure..."
        if (-not (Get-Module -ListAvailable -Name Az.Accounts)) {
            Write-Host "`nAz.Accounts module not found. Installing..."
            try {
                Install-Module -Name Az.Accounts -Scope CurrentUser -Force -ErrorAction Stop
                Import-Module Az.Accounts
            } catch {
                $msg = "Failed to install Az.Accounts module. NuGet provider status: $($_.Exception.Message)"
                Write-Error $msg
                Write-Log $msg -Level 'Error'
                return
            }
        } else {
            Import-Module Az.Accounts
        }
        try {
            Invoke-WithRetry -ScriptBlock {
                Connect-AzAccount -UseDeviceAuthentication -ErrorAction Stop
            } -MaxAttempts 3 -InitialDelaySeconds 5 `
              -OperationName "Azure authentication" `
              -SuccessCondition { Get-AzContext }

            $azContext = Get-AzContext
            $pluginArgs = @{
                SubscriptionId = $azContext.Subscription.Id
                TenantId       = $azContext.Tenant.Id
            }
        } catch {
            Write-Error "Failed to authenticate with Azure after multiple attempts: $($_)"
            Write-Log "Failed to authenticate with Azure after multiple attempts: $($_)" -Level 'Error'
            return
        }
    } elseif ($plugin -eq 'Manual') {
        Write-Host "`nManual challenge selected. You will need to create DNS TXT records manually."
    } else {
        # Handle other plugins
        Write-Host "`nYou selected the $plugin plugin."

        # Prompt to view the plugin's guide
        $viewGuide = Read-Host "`nWould you like to view the $plugin plugin guide? (Y/N)"
        if ($viewGuide -match '^(Y|y)$') {
            $guideUrl = "https://poshac.me/docs/v4/Plugins/$plugin/"
            Start-Process $guideUrl
        }

        # Retrieve plugin parameter information
        $pluginInfo = Get-PAPlugin -Plugin $plugin
        $pluginParams = $pluginInfo.Params

        if ($pluginParams) {
            Write-Host "`nThe $plugin plugin requires the following parameters:"
            # Initialize an empty hashtable for plugin arguments
            $pluginArgs = @{}

            # Loop through each parameter and prompt the user for input
            foreach ($param in $pluginParams) {
                do {
                    $paramValue = Read-Host "`nEnter value for '$param' or 0 to go back"
                    if ($paramValue -eq '0') { return }
                    $pluginArgs[$param] = $paramValue

                    if (-not (Test-PluginParameters -Plugin $plugin -Parameters $pluginArgs)) {
                        Write-Warning "Invalid parameter format. Please try again."
                        $pluginArgs.Remove($param)
                        continue
                    }
                    break
                } while ($true)
            }
        } else {
            Write-Host "`nThe $plugin plugin does not require any parameters."
            $pluginArgs = @{}
        }
    }

    # Submit the certificate order
    Write-Host "`nRequesting certificate for domain(s): $($domains -join ', ')"
    Write-Log "Requesting certificate for domain(s): $($domains -join ', ')"
    try {
        if ($plugin -eq 'Manual') {
            # Manual challenge handling
            $cert = New-PACertificate -Domain $mainDomain -Plugin $plugin -DnsSleep 0 -Verbose

            Write-Host "`nPlease create the following DNS TXT records:"
            Write-Host "------------------------------------------"
            foreach ($authz in $cert.Authorization) {
                foreach ($challenge in $authz.Challenges) {
                    if ($challenge.Type -eq 'dns-01') {
                        $dnsName = "_acme-challenge." + $authz.Identifier
                        $txtValue = $challenge.DnsDigest
                        Write-Host "$dnsName -> $txtValue"
                    }
                }
            }
            Write-Host "------------------------------------------"

            while ($true) {
                $continue = Read-Host "`nPress Enter when you have created the DNS records and they have propagated, or type '0' to cancel"
                if ($continue -eq '0') {
                    Write-Warning "`nOperation canceled by the user."
                    return
                }

                # Test DNS record propagation with retry
                $allRecordsPresent = $true
                foreach ($authz in $cert.Authorization) {
                    $dnsName = "_acme-challenge." + $authz.Identifier
                    try {
                        $dnsResult = Invoke-WithRetry -ScriptBlock {
                            Resolve-DnsName -Name $dnsName -Type TXT -ErrorAction Stop
                        } -MaxAttempts 10 -InitialDelaySeconds 30 `
                          -OperationName "DNS propagation check for $dnsName" `
                          -SuccessCondition { $_.Strings -and $_.Strings.Count -gt 0 }

                        Write-Host "`nFound DNS TXT record for ${dnsName}: $($dnsResult.Strings)" -ForegroundColor Green
                    } catch {
                        Write-Warning "`nDNS TXT record not found for $dnsName after multiple attempts."
                        $allRecordsPresent = $false
                        break
                    }
                }

                if ($allRecordsPresent) {
                    # Proceed with validation
                    try {
                        $validationResult = Complete-ChallengeWithRetry -AuthChain $cert -MaxAttempts 5 -PropagationWait 30
                        if (-not $validationResult) {
                            Write-Error "Failed to validate domain ownership after multiple attempts"
                            Write-Log "Failed to validate domain ownership after multiple attempts" -Level 'Error'
                            return
                        }

                        # Check the authorization status after challenge completion
                        $validationSuccess = $true
                        foreach ($authz in $cert.Authorization) {
                            $status = Get-PAAuthorization -AuthUrl $authz.location -Verbose
                            if ($status.status -ne 'valid') {
                                $validationSuccess = $false
                                Write-Error "`nAuthorization failed for domain $($authz.Identifier). Status: $($status.status)"
                                Write-Log "Authorization failed for domain $($authz.Identifier). Status: $($status.status)" -Level 'Error'
                                break
                            }
                        }

                        if ($validationSuccess) {
                            Write-Host "`nAll domain validations completed successfully." -ForegroundColor Green
                            Write-Log "All domain validations completed successfully."
                            break
                        } else {
                            $retry = Read-Host "`nWould you like to retry validation? (Y/N)"
                            if ($retry -notmatch '^(Y|y)$') {
                                Write-Warning "`nReturning to the main menu"
                                return
                            }
                        }
                    } catch {
                        Write-Error "`nValidation failed:`n$($_)"
                        Write-Log "Validation failed during manual challenge: $($_)" -Level 'Error'
                        $retry = Read-Host "`nWould you like to retry validation? (Y/N)"
                        if ($retry -notmatch '^(Y|y)$') {
                            Write-Warning "`nReturning to the main menu"
                            return
                        }
                    }
                } else {
                    Write-Warning "`nPlease wait a few minutes for DNS propagation and try again."
                    $wait = Read-Host "`nPress Enter to retry or type '0' to cancel"
                    if ($wait -eq '0') {
                        Write-Warning "`nOperation canceled by the user."
                        return
                    }
                }
            }

            # Wait for certificate issuance and verify the result
            Write-Host "`nWaiting for certificate issuance..."
            try {
                $cert = Submit-OrderFinalization -Order $cert.Order -Verbose

                # Verify the certificate was issued successfully
                if (-not $cert.Certificate) {
                    Write-Error "`nCertificate was not issued. Please check the Let's Encrypt logs for details."
                    Write-Log "Certificate was not issued for $mainDomain" -Level 'Error'
                    Read-Host "`nPress Enter to return to the main menu"
                    return
                }

                # Verify the certificate properties
                $certDetails = Get-PACertificate -MainDomain $mainDomain
                if ($null -eq $certDetails) {
                    Write-Error "`nUnable to retrieve certificate details."
                    Write-Log "Unable to retrieve certificate details for $mainDomain" -Level 'Error'
                    Read-Host "`nPress Enter to return to the main menu"
                    return
                }

                Write-Host "`nCertificate issued successfully:" -ForegroundColor Green
                Write-Host "Subject: $($certDetails.Certificate.Subject)"
                Write-Host "Issuer: $($certDetails.Certificate.Issuer)"
                Write-Host "Valid Until: $($certDetails.Certificate.NotAfter)"
                Write-Log "Certificate issued successfully for $mainDomain, valid until $($certDetails.Certificate.NotAfter)"
            } catch {
                Write-Error "`nFailed to finalize certificate order: $($_)"
                Write-Log "Failed to finalize certificate order for ${mainDomain}: " + $_ -Level 'Error'
                Read-Host "`nPress Enter to return to the main menu"
                return
            }
        } else {
            # Automated challenge handling

            # Added -Force to overwrite existing orders
            $cert = New-PACertificate -Domain $mainDomain -Plugin $plugin -PluginArgs $pluginArgs -Force -Verbose

            # Check if the certificate was obtained successfully
            if (-not $cert.CertFile -and -not $cert.FullChainFile -and -not $cert.PfxFile) {
                Write-Error "`nFailed to obtain the certificate. Please check the output above for errors."
                Write-Log "Failed to obtain the certificate for $mainDomain" -Level 'Error'
                Read-Host "`nPress Enter to return to the main menu"
                return
            }
        }

        # Call the Install-Certificate function to handle installation options
        Install-Certificate -PACertificate $cert

    } catch {
        Write-Error "`nAn error occurred during certificate request: $($_)"
        Write-Log "An error occurred during certificate request: $($_)" -Level 'Error'
        Read-Host "`nPress Enter to return to the main menu"
    }
    Read-Host "`nPress Enter to return to the main menu"
}
# Function to set up automatic renewal
function Set-AutomaticRenewal {
    [CmdletBinding(SupportsShouldProcess=$true)]
    param(
        [Parameter()]
        [switch]$Force
    )

    Write-Debug "Loading renewal configuration"
    $config = Get-RenewalConfig
    Write-Verbose "Current renewal configuration:"
    Write-Verbose "  Time: $($config.RenewalHour):$($config.RenewalMinute.ToString('00'))"
    Write-Verbose "  Randomization: $($config.UseRandomization)"
    Write-Verbose "  Window: $($config.RandomizationWindow) minutes"

    # Show current settings and prompt for changes
    Write-Host "`nCurrent Renewal Schedule Settings:"
    Write-Host "--------------------------------"
    Write-Host "Renewal Time: $($config.RenewalHour):$($config.RenewalMinute.ToString('00'))"
    Write-Host "Use Randomization: $($config.UseRandomization)"
    if ($config.UseRandomization) {
        Write-Host "Randomization Window: $($config.RandomizationWindow) minutes"
    }
    Write-Host "`nSelect an option to modify:"
    Write-Host "1) Change renewal time"
    Write-Host "2) Toggle randomization"
    Write-Host "3) Set randomization window"
    Write-Host "4) Apply current settings"
    Write-Host "0) Back"

    $choice = Get-ValidatedInput -Prompt "`nEnter your choice (0-4)" -ValidOptions (1..4)
    switch ($choice) {
        1 {
            do {
                $hour = Read-Host "`nEnter the hour for renewal (0-23)"
                if ($hour -match '^\d+$' -and [int]$hour -ge 0 -and [int]$hour -le 23) {
                    $config.RenewalHour = [int]$hour
                    break
                }
                Write-Warning "Invalid hour. Please enter a number between 0 and 23."
            } while ($true)

            do {
                $minute = Read-Host "Enter the minute for renewal (0-59)"
                if ($minute -match '^\d+$' -and [int]$minute -ge 0 -and [int]$minute -le 59) {
                    $config.RenewalMinute = [int]$minute
                    break
                }
                Write-Warning "Invalid minute. Please enter a number between 0 and 59."
            } while ($true)
        }
        2 {
            $config.UseRandomization = -not $config.UseRandomization
            Write-Host "`nRandomization $(if ($config.UseRandomization) {'enabled'} else {'disabled'})"
        }
        3 {
            do {
                $window = Read-Host "`nEnter randomization window in minutes (15-180)"
                if ($window -match '^\d+$' -and [int]$window -ge 15 -and [int]$window -le 180) {
                    $config.RandomizationWindow = [int]$window
                    break
                }
                Write-Warning "Invalid window. Please enter a number between 15 and 180."
            } while ($true)
        }
        0 { return }
    }

    # Save the configuration
    Save-RenewalConfig -Config $config

    # Calculate trigger time
    $baseTime = [DateTime]::Today.AddHours($config.RenewalHour).AddMinutes($config.RenewalMinute)

    if ($config.UseRandomization) {
        # Calculate random offset
        $halfWindow = [math]::Floor($config.RandomizationWindow / 2)
        $randomOffset = Get-Random -Minimum (-$halfWindow) -Maximum $halfWindow
        $baseTime = $baseTime.AddMinutes($randomOffset)
    }

    $taskName = "Posh-ACME Certificate Renewal"
    $scriptPath = $MyInvocation.MyCommand.Path

    # Create the scheduled task action
    $action = New-ScheduledTaskAction -Execute 'PowerShell.exe' `
        -Argument "-NoProfile -WindowStyle Hidden -File `"$scriptPath`" -RenewAll"

    # Create the trigger with the calculated time
    $trigger = New-ScheduledTaskTrigger -Daily -At $baseTime

    # Add randomization to the trigger if enabled
    if ($config.UseRandomization) {
        $trigger.RandomDelay = [TimeSpan]::FromMinutes($config.RandomizationWindow)
    }

    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries

    try {
        $task = New-ScheduledTask -Action $action -Trigger $trigger -Principal $principal -Settings $settings
        Register-ScheduledTask -TaskName $taskName -InputObject $task -Force

        Write-Host "`nAutomatic renewal configured via scheduled task '$taskName'"
        Write-Host "Base run time: $($baseTime.ToString('HH:mm'))"
        if ($config.UseRandomization) {
            Write-Host "Random delay window: $($config.RandomizationWindow) minutes"
        }
        Write-Log "Automatic renewal task '$taskName' configured for $($baseTime.ToString('HH:mm'))"
    } catch {
        $msg = "Failed to create scheduled task '$taskName'. Action: '$($action.Execute) $($action.Arguments)', Error: $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
    }

    Read-Host "`nPress Enter to return to the main menu"
}

# Function to view existing certificates
function Get-ExistingCertificates {
    [CmdletBinding()]
    param ()
    Initialize-ACMEServer
    Clear-CertificateCache # Clear cache at start of listing
    $orders = Get-PAOrder
    if ($orders) {
        foreach ($order in $orders) {
            $cert = Get-CachedPACertificate -MainDomain $order.MainDomain
            if ($cert) {
                Write-Host "`nOrder Name: $($order.OrderName)"
                Write-Host "Main Domain: $($order.MainDomain)"
                Write-Host "Alternative Names: $($order.AllDnsNames -join ', ')"
                Write-Host "Expires: $($cert.Certificate.NotAfter)"
                Write-Host "Issuer: $($cert.Certificate.Issuer)"
                Write-Host "Thumbprint: $($cert.Certificate.Thumbprint)"
                Write-Host "----------------------------------------"
            }
        }
    } else {
        Write-Host "`nNo existing certificates found."
    }
    Read-Host "`nPress Enter to return to the main menu"
}

# Function to revoke a certificate
function Revoke-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
    param (
        [Parameter()]
        [switch]$Force
    )
    Initialize-ACMEServer
    # Load revoked certificates
    $revokedCerts = Get-RevokedCertificates

    # Get active certificates (exclude revoked ones)
    $certs = Get-PACertificate | Where-Object { $revokedCerts -notcontains $_.MainDomain }

    if (-not $certs) {
        Write-Warning "`nNo active certificates available to revoke."
        Read-Host "`nPress Enter to return to the main menu"
        return
    }

    Write-Host "`nSelect the certificate to revoke:"
    $i = 1
    foreach ($cert in $certs) {
        Write-Host "$i) $($cert.AllSANs -join ', ')"
        $i++
    }
    $selection = Get-ValidatedInput -Prompt "`nEnter the number corresponding to the certificate or 0 to cancel" -ValidOptions (1..$certs.Count)
    if ($selection -eq 0) {
        Write-Warning "`nOperation canceled."
        Read-Host "`nPress Enter to return to the main menu"
        return
    } else {
        $certToRevoke = $certs[$selection - 1]

        # Get the certificate file path
        $certFilePath = $certToRevoke.CertFile
        if (-not (Test-Path $certFilePath)) {
            Write-Error "`nCertificate file not found at $certFilePath. Cannot revoke."
            Write-Log "Certificate file not found at $certFilePath. Cannot revoke." -Level 'Error'
            Read-Host "`nPress Enter to return to the main menu"
            return
        }

        # Get the private key file path
        $keyFilePath = $certToRevoke.KeyFile
        if (-not (Test-Path $keyFilePath)) {
            Write-Error "`nPrivate key file not found at $keyFilePath. Cannot revoke."
            Write-Log "Private key file not found at $keyFilePath. Cannot revoke." -Level 'Error'
            Read-Host "`nPress Enter to return to the main menu"
            return
        }

        # Confirm revocation
        if (-not (Confirm-Action -Message "`nAre you sure you want to revoke the certificate for $($certToRevoke.AllSANs -join ', ')? (Y/N)")) {
            Write-Warning "`nRevocation canceled."
            Read-Host "`nPress Enter to return to the main menu"
            return
        }

        try {
            # Revoke the certificate using CertFile and KeyFile
            Revoke-PACertificate -CertFile $certFilePath -KeyFile $keyFilePath -Reason keyCompromise -Force -Verbose

            # Add to revoked certificates
            $revokedCerts += $certToRevoke.MainDomain
            Save-RevokedCertificates $revokedCerts

            Write-Host "`nCertificate for $($certToRevoke.AllSANs -join ', ') has been revoked successfully."
            Write-Log "Certificate for $($certToRevoke.AllSANs -join ', ') has been revoked successfully."
        } catch {
            if ($_.Exception.Message -match 'already revoked') {
                # Certificate is already revoked; update local status
                $revokedCerts += $certToRevoke.MainDomain
                Save-RevokedCertificates $revokedCerts

                Write-Warning "`nCertificate for $($certToRevoke.AllSANs -join ', ') is already revoked. Updated status accordingly."
                Write-Log "Certificate for $($certToRevoke.AllSANs -join ', ') is already revoked. Updated status accordingly." -Level 'Warning'
            } else {
                Write-Error "`nFailed to revoke certificate for $($certToRevoke.AllSANs -join ', '): $($_)"
                Write-Log "Failed to revoke certificate for $($certToRevoke.AllSANs -join ', '): $($_)" -Level 'Error'
            }
        }
    }
    Read-Host "`nPress Enter to return to the main menu"
}

# Function to remove a certificate
function Remove-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
    param (
        [Parameter()]
        [switch]$Force
    )
    Initialize-ACMEServer
    Clear-CertificateCache # Clear cache at start of operation
    # Load revoked certificates
    $revokedCerts = Get-RevokedCertificates

    $orders = Get-PAOrder
    if (-not $orders) {
        Write-Warning "`nNo certificates available to delete."
        Read-Host "`nPress Enter to return to the main menu"
        return
    }

    Write-Host "`nSelect the certificate to delete:"
    $i = 1
    foreach ($order in $orders) {
        $status = if ($revokedCerts -contains $order.MainDomain) { "Revoked" } else { "Active" }
        Write-Host "$i) $($order.MainDomain) - Status: $status"
        $i++
    }
    $selection = Get-ValidatedInput -Prompt "`nEnter the number corresponding to the certificate or 0 to cancel" -ValidOptions (1..$orders.Count)
    if ($selection -eq 0) {
        Write-Warning "`nOperation canceled."
        Read-Host "`nPress Enter to return to the main menu"
        return
    } else {
        $orderToDelete = $orders[$selection - 1]
        $mainDomain = $orderToDelete.MainDomain

        # Check if the certificate is revoked
        $isRevoked = $revokedCerts -contains $mainDomain

        if ($isRevoked) {
            # Extra confirmation for revoked certificates
            if (-not (Confirm-Action -Message "`nThe certificate for $mainDomain is already revoked. Are you sure you want to delete it? (Y/N)")) {
                Write-Warning "`nDeletion canceled."
                Read-Host "`nPress Enter to return to the main menu"
                return
            }
        } else {
            # Ask if the user wants to revoke the certificate first
            $revokeFirst = Read-Host "`nThe certificate for $mainDomain is still active. Do you want to revoke it before deletion? (Y/N/Cancel)"
            if ($revokeFirst -match '^(Y|y)$') {
                # Revoke the certificate
                $cert = Get-CachedPACertificate -MainDomain $mainDomain
                if ($cert) {
                    # Get the certificate file path
                    $certFilePath = $cert.CertFile
                    if (-not (Test-Path $certFilePath)) {
                        Write-Error "`nCertificate file not found at $certFilePath. Cannot revoke."
                        Write-Log "Certificate file not found at $certFilePath. Cannot revoke." -Level 'Error'
                        Read-Host "`nPress Enter to return to the main menu"
                        return
                    }

                    # Get the private key file path
                    $keyFilePath = $cert.KeyFile
                    if (-not (Test-Path $keyFilePath)) {
                        Write-Error "`nPrivate key file not found at $keyFilePath. Cannot revoke."
                        Write-Log "Private key file not found at $keyFilePath. Cannot revoke." -Level 'Error'
                        Read-Host "`nPress Enter to return to the main menu"
                        return
                    }

                    try {
                        Revoke-PACertificate -CertFile $certFilePath -KeyFile $keyFilePath -Reason keyCompromise -Force -Verbose

                        # Add to revoked certificates
                        $revokedCerts += $mainDomain
                        Save-RevokedCertificates $revokedCerts

                        Write-Host "`nCertificate for $mainDomain has been revoked successfully."
                        Write-Log "Certificate for $mainDomain has been revoked successfully."
                    } catch {
                        if ($_.Exception.Message -match 'already revoked') {
                            # Certificate is already revoked; update local status
                            $revokedCerts += $mainDomain
                            Save-RevokedCertificates $revokedCerts

                            Write-Warning "`nCertificate for $mainDomain is already revoked. Updated status accordingly."
                            Write-Log "Certificate for $mainDomain is already revoked. Updated status accordingly." -Level 'Warning'
                        } else {
                            Write-Error "`nFailed to revoke certificate for ${mainDomain}: $($_)"
                            Write-Log "Failed to revoke certificate for ${mainDomain}: $($_)" -Level 'Error'
                            Read-Host "`nPress Enter to return to the main menu"
                            return
                        }
                    }
                } else {
                    Write-Error "`nCertificate not found. Cannot revoke."
                    Write-Log "Certificate not found for $mainDomain. Cannot revoke." -Level 'Error'
                    Read-Host "`nPress Enter to return to the main menu"
                    return
                }
            } elseif ($revokeFirst -match '^(Cancel|cancel|C|c)$') {
                Write-Warning "`nDeletion canceled."
                Read-Host "`nPress Enter to return to the main menu"
                return
            }
            # Proceed to deletion without revocation if user selects 'N' or similar
        }

        # Confirm deletion
        if (-not (Confirm-Action -Message "`nAre you sure you want to delete the certificate for ${mainDomain}? This action cannot be undone. (Y/N)")) {
            Write-Warning "`nDeletion canceled."
            Read-Host "`nPress Enter to return to the main menu"
            return
        }

        try {
            Remove-PAOrder -MainDomain $mainDomain -Force -Verbose

            # Remove from revoked certificates if present
            if ($revokedCerts -contains $mainDomain) {
                $revokedCerts = $revokedCerts | Where-Object { $_ -ne $mainDomain }
                Save-RevokedCertificates $revokedCerts
            }

            Write-Host "`nCertificate for $mainDomain has been deleted successfully."
            Write-Log "Certificate for $mainDomain has been deleted successfully."
        } catch {
            Write-Error "`nFailed to delete certificate for ${mainDomain}: $($_)"
            Write-Log "Failed to delete certificate for ${mainDomain}: $($_)" -Level 'Error'
        }
    }
    Read-Host "`nPress Enter to return to the main menu"
}

# Function to change ACME server
function Set-ACMEServer {
    [CmdletBinding()]
    param ()
    while ($true) {
        Write-Host "`nSelect the ACME server to use:"
        Write-Host "1) Let's Encrypt Production"
        Write-Host "2) Let's Encrypt Staging"
        Write-Host "0) Back"
        $serverChoice = Get-ValidatedInput -Prompt "`nEnter the number corresponding to your choice (0-2)" -ValidOptions 1,2
        switch ($serverChoice) {
            0 { return }
            1 {
                Set-PAServer LE_PROD
                Write-Host "`nACME server set to Let's Encrypt Production."
                break
            }
            2 {
                Set-PAServer LE_STAGING
                Write-Host "`nACME server set to Let's Encrypt Staging."
                break
            }
            default {
                Write-Warning "`nInvalid selection. Please choose 0-2."
            }
        }
    }
    Read-Host "`nPress Enter to return to the advanced options"
}

# Function for challenge validation with retry
function Complete-ChallengeWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [object]$AuthChain,
        [Parameter()]
        [int]$MaxAttempts = 3,
        [Parameter()]
        [int]$PropagationWait = 30
    )

    Write-ProgressHelper -Activity "Validating Domain Ownership" -Status "Starting validation process..."

    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        Write-ProgressHelper -Activity "Validating Domain Ownership" `
            -Status "Attempt $attempt of $MaxAttempts" `
            -StepNumber $attempt -TotalSteps $MaxAttempts

        try {
            Complete-AuthChallenge -AuthChain $AuthChain -DnsSleep 0

            # Verify authorizations with progress
            $authCount = $AuthChain.Authorization.Count
            $currentAuth = 0
            $allValid = $true

            foreach ($authz in $AuthChain.Authorization) {
                $currentAuth++
                Write-ProgressHelper -Activity "Checking Authorization Status" `
                    -Status "Verifying $($authz.Identifier)" `
                    -StepNumber $currentAuth -TotalSteps $authCount

                $status = Get-PAAuthorization -AuthUrl $authz.location
                Write-Debug "Authorization status for $($authz.Identifier): $($status.status)"

                if ($status.status -ne 'valid') {
                    $allValid = $false
                    $details = $status.challenges | Where-Object { $_.type -eq 'dns-01' } | Select-Object -ExpandProperty error
                    Write-Warning "Authorization failed for $($authz.Identifier): $($details.detail)"
                    Write-Log "Authorization failed for $($authz.Identifier): $($details.detail)" -Level 'Warning'
                }
            }

            if ($allValid) {
                Write-Progress -Activity "Validating Domain Ownership" -Completed
                return $true
            }
        } catch {
            Write-Error "Challenge validation attempt $attempt failed: $($_.Exception.Message)"
            Write-Log "Challenge validation attempt $attempt failed: $($_.Exception.Message)" -Level 'Error'

            if ($attempt -eq $MaxAttempts) {
                throw "Challenge validation failed after $MaxAttempts attempts"
            }
            Start-Sleep -Seconds $PropagationWait
        }
    }

    Write-Progress -Activity "Validating Domain Ownership" -Completed
    return $false
}
#endregion

#region UI Functions
# Function to display the main menu
function Show-Menu {
    [CmdletBinding()]
    param ()
    Clear-Host
    Initialize-ACMEServer
    Write-Host "`n=== Let's Encrypt Certificate Management ===`n" -ForegroundColor Cyan
    Write-Host "Available Actions:"
    Write-Host "1. Register a new certificate"
    Write-Host "2. Install certificate"
    Write-Host "3. Configure automatic renewal"
    Write-Host "4. View existing certificates"
    Write-Host "5. Revoke a certificate"
    Write-Host "6. Delete a certificate"
    Write-Host "7. Advanced options"
    Write-Host "8. Exit`n"
}

# Function to display the advanced options menu
function Show-AdvancedOptions {
    [CmdletBinding()]
    param ()
    while ($true) {
        Clear-Host
        $currentServer = (Get-PAServer).Name
        Write-Host "=== Advanced Options ===`n"
        Write-Host "1) Change ACME server (current: $currentServer)"
        Write-Host "2) Manage saved settings"
        Write-Host "0) Back"
        $advancedChoice = Read-Host "`nEnter your choice (0-2)"
        switch ($advancedChoice) {
            '0' { return }
            '1' { Set-ACMEServer }
            '2' { Show-SettingsMenu }
            default { Write-Warning "`nInvalid selection. Please choose 0-2." }
        }
        Read-Host "`nPress Enter to return to the advanced options"
    }
}

# Function to display the settings menu
function Show-SettingsMenu {
    [CmdletBinding()]
    param()

    $settings = Get-ScriptSettings
    while ($true) {
        Clear-Host
        Write-Host "`n=== Settings Management ===`n" -ForegroundColor Cyan
        Write-Host "Current Settings:"
        Write-Host "1) Default DNS Plugin: $($settings.DefaultDNSPlugin)"
        Write-Host "2) Cloudflare Token: $(if ($settings.CloudflareToken) {'[Set]'} else {'[Not Set]'})"
        Write-Host "3) AWS Profile: $($settings.AWSProfile)"
        Write-Host "4) Azure Settings: $(if ($settings.AzureSubscriptionId) {'[Set]'} else {'[Not Set]'})"
        Write-Host "5) Default Email: $($settings.LastUsedEmail)"
        Write-Host "6) Default Certificate Path: $($settings.DefaultCertPath)"
        Write-Host "7) Export settings"
        Write-Host "8) Import settings"
        Write-Host "9) Installation Preferences"
        Write-Host "0) Back"

        $choice = Get-ValidatedInput -Prompt "`nEnter your choice" -ValidOptions (1..9)
        switch ($choice) {
            1 {
                $plugins = @(Get-PAPlugin | Where-Object { $_.ChallengeType -eq 'dns-01' })
                Write-Host "`nAvailable plugins:"
                $plugins | ForEach-Object { Write-Host "- $($_.Name)" }
                $newPlugin = Read-Host "`nEnter default DNS plugin name (or press Enter to keep current)"
                if ($newPlugin) { $settings.DefaultDNSPlugin = $newPlugin }
            }
            2 {
                $token = Read-Host "Enter Cloudflare API Token (or press Enter to keep current)" -AsSecureString
                if ($token.Length -gt 0) {
                    $settings.CloudflareToken = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto(
                        [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($token)
                    )
                }
            }
            3 {
                $awsProfile = Read-Host "Enter AWS Profile name (or press Enter to keep current)"
                if ($awsProfile) { $settings.AWSProfile = $awsProfile }
            }
            4 {
                $subId = Read-Host "Enter Azure Subscription ID (or press Enter to keep current)"
                if ($subId) { $settings.AzureSubscriptionId = $subId }
                $tenantId = Read-Host "Enter Azure Tenant ID (or press Enter to keep current)"
                if ($tenantId) { $settings.AzureTenantId = $tenantId }
            }
            5 {
                $email = Read-Host "Enter default email address (or press Enter to keep current)"
                if ($email) { $settings.LastUsedEmail = $email }
            }
            6 {
                $path = Read-Host "Enter default certificate path (or press Enter to keep current)"
                if ($path) { $settings.DefaultCertPath = $path }
            }
            7 {
                $exportPath = Read-Host "Enter path to export settings"
                if ($exportPath) {
                    try {
                        $settings | ConvertTo-Json | Set-Content -Path $exportPath
                        Write-Host "Settings exported successfully" -ForegroundColor Green
                    } catch {
                        Write-Warning "Failed to export settings: $_"
                    }
                }
            }
            8 {
                $importPath = Read-Host "Enter path to import settings"
                if (Test-Path $importPath) {
                    try {
                        $newSettings = Get-Content $importPath -Raw | ConvertFrom-Json
                        $settings = $newSettings
                        Save-ScriptSettings -Settings $settings
                        Write-Host "Settings imported successfully" -ForegroundColor Green
                    } catch {
                        Write-Warning "Failed to import settings: $_"
                    }
                }
            }
            9 {
                Show-InstallationSettings -Settings $settings
            }
            0 { break }
        }

        if ($choice -ne 0) {
            Save-ScriptSettings -Settings $settings
            Read-Host "Press Enter to continue..."
        }
    }
}

# Function to display installation settings
function Show-InstallationSettings {
    [CmdletBinding()]
    param($Settings)

    Write-Host "`n=== Installation Preferences ===`n"
    Write-Host "1) Always make keys exportable: $($Settings.AlwaysExportable)"
    Write-Host "2) Preferred install location: $($Settings.PreferredInstallLocation)"
    Write-Host "3) Default PEM save location: $($Settings.DefaultPEMLocation)"
    Write-Host "4) Default PFX save location: $($Settings.DefaultPFXLocation)"
    Write-Host "0) Back"

    $choice = Get-ValidatedInput -Prompt "`nEnter your choice" -ValidOptions (1..4)

    switch ($choice) {
        1 { $Settings.AlwaysExportable = -not $Settings.AlwaysExportable }
        2 {
            $locations = @('ManagementServer', 'RecordingServer', 'PFXFile')
            Write-Host "`nAvailable locations:"
            for ($i = 0; $i -lt $locations.Count; $i++) {
                Write-Host "$($i + 1)) $($locations[$i])"
            }
            $locChoice = Get-ValidatedInput -Prompt "Select location" -ValidOptions (1..$locations.Count)
            $Settings.PreferredInstallLocation = $locations[$locChoice - 1]
        }
        3 {
            $path = Read-Host "Enter default PEM save location (Enter to keep current)"
            if ($path) { $Settings.DefaultPEMLocation = $path }
        }
        4 {
            $path = Read-Host "Enter default PFX save location (Enter to keep current)"
            if ($path) { $Settings.DefaultPFXLocation = $path }
        }
    }
}
#endregion

#region Core Utility Functions

# Function to log messages
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,
        [Parameter()]
        [ValidateSet('Info', 'Warning', 'Error', 'Success')]
        [string]$Level = 'Info'
    )

    # Only log if the message is meaningful
    if ([string]::IsNullOrWhiteSpace($Message)) {
        return
    }

    # Filter out routine informational messages
    if ($Level -eq 'Info') {
        $routinePatterns = @(
            'ACME server set to',
            'Certificate cache cleared',
            'User exited the script',
            'Selected certificate:',
            'Detecting DNS provider'
        )

        foreach ($pattern in $routinePatterns) {
            if ($Message -like "*$pattern*") {
                return
            }
        }
    }

    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    "$timestamp [$Level] $Message" | Out-File -FilePath $logFile -Append
}

# Retry operations with exponential backoff
function Invoke-WithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [scriptblock]$ScriptBlock,

        [Parameter()]
        [int]$MaxAttempts = 5,

        [Parameter()]
        [int]$InitialDelaySeconds = 2,

        [Parameter()]
        [double]$BackoffMultiplier = 2,

        [Parameter()]
        [string]$OperationName = "Operation",

        [Parameter()]
        [scriptblock]$SuccessCondition = { $true }
    )

    $attempt = 1
    $delay = $InitialDelaySeconds

    while ($attempt -le $MaxAttempts) {
        Write-Debug "Attempt $attempt of $MaxAttempts for $OperationName"
        try {
            $result = & $ScriptBlock

            if (& $SuccessCondition) {
                Write-Debug "$OperationName succeeded on attempt $attempt"
                return $result
            }

            Write-Verbose "$OperationName attempt ${attempt}: Condition not met, retrying..."
        }
        catch {
            Write-Verbose "$OperationName attempt $attempt failed: $($_.Exception.Message)"
        }

        if ($attempt -eq $MaxAttempts) {
            Write-Error "All $MaxAttempts attempts for $OperationName failed"
            throw "Failed to complete $OperationName after $MaxAttempts attempts"
        }

        Write-Debug "Waiting $delay seconds before next attempt"
        Start-Sleep -Seconds $delay
        $delay = [math]::Min($delay * $BackoffMultiplier, 60) # Cap at 60 seconds
        $attempt++
    }
}

# Ensure the ACME server is set
function Initialize-ACMEServer {
    Write-Debug "Checking ACME server configuration"
    if (-not (Get-PAServer)) {
        Write-Verbose "No ACME server configured. Setting to Let's Encrypt Production."
        Set-PAServer LE_PROD
        Write-Debug "ACME server set successfully"
    } else {
        Write-Debug "ACME server already configured"
    }
}

# Function to securely store credentials
function Set-SecureCredential {
    [CmdletBinding()]
    param (
        [string]$ProviderName,
        [pscredential]$Credential
    )
    $credDir = "$env:APPDATA\PoshACME\Creds"
    if (-not (Test-Path $credDir)) {
        New-Item -ItemType Directory -Path $credDir -Force | Out-Null
    }
    $credPath = "$credDir\$ProviderName.cred"
    try {
        $Credential | Export-Clixml -Path $credPath
    } catch {
        $msg = "Failed to save credentials for ${ProviderName} to '$credPath': $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
    }
}

# Function to retrieve secure credentials
function Get-SecureCredential {
    [CmdletBinding()]
    param (
        [string]$ProviderName
    )
    $credPath = "$env:APPDATA\PoshACME\Creds\$ProviderName.cred"
    if (Test-Path $credPath) {
        try {
            $cred = Import-Clixml -Path $credPath
            if ($null -eq $cred) { return $null }
            return $cred
        } catch {
            $msg = "Failed to import credentials for ${ProviderName} from '$credPath': $($_.Exception.Message)"
            Write-Error $msg
            Write-Log $msg -Level 'Error'
            return $null
        }
    }
    return $null
}

# Function to confirm actions
function Confirm-Action {
    [CmdletBinding()]
    param (
        [string]$Message
    )
    $response = Read-Host "$Message (Y/N)"
    return $response -match '^[Yy]$'
}

# Function for progress reporting
function Write-ProgressHelper {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Activity,
        [Parameter()]
        [string]$Status = "In Progress",
        [Parameter()]
        [int]$PercentComplete,
        [Parameter()]
        [string]$CurrentOperation,
        [Parameter()]
        [int]$StepNumber,
        [Parameter()]
        [int]$TotalSteps
    )

    if ($StepNumber -and $TotalSteps) {
        $PercentComplete = ($StepNumber / $TotalSteps) * 100
    }

    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete -CurrentOperation $CurrentOperation
}
#endregion

#region Validation Functions
# Function to validate integer input within a range
function Get-ValidatedInput {
    [CmdletBinding()]
    param (
        [string]$Prompt,
        [int[]]$ValidOptions
    )
    do {
        $formattedPrompt = "${Prompt}: "
        $userInput = Read-Host $formattedPrompt
        if ([int]::TryParse($userInput, [ref]$null) -and $ValidOptions -contains [int]$userInput) {
            return [int]$userInput
        } elseif ($userInput -eq '0') {
            return 0
        } else {
            $validChoices = ($ValidOptions | Sort-Object) -join ', '
            Write-Warning "Please enter a valid option ($validChoices) or 0 to go back."
        }
    } while ($true)
}

# Function to validate file paths
function Test-ValidPath {
    [CmdletBinding()]
    param (
        [string]$Path,
        [switch]$IsDirectory,
        [switch]$MustExist,
        [switch]$MustNotExist,
        [switch]$RequireWrite
    )

    try {
        # Check if path is null or empty
        if ([string]::IsNullOrWhiteSpace($Path)) {
            Write-Warning "Path cannot be empty."
            return $false
        }

        # Check for invalid characters
        $invalidChars = [System.IO.Path]::GetInvalidPathChars()
        if ($Path.IndexOfAny($invalidChars) -ge 0) {
            Write-Warning "Path contains invalid characters."
            return $false
        }

        # Check if the path exists
        if ($MustExist -and -not (Test-Path $Path)) {
            Write-Warning "Path does not exist: $Path"
            return $false
        }

        # Check if the path must not exist
        if ($MustNotExist -and (Test-Path $Path)) {
            Write-Warning "Path already exists: $Path"
            return $false
        }

        # Check if the path is a directory
        if ($IsDirectory -and -not (Test-Path $Path -PathType Container)) {
            Write-Warning "Path is not a directory: $Path"
            return $false
        }

        # Check if the path is a file
        if (-not $IsDirectory -and -not (Test-Path $Path -PathType Leaf)) {
            Write-Warning "Path is not a file: $Path"
            return $false
        }

        # Check if the path is writable
        if ($RequireWrite) {
            $testFile = [System.IO.Path]::Combine($Path, [System.IO.Path]::GetRandomFileName())
            try {
                [System.IO.File]::Create($testFile).Dispose()
                [System.IO.File]::Delete($testFile)
            } catch {
                Write-Warning "Path is not writable: $Path"
                return $false
            }
        }

        return $true
    } catch {
        Write-Warning "An error occurred while validating the path: $($_)"
        return $false
    }
}

# Function to validate email addresses
function Test-ValidEmail {
    [CmdletBinding()]
    param (
        [string]$Email
    )

    if ([string]::IsNullOrWhiteSpace($Email)) {
        Write-Warning "Email address cannot be empty."
        return $false
    }

    if ($Email -notmatch '^[\w\.-]+@[\w\.-]+\.\w+$') {
        Write-Warning "Invalid email address format: $Email"
        return $false
    }

    return $true
}

# Function to validate domain names
function Test-ValidDomain {
    [CmdletBinding()]
    param (
        [string]$Domain
    )

    if ([string]::IsNullOrWhiteSpace($Domain)) {
        Write-Warning "Domain name cannot be empty."
        return $false
    }

    if ($Domain -notmatch '^[a-zA-Z0-9.-]+$') {
        Write-Warning "Invalid domain name format: $Domain"
        return $false
    }

    return $true
}

# Function to validate plugin parameters
function Test-PluginParameters {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Plugin,
        [Parameter(Mandatory = $true)]
        [hashtable]$Parameters
    )

    $validationRules = @{
        'Cloudflare' = @{
            'CFToken' = '^[a-zA-Z0-9_-]{40,}$'
        }
        'Route53' = @{
            'ProfileName' = '^[a-zA-Z0-9_-]+$'
            'AccessKey' = '^[A-Z0-9]{20}$'
            'SecretKey' = '^[a-zA-Z0-9/+]{40}$'
        }
        'Azure' = @{
            'SubscriptionId' = '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$'
            'TenantId' = '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$'
        }
    }

    if (-not $validationRules.ContainsKey($Plugin)) {
        Write-Debug "No validation rules defined for plugin: $Plugin"
        return $true
    }

    $rules = $validationRules[$Plugin]
    $isValid = $true

    foreach ($param in $Parameters.GetEnumerator()) {
        if ($rules.ContainsKey($param.Key)) {
            if ($param.Value -notmatch $rules[$param.Key]) {
                Write-Error "Invalid format for $($param.Key) in $Plugin plugin"
                $isValid = $false
            }
        }
    }

    return $isValid
}
#endregion

#region Certificate Cache Functions
# Function to get cache file path
function Get-CacheFilePath {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain
    )
    $cacheDir = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Posh-ACME\cache"
    if (-not (Test-Path $cacheDir)) {
        New-Item -ItemType Directory -Path $cacheDir -Force | Out-Null
    }
    return Join-Path -Path $cacheDir -ChildPath "$($MainDomain.Replace('*', '_wild_')).json"
}

# Function to get cached certificate
function Get-CachedPACertificate {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain,
        [switch]$Force
    )

    Write-Debug "Attempting to retrieve certificate for $MainDomain"
    $cachePath = Get-CacheFilePath -MainDomain $MainDomain

    if (-not $Force -and (Test-Path $cachePath)) {
        try {
            $cacheData = Get-Content -Path $cachePath -Raw | ConvertFrom-Json
            if ((Get-Date) -lt [DateTime]::Parse($cacheData.ExpiryTime)) {
                Write-Verbose "Retrieved certificate from cache for $MainDomain"
                return $cacheData.Certificate
            }
        } catch {
            Write-Debug "Cache read failed: $($_.Exception.Message)"
        }
    }

    Write-Verbose "Fetching fresh certificate for $MainDomain"
    try {
        $maxAttempts = 3
        $attempt = 1
        $lastError = $null

        while ($attempt -le $maxAttempts) {
            try {
                $cert = Get-PACertificate -MainDomain $MainDomain -ErrorAction Stop
                if ($null -eq $cert) {
                    throw "Get-PACertificate returned null"
                }

                # Save to cache with file lock handling
                $cacheData = @{
                    Certificate = $cert
                    ExpiryTime = (Get-Date).AddMinutes(30).ToString('o')
                }

                Invoke-WithRetry -ScriptBlock {
                    $cacheData | ConvertTo-Json | Set-Content -Path $cachePath -Force
                } -MaxAttempts 3 -InitialDelaySeconds 1 `
                  -OperationName "Cache write for $MainDomain"

                return $cert
            } catch {
                $lastError = $_
                Write-Debug "Attempt $attempt failed: $($_.Exception.Message)"
                $attempt++
                if ($attempt -le $maxAttempts) {
                    Start-Sleep -Seconds (2 * $attempt)
                }
            }
        }
        throw "Failed to retrieve certificate after $maxAttempts attempts: $($lastError.Exception.Message)"
    } catch {
        Write-Error "Critical error retrieving certificate for ${MainDomain}: $($_.Exception.Message)"
        Write-Log "Critical error retrieving certificate for ${MainDomain}: $($_.Exception.Message)" -Level 'Error'
        throw
    }
}

# Function to clear the certificate cache
function Clear-CertificateCache {
    [CmdletBinding()]
    param ()
    Write-Debug "Clearing certificate cache"
    $cacheDir = Join-Path -Path $env:LOCALAPPDATA -ChildPath "Posh-ACME\cache"
    if (Test-Path $cacheDir) {
        Get-ChildItem -Path $cacheDir -Filter "*.json" | Remove-Item -Force
    }
    Write-Verbose "Certificate cache cleared"
}
#endregion

#region Domain and Certificate File Functions
# Function to load and parse the public suffix list
function Get-PublicSuffixList {
    [CmdletBinding()]
    param (
        [string]$Url = "https://publicsuffix.org/list/public_suffix_list.dat"
    )
    $cacheDir = "$env:LOCALAPPDATA\PoshACME"
    $cachePath = "$cacheDir\public_suffix_list.dat"

    # Ensure the cache directory exists
    if (-not (Test-Path $cacheDir)) {
        New-Item -ItemType Directory -Path $cacheDir -Force | Out-Null
    }

    if (-not (Test-Path $cachePath -PathType Leaf) -or ((Get-Date) - (Get-Item $cachePath).LastWriteTime).TotalDays -gt 7) {
        Write-ProgressHelper -Activity "Updating Public Suffix List" -Status "Downloading latest list..."
        try {
            $wc = New-Object System.Net.WebClient
            $wc.DownloadProgressChanged = {
                param($send, $e)
                Write-ProgressHelper -Activity "Downloading Public Suffix List" `
                    -Status "Downloaded: $([math]::Round($e.BytesReceived/1KB, 2)) KB" `
                    -PercentComplete $e.ProgressPercentage
            }
            $wc.DownloadFileTaskAsync($Url, $cachePath).Wait()
        } catch {
            Write-Error "Failed to download public suffix list: $($_)"
            Write-Log "Failed to download public suffix list: $($_)" -Level 'Error'
            return @()
        }
    }

    try {
        $suffixes = Get-Content -Path $cachePath | Where-Object {
            $_ -and -not $_.StartsWith("//")
        }
        return $suffixes
    } catch {
        Write-Error "Failed to load public suffix list: $($_)"
        Write-Log "Failed to load public suffix list: $($_)" -Level 'Error'
        return @()
    }
}

# Function to extract the base domain using the public suffix list
function Get-BaseDomain {
    [CmdletBinding()]
    param (
        [string]$domainName,
        [string[]]$Suffixes
    )
    if ([string]::IsNullOrWhiteSpace($domainName)) {
        Write-Warning "Domain name is empty."
        return $null
    }
    if ($null -eq $Suffixes -or $Suffixes.Count -eq 0) {
        Write-Warning "Suffixes list is empty."
        return $domainName
    }
    $domainLabels = $domainName.ToLower().Split('.')
    for ($i = 0; $i -lt $domainLabels.Length; $i++) {
        $candidate = ($domainLabels[$i..($domainLabels.Length - 1)] -join '.')
        if ($Suffixes -contains $candidate) {
            if ($i -gt 0) {
                $registeredDomain = ($domainLabels[($i - 1)..($domainLabels.Length - 1)] -join '.')
                return $registeredDomain
            } else {
                return $domainName
            }
        }
    }
    return $domainName
}

# Get next file version from Recording Server certificate folder
function Get-NextFileVersion {
    [CmdletBinding()]
    param(
        [string]$folderPath,
        [string]$baseName, # 'cert' or 'pvkey'
        [string]$extension = ".pem"
    )
    $latestVersion = -1
    $files = Get-ChildItem -Path $folderPath -Filter "$baseName*${extension}"
    foreach ($file in $files) {
        if ($file.Name -match "${baseName}(\d+)$extension") {
            [int]$versionNumber = $Matches[1]
            if ($versionNumber -gt $latestVersion) {
                $latestVersion = $versionNumber
            }
        }
    }
    return ($latestVersion + 1).ToString("D3")
}

# Get Recording Server certificate folder path
function Get-RSCertFolder {
    [CmdletBinding()]
    param ()
    $certFolderPaths = @(
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
        "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates"
    )
    foreach ($path in $certFolderPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    Write-Error "Failed to find any predefined certificate folders."
    Write-Log "Failed to find any predefined certificate folders." -Level 'Error'
    return $null
}

# Save PEM files with auto-versioning
function Save-PEMFiles {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$directory,
        [Parameter(Mandatory = $true)]
        [string]$certContent,
        [Parameter(Mandatory = $true)]
        [string]$keyContent,
        [Parameter()]
        [switch]$NoVersioning
    )

    Write-Debug "Saving PEM files to $directory"
    if (-not (Test-Path $directory)) {
        $msg = "Certificate directory does not exist: '$directory'"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        return $null
    }

    try {
        # Get next version with retry for file system operations
        if (-not $NoVersioning) {
            $certVersion = Invoke-WithRetry -ScriptBlock {
                Get-NextFileVersion -folderPath $directory -baseName "cert"
            } -MaxAttempts 3 -InitialDelaySeconds 1 `
              -OperationName "Version number generation"

            $certOutputFile = Join-Path -Path $directory -ChildPath ("cert" + $certVersion + ".pem")
            $keyOutputFile = Join-Path -Path $directory -ChildPath ("pvkey" + $certVersion + ".pem")
        } else {
            $certOutputFile = Join-Path -Path $directory -ChildPath "cert.pem"
            $keyOutputFile = Join-Path -Path $directory -ChildPath "pvkey.pem"
        }

        # Save files with retry for locked files
        Invoke-WithRetry -ScriptBlock {
            Set-Content -Path $certOutputFile -Value $certContent -Encoding ascii -ErrorAction Stop
            Set-Content -Path $keyOutputFile -Value $keyContent -Encoding ascii -ErrorAction Stop
        } -MaxAttempts 5 -InitialDelaySeconds 2 `
          -OperationName "PEM file save" `
          -SuccessCondition { Test-Path $certOutputFile -and Test-Path $keyOutputFile }

        return @{
            CertFile = $certOutputFile
            KeyFile = $keyOutputFile
        }
    } catch {
        $msg = "Failed to save PEM files to '$directory' after multiple attempts. Certificate: '$certOutputFile', Key: '$keyOutputFile'. Error: $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        throw
    }
}

# Function to get certificate PEM content
function Get-CertificatePEMContent {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        [Parameter()]
        [switch]$IncludeKey
    )

    $result = @{
        CertContent = $null
        KeyContent = $null
        Success = $false
        ErrorMessage = $null
    }

    try {
        # Get certificate content
        if ($Certificate.CertificatePEM) {
            $result.CertContent = Get-Content -Path $Certificate.CertificatePEM -Raw
        } elseif ($Certificate.PEM) {
            $result.CertContent = $Certificate.PEM
        } elseif ($Certificate.CertFile) {
            $result.CertContent = Get-Content -Path $Certificate.CertFile -Raw
        } else {
            throw "Unable to retrieve PEM content from certificate object."
        }

        # Get key content if requested
        if ($IncludeKey) {
            if ($Certificate.KeyFile) {
                $result.KeyContent = Get-Content -Path $Certificate.KeyFile -Raw
            } else {
                throw "Unable to retrieve key content from certificate object."
            }
        }

        $result.Success = $true
    } catch {
        $result.ErrorMessage = $_.Exception.Message
        Write-Error $result.ErrorMessage
        Write-Log $result.ErrorMessage -Level 'Error'
    }

    return $result
}
#endregion

#region Main Script Execution

# Check if Posh-ACME module is installed
if (-not (Get-Module -ListAvailable -Name Posh-ACME)) {
    Write-Host "Posh-ACME module not found. Installing..." -ForegroundColor Yellow
    try {
        Install-Module -Name Posh-ACME -Scope CurrentUser -Force -ErrorAction Stop
        Import-Module Posh-ACME
        Write-Host "Posh-ACME module installed successfully." -ForegroundColor Green
        Write-Log "Posh-ACME module installed successfully." -Level 'Success'
    } catch {
        Write-Error "Failed to install Posh-ACME module: $($_)"
        Write-Log "Failed to install Posh-ACME module: $($_)" -Level 'Error'
        Read-Host "Press Enter to exit"
        exit
    }
} else {
    Import-Module Posh-ACME
}

# Check if running in non-interactive mode with RenewAll parameter
if ($RenewAll) {
    Write-Host "Running in non-interactive renewal mode..."
    Write-Log "Running in non-interactive renewal mode" -Level 'Info'

    # Get all certificates
    $orders = Get-PAOrder
    if (-not $orders) {
        Write-Host "No certificates found to renew." -ForegroundColor Yellow
        Write-Log "No certificates found to renew." -Level 'Warning'
        exit
    }

    $renewalCount = 0
    $errorCount = 0

    foreach ($order in $orders) {
        $mainDomain = $order.MainDomain
        Write-Host "Processing certificate for $mainDomain..." -ForegroundColor Cyan

        try {
            # Get certificate details
            $cert = Get-CachedPACertificate -MainDomain $mainDomain -Force

            # Check if renewal is needed (30 days before expiry)
            $renewalThreshold = (Get-Date).AddDays(30)
            if ($cert.Certificate.NotAfter -gt $renewalThreshold -and -not $Force) {
                Write-Host "Certificate for $mainDomain is still valid until $($cert.Certificate.NotAfter). Skipping renewal." -ForegroundColor Green
                Write-Log "Certificate for $mainDomain is still valid until $($cert.Certificate.NotAfter). Skipping renewal." -Level 'Info'
                continue
            }

            # Renew the certificate
            Write-Host "Renewing certificate for $mainDomain..." -ForegroundColor Yellow
            Write-Log "Renewing certificate for $mainDomain" -Level 'Info'

            # Get the plugin and plugin args used for the original certificate
            $plugin = $order.Plugin
            $pluginArgs = $order.PluginArgs

            # Submit the renewal
            $newCert = Submit-Renewal -MainDomain $mainDomain -Force

            if ($newCert) {
                Write-Host "Certificate for $mainDomain renewed successfully." -ForegroundColor Green
                Write-Log "Certificate for $mainDomain renewed successfully." -Level 'Success'
                $renewalCount++

                # Reinstall the certificate if it was previously installed
                # This would require tracking installation status, which is beyond the scope of this example
            } else {
                Write-Warning "Failed to renew certificate for $mainDomain."
                Write-Log "Failed to renew certificate for $mainDomain." -Level 'Warning'
                $errorCount++
            }
        } catch {
            Write-Error "Error renewing certificate for ${mainDomain}: $($_)"
            Write-Log "Error renewing certificate for ${mainDomain}: $($_)" -Level 'Error'
            $errorCount++
        }
    }

    Write-Host "`nRenewal Summary:" -ForegroundColor Cyan
    Write-Host "Certificates processed: $($orders.Count)" -ForegroundColor White
    Write-Host "Successful renewals: $renewalCount" -ForegroundColor Green
    Write-Host "Failed renewals: $errorCount" -ForegroundColor Red

    Write-Log "Renewal Summary - Processed: $($orders.Count), Successful: $renewalCount, Failed: $errorCount" -Level 'Info'
    exit
}

# Interactive mode
try {
    # Main menu loop
    while ($true) {
        Show-Menu
        $choice = Read-Host "`nEnter your choice (1-8)"

        switch ($choice) {
            '1' { Register-Certificate }
            '2' { Install-Certificate }
            '3' { Set-AutomaticRenewal }
            '4' { Get-ExistingCertificates }
            '5' { Revoke-Certificate }
            '6' { Remove-Certificate }
            '7' { Show-AdvancedOptions }
            '8' {
                Write-Host "`nExiting script. Goodbye!"
                Write-Log "User exited the script" -Level 'Info'
                exit
            }
            default { Write-Warning "`nInvalid selection. Please choose 1-8." }
        }
    }
} catch {
    Write-Error "An unexpected error occurred: $($_)"
    Write-Log "An unexpected error occurred: $($_)" -Level 'Error'
    Read-Host "`nPress Enter to exit"
}
#endregion