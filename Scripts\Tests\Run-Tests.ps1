<#
.SYNOPSIS
    Runs tests for the AutoCertLE module.

.DESCRIPTION
    This script runs Pester tests for the AutoCertLE module.

.EXAMPLE
    .\Run-Tests.ps1
#>

# Ensure Pester is installed
if (-not (Get-Module -ListAvailable -Name Pester)) {
    Write-Host "Pester module not found. Installing..." -ForegroundColor Yellow
    Install-Module -Name Pester -Force -SkipPublisherCheck -Scope CurrentUser
}

# Import Pester
Import-Module Pester -MinimumVersion 5.0

# Configure Pester
$pesterConfig = New-PesterConfiguration
$pesterConfig.Run.Path = $PSScriptRoot
$pesterConfig.Output.Verbosity = 'Detailed'
$pesterConfig.TestResult.Enabled = $true
$pesterConfig.TestResult.OutputPath = "$PSScriptRoot\TestResults.xml"
$pesterConfig.CodeCoverage.Enabled = $true
$pesterConfig.CodeCoverage.Path = "$PSScriptRoot\..\AutoCertLE"

# Run the tests
Invoke-Pester -Configuration $pesterConfig
