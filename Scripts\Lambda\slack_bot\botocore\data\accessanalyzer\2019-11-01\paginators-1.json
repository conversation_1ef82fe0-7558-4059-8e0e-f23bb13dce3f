{"pagination": {"ListAnalyzedResources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "analyzedResources"}, "ListAnalyzers": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "analyzers"}, "ListArchiveRules": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "archiveRules"}, "ListFindings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "findings"}, "ListAccessPreviewFindings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "findings"}, "ListAccessPreviews": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "accessPreviews"}, "ValidatePolicy": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "findings"}, "ListPolicyGenerations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "policyGenerations"}, "GetFindingV2": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "findingDetails"}, "ListFindingsV2": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "findings"}, "GetFindingRecommendation": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "recommendedSteps"}}}