<#
.SYNOPSIS
    Removes a certificate from Posh-ACME.

.DESCRIPTION
    Removes a certificate from Posh-ACME's management, but does not revoke it with the ACME server.

.PARAMETER MainDomain
    The main domain of the certificate to remove.

.EXAMPLE
    Remove-Certificate -MainDomain "example.com"
#>
function Remove-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
    param (
        [Parameter(Mandatory=$true)]
        [string]$MainDomain
    )

    # Ensure the ACME server is set
    Initialize-ACMEServer

    try {
        # Get the certificate
        $cert = Get-CachedPACertificate -MainDomain $MainDomain
        if (-not $cert) {
            Write-Error "Certificate for $MainDomain not found."
            Write-Log "Certificate for $MainDomain not found during removal attempt." -Level 'Error'
            return
        }

        # Display certificate information
        Write-Host "`nCertificate Information:" -ForegroundColor Cyan
        Write-Host "Domain: $MainDomain"
        Write-Host "Issuer: $($cert.Certificate.Issuer)"
        Write-Host "Valid Until: $($cert.Certificate.NotAfter)"
        Write-Host "Subject Alternative Names: $($cert.Certificate.DnsNameList -join ', ')"

        # Confirm deletion
        $confirm = Read-Host "`nAre you sure you want to delete this certificate from Posh-ACME? This will not revoke the certificate. (Y/N)"
        if ($confirm -notmatch '^[Yy]$') {
            Write-Host "`nDeletion cancelled." -ForegroundColor Yellow
            return
        }

        # Delete the certificate
        if ($PSCmdlet.ShouldProcess($MainDomain, "Remove certificate")) {
            Remove-PAOrder -MainDomain $MainDomain -Force
            
            # Clear the certificate from cache
            $cachePath = Get-CacheFilePath -MainDomain $MainDomain
            if (Test-Path $cachePath) {
                Remove-Item -Path $cachePath -Force
            }
            
            Write-Host "`nCertificate for $MainDomain has been deleted from Posh-ACME." -ForegroundColor Green
            Write-Log "Certificate for $MainDomain has been deleted from Posh-ACME." -Level 'Success'
        }
    } catch {
        Write-Error "Failed to delete certificate: $($_)"
        Write-Log "Failed to delete certificate for ${MainDomain}: $($_)" -Level 'Error'
    }
    
    Read-Host "`nPress Enter to return to the main menu"
}
