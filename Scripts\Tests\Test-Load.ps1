<#
.SYNOPSIS
    Load testing for the AutoCertLE module.

.DESCRIPTION
    This script performs load testing by simulating multiple certificate operations.

.PARAMETER BaseDomain
    The base domain to use for testing. Subdomains will be created automatically.

.PARAMETER Count
    The number of certificate operations to simulate. Default is 10.

.PARAMETER Parallel
    If specified, operations will be run in parallel using jobs.

.PARAMETER OperationType
    The type of operation to test. Valid values are 'Register', 'Install', 'Distribute', 'All'. Default is 'All'.

.EXAMPLE
    .\Test-Load.ps1 -BaseDomain "example.com" -Count 5 -OperationType "Install"
#>
[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$BaseDomain,
    
    [Parameter()]
    [int]$Count = 10,
    
    [Parameter()]
    [switch]$Parallel,
    
    [Parameter()]
    [ValidateSet('Register', 'Install', 'Distribute', 'All')]
    [string]$OperationType = 'All'
)

# Import the module
$modulePath = Join-Path -Path $PSScriptRoot -ChildPath '..\AutoCertLE\AutoCertLE.psd1'
Import-Module $modulePath -Force

# Start logging
$logFile = Join-Path -Path $PSScriptRoot -ChildPath "LoadTest_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
Start-Transcript -Path $logFile -Append

try {
    Write-Host "=== AutoCertLE Load Test ===" -ForegroundColor Cyan
    Write-Host "Base Domain: $BaseDomain" -ForegroundColor Cyan
    Write-Host "Operation Count: $Count" -ForegroundColor Cyan
    Write-Host "Parallel: $Parallel" -ForegroundColor Cyan
    Write-Host "Operation Type: $OperationType" -ForegroundColor Cyan
    Write-Host "===========================" -ForegroundColor Cyan
    
    # Ensure we're using the staging environment
    Write-Host "`nSetting Let's Encrypt staging environment..." -ForegroundColor Cyan
    Set-ACMEServer -StagingMode
    
    # Create mock certificates for testing
    Write-Host "`nCreating mock certificates for testing..." -ForegroundColor Cyan
    $mockCerts = @()
    for ($i = 1; $i -le $Count; $i++) {
        $domain = "test$i.$BaseDomain"
        $mockCert = [PSCustomObject]@{
            MainDomain = $domain
            Certificate = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new()
            CertFile = "C:\fakepath\$domain.cer"
            KeyFile = "C:\fakepath\$domain.key"
            PfxFile = "C:\fakepath\$domain.pfx"
        }
        $mockCerts += $mockCert
    }
    
    # Mock the certificate functions
    function Test-RegisterCertificate {
        param ([string]$Domain)
        Start-Sleep -Seconds (Get-Random -Minimum 1 -Maximum 5)
        return [PSCustomObject]@{
            MainDomain = $Domain
            Certificate = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new()
            CertFile = "C:\fakepath\$Domain.cer"
            KeyFile = "C:\fakepath\$Domain.key"
            PfxFile = "C:\fakepath\$Domain.pfx"
        }
    }
    
    function Test-InstallCertificate {
        param ([object]$Certificate, [string]$ServerType)
        Start-Sleep -Seconds (Get-Random -Minimum 2 -Maximum 8)
        return [PSCustomObject]@{
            Success = $true
            ServerName = $env:COMPUTERNAME
            CertificateSubject = $Certificate.MainDomain
        }
    }
    
    function Test-DistributeCertificate {
        param ([object]$Certificate, [string[]]$Servers)
        Start-Sleep -Seconds (Get-Random -Minimum 5 -Maximum 15)
        return [PSCustomObject]@{
            Success = $true
            SuccessCount = $Servers.Count
            FailureCount = 0
        }
    }
    
    # Define the operations to test
    $operations = @()
    
    if ($OperationType -eq 'Register' -or $OperationType -eq 'All') {
        Write-Host "`nPreparing certificate registration operations..." -ForegroundColor Cyan
        for ($i = 1; $i -le $Count; $i++) {
            $domain = "test$i.$BaseDomain"
            $operations += @{
                Type = 'Register'
                Domain = $domain
                ScriptBlock = { param($Domain) Test-RegisterCertificate -Domain $Domain }
                Arguments = @($domain)
            }
        }
    }
    
    if ($OperationType -eq 'Install' -or $OperationType -eq 'All') {
        Write-Host "`nPreparing certificate installation operations..." -ForegroundColor Cyan
        foreach ($cert in $mockCerts) {
            $operations += @{
                Type = 'Install'
                Domain = $cert.MainDomain
                ScriptBlock = { param($Cert) Test-InstallCertificate -Certificate $Cert -ServerType 'ManagementServer' }
                Arguments = @($cert)
            }
        }
    }
    
    if ($OperationType -eq 'Distribute' -or $OperationType -eq 'All') {
        Write-Host "`nPreparing certificate distribution operations..." -ForegroundColor Cyan
        foreach ($cert in $mockCerts) {
            $servers = @("server1", "server2", "server3")
            $operations += @{
                Type = 'Distribute'
                Domain = $cert.MainDomain
                ScriptBlock = { param($Cert, $Servers) Test-DistributeCertificate -Certificate $Cert -Servers $Servers }
                Arguments = @($cert, $servers)
            }
        }
    }
    
    # Run the operations
    $results = @()
    $jobs = @()
    $startTime = Get-Date
    
    Write-Host "`nRunning $($operations.Count) operations..." -ForegroundColor Cyan
    
    if ($Parallel) {
        # Run operations in parallel using jobs
        foreach ($op in $operations) {
            $job = Start-Job -ScriptBlock $op.ScriptBlock -ArgumentList $op.Arguments
            $jobs += @{
                Job = $job
                Type = $op.Type
                Domain = $op.Domain
                StartTime = Get-Date
            }
        }
        
        # Wait for all jobs to complete
        Write-Host "Waiting for all jobs to complete..." -ForegroundColor Yellow
        $completedJobs = 0
        while ($jobs | Where-Object { $_.Job.State -eq 'Running' }) {
            $runningCount = ($jobs | Where-Object { $_.Job.State -eq 'Running' }).Count
            $newCompletedCount = $jobs.Count - $runningCount
            if ($newCompletedCount -gt $completedJobs) {
                $completedJobs = $newCompletedCount
                Write-Host "Progress: $completedJobs / $($jobs.Count) operations completed" -ForegroundColor Yellow
            }
            Start-Sleep -Seconds 1
        }
        
        # Process job results
        foreach ($jobInfo in $jobs) {
            $job = $jobInfo.Job
            $endTime = Get-Date
            $duration = $endTime - $jobInfo.StartTime
            
            try {
                $result = Receive-Job -Job $job -ErrorAction Stop
                $success = $true
            } catch {
                $result = $null
                $success = $false
            }
            
            $results += [PSCustomObject]@{
                Type = $jobInfo.Type
                Domain = $jobInfo.Domain
                Success = $success
                Result = $result
                Duration = $duration.TotalSeconds
            }
            
            Remove-Job -Job $job -Force
        }
    } else {
        # Run operations sequentially
        foreach ($op in $operations) {
            $opStartTime = Get-Date
            Write-Host "Running $($op.Type) operation for $($op.Domain)..." -ForegroundColor Yellow
            
            try {
                $result = & $op.ScriptBlock $op.Arguments
                $success = $true
            } catch {
                $result = $null
                $success = $false
            }
            
            $opEndTime = Get-Date
            $duration = $opEndTime - $opStartTime
            
            $results += [PSCustomObject]@{
                Type = $op.Type
                Domain = $op.Domain
                Success = $success
                Result = $result
                Duration = $duration.TotalSeconds
            }
            
            Write-Host "Completed in $($duration.TotalSeconds.ToString('0.00')) seconds" -ForegroundColor $(if ($success) { 'Green' } else { 'Red' })
        }
    }
    
    $endTime = Get-Date
    $totalDuration = $endTime - $startTime
    
    # Summarize results
    Write-Host "`n=== Load Test Results ===" -ForegroundColor Cyan
    Write-Host "Total operations: $($operations.Count)" -ForegroundColor Cyan
    Write-Host "Successful operations: $($results | Where-Object { $_.Success } | Measure-Object).Count" -ForegroundColor Green
    Write-Host "Failed operations: $($results | Where-Object { -not $_.Success } | Measure-Object).Count" -ForegroundColor Red
    Write-Host "Total duration: $($totalDuration.TotalSeconds.ToString('0.00')) seconds" -ForegroundColor Cyan
    Write-Host "Average operation duration: $(($results | Measure-Object -Property Duration -Average).Average.ToString('0.00')) seconds" -ForegroundColor Cyan
    
    # Show results by operation type
    foreach ($type in @('Register', 'Install', 'Distribute') | Where-Object { $results | Where-Object { $_.Type -eq $_ } }) {
        $typeResults = $results | Where-Object { $_.Type -eq $type }
        $avgDuration = ($typeResults | Measure-Object -Property Duration -Average).Average
        
        Write-Host "`n$type operations:" -ForegroundColor Cyan
        Write-Host "  Count: $($typeResults.Count)" -ForegroundColor Cyan
        Write-Host "  Successful: $($typeResults | Where-Object { $_.Success } | Measure-Object).Count" -ForegroundColor Green
        Write-Host "  Failed: $($typeResults | Where-Object { -not $_.Success } | Measure-Object).Count" -ForegroundColor Red
        Write-Host "  Average duration: $($avgDuration.ToString('0.00')) seconds" -ForegroundColor Cyan
    }
    
    Write-Host "`nLoad testing completed!" -ForegroundColor Green
} catch {
    Write-Host "Error during load testing: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
} finally {
    Stop-Transcript
    Write-Host "`nTest log saved to: $logFile" -ForegroundColor Cyan
}

