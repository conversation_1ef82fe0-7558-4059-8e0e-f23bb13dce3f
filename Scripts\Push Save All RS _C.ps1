<#
.SYNOPSIS
    Pushes a save to all RS connected to the MS

.DESCRIPTION
    This PowerShell script automates pushing a save to all RS from the MS. It prompts for CV admin credentials, retreives RS IDs from SQL, and sends a save using the MS API.

.NOTES
    Author:         <EMAIL>
    Creation Date:  06/12/24

#>

$config = @{
    AppSettingsPaths = @(
        "C:\Program Files\Salient Security Platform\CompleteView\Management Server\appsettings.json",
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Management Server\appsettings.json"
    )
    TokenUrl = "http://localhost:8095/connect/token"
}

# Prompt for Management Server user
Write-Host "`nPush a Save to All RS`n"
Write-Host "Enter CV Admin Username (if username is admin, press enter)"
$msUser = Read-Host "Username"
if ([string]::IsNullOrWhiteSpace($msUser)) {
    $msUser = "admin"
}

# Prompt for Management Server password in plaintext
Write-Host "`nEnter Password (password will be visible):`n"
$msPassText = Read-Host "Password"

# Define appsettings.json paths
$jsonFilePath = $null
foreach ($path in $config.AppSettingsPaths) {
    if (Test-Path -Path $path) {
        $jsonFilePath = $path
        break
    }
}

# Verify appsettings.json exists; exit if not found.
if (-not (Test-Path -Path $jsonFilePath)) {
    Write-Error "appsettings.json not found in configured locations"
    exit
}

# Get SQL connection string from appsettings.json
try {
    $jsonContent = Get-Content -Path $jsonFilePath -Raw | ConvertFrom-Json
    $connectionString = $jsonContent.ConnectionString
    Write-Host "SQL connection info retrieved" -ForegroundColor Green 
}
catch {
    Write-Error "Failed to parse appsettings.json. Error: $_"
    exit
}

# Function to test SQL connection
function Test-SqlConnection {
    param(
        [string]$connectionString
    )
    $connection = $null
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection $connectionString
        $connection.Open()
        if ($connection.State -eq 'Open') {
            Write-Host "Successfully connected to SQL" -ForegroundColor Green
            return $true
        }
        else {
            return $false
        }
    }
    catch {
        Write-Error "Failed to connect with the provided credentials. Error: $($_.Exception.Message)"
        return $false
    }
    finally {
        if ($connection) {
            $connection.Close()
        }
    }
}

Write-Host "Testing connection to SQL..." -ForegroundColor Green
$credentialTested = Test-SqlConnection -connectionString $connectionString

if (-not $credentialTested) {
    Write-Error "Failed to connect to SQL"
    exit
} else {
    Write-Host "Retrieving RS IDs..." -ForegroundColor Green
    $serverIds = @()
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection $connectionString
        $connection.Open()

        # SELECT RecordingServerIds
        $selectCommand = $connection.CreateCommand()
        $selectCommand.CommandText = "SELECT DISTINCT RecordingServerId FROM rs.RecordingServers"
        $reader = $selectCommand.ExecuteReader()
        while ($reader.Read()) {
            $serverIds += $reader["RecordingServerId"]
        }
        $reader.Close()
        Write-Host "RS IDs retrieved" -ForegroundColor Green
    } catch {
        Write-Error "Error retrieving RS IDs: $($_.Exception.Message)"
    } finally {
        if ($connection) { $connection.Close() }
    }

    Write-Host "Requesting MS token..." -ForegroundColor Green
    $tokenObtained = $false
    do {
        try {
            $response = Invoke-RestMethod -Method Post -Uri $config.TokenUrl -Body @{
                grant_type = "password"
                client_id = "client.desktop"
                client_secret = "secret"
                username = $msUser
                password = $msPassText
                scope = "management-server.config"
            } -ContentType "application/x-www-form-urlencoded"
            $token = $response.access_token
            Write-Host "Token obtained successfully" -ForegroundColor Green
            $tokenObtained = $true
        } catch {
            if ($_.Exception.Response.StatusCode -eq [System.Net.HttpStatusCode]::Unauthorized) {
                Write-Error "Authentication failed. Please try again."
                # Re-prompt for credentials
                Write-Host "Enter CV Admin Username (if username is admin, press enter):"
                $msUser = Read-Host "Username"
                if ([string]::IsNullOrWhiteSpace($msUser)) {
                    $msUser = "admin"
                }

                Write-Host "Enter Password (password will be visible):`n"
                $msPassText = Read-Host "Password"
                
            } else {
                $errorMsg = $_.Exception.Message
                Write-Error "Failed to obtain token: $errorMsg"
                exit
            }
        }
    } while (-not $tokenObtained)

    if ($serverIds.Count -gt 0) {
        Write-Host "Triggering push for recording servers..." -ForegroundColor Green
        $msApiUrl = "http://localhost:8095/api/v2.0/recordingServers/$($serverIds -join ',')/config/push"
        $headers = @{ Authorization = "Bearer $token" }

        try {
            $apiResponse = Invoke-RestMethod -Method Post -Uri $msApiUrl -Headers $headers
            $serverIdsPushed = $apiResponse | ForEach-Object { $_.recordingServerId }
            $serverIdsString = $serverIdsPushed -join ", "
            Write-Host "Config push response: RS $serverIdsString pushed successfully" -ForegroundColor Cyan
            $totalServersAffected = $serverIdsPushed.Count
            Write-Host "Total RS affected: " -NoNewline; Write-Host "$totalServersAffected" -ForegroundColor Red
        } catch {
            $errorMsg = $_.Exception.Message
            Write-Error "Error occurred during API call: $errorMsg"
            Write-Host "Stack Trace:" -ForegroundColor Red
            Write-Host $_.Exception.StackTrace -ForegroundColor Red
            exit
        }
    } else {
        Write-Error "No servers affected by the update"
    }
}
