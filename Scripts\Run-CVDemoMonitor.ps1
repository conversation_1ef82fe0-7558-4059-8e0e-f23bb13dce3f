#Requires -Version 5.1
<#
.SYNOPSIS
    Wrapper script for the CVDemoMonitor module.

.DESCRIPTION
    This script imports the CVDemoMonitor module and calls the Start-CVMonitoring function
    with the provided parameters. It serves as a simple entry point for the monitoring functionality.

.PARAMETER SlackWebhookUrl
    The Slack webhook URL for sending notifications.

.PARAMETER LogPath
    Custom path for the script's log file. If not specified, logs are stored in the CompleteView crash dumps directory.

.PARAMETER StateFilePath
    Custom path for the script's state file. If not specified, state is stored in the CompleteView crash dumps directory.

.PARAMETER LookbackMinutes
    Number of minutes to look back for events when the script runs. Default is 60 minutes.

.PARAMETER DumpWaitTimeoutMinutes
    Maximum time to wait for crash dump files to appear. Default is 5 minutes.

.PARAMETER NoSlack
    If specified, disables Slack notifications.

.PARAMETER TestMode
    Runs the script in test mode, simulating crashes without saving state.

.PARAMETER MonitorRecordingServer
    Enables monitoring of the CompleteView Recording Server.

.PARAMETER MonitorAdminService
    Enables monitoring of the CompleteView Administrative Service.

.PARAMETER MonitorManagementServer
    Enables monitoring of the CompleteView Management Server.

.PARAMETER LogLevel
    Sets the logging verbosity. Valid values: ERROR, WARNING, INFO, DEBUG. Default is INFO.

.PARAMETER ConfigFilePath
    Path to a configuration file for customizing script behavior.

.PARAMETER UseEnvironmentVariables
    Enables reading configuration from environment variables.

.PARAMETER ForceRegistryLookup
    Forces registry lookup for CompleteView installation paths.

.PARAMETER InstallBasePath
    Explicitly sets the base installation path for CompleteView.

.PARAMETER SkipSqlModuleCheck
    Skips checking for the SqlServer PowerShell module when testing database connectivity.

.PARAMETER SkipOdbcFallback
    Skips the ODBC fallback method when testing database connectivity.

.PARAMETER SkipTcpFallback
    Skips the TCP connectivity fallback when testing database connectivity.

.PARAMETER SqlConnectionString
    Provides a custom SQL connection string for database connectivity testing.

.EXAMPLE
    .\Run-CVDemoMonitor.ps1 -MonitorRecordingServer -MonitorManagementServer
    Monitors the Recording Server and Management Server with default settings.

.EXAMPLE
    .\Run-CVDemoMonitor.ps1 -MonitorRecordingServer -MonitorAdminService -MonitorManagementServer -LogLevel DEBUG
    Monitors all services with detailed logging.

.EXAMPLE
    .\Run-CVDemoMonitor.ps1 -TestMode -MonitorRecordingServer
    Tests the script's functionality for the Recording Server without saving state.

.NOTES
    Author: <EMAIL>
    Version: 1.0
    Updated: 2023-07-25
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidatePattern('^https://hooks\.slack\.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$')]
    [string]$SlackWebhookUrl = "*******************************************************************************",

    [Parameter(Mandatory = $false)]
    [string]$LogPath = "",

    [Parameter(Mandatory = $false)]
    [string]$StateFilePath = "",

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 10080)] # Max 1 week (10080 minutes)
    [int]$LookbackMinutes = 60,

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 60)]
    [int]$DumpWaitTimeoutMinutes = 5,

    [Parameter(Mandatory = $false)]
    [switch]$NoSlack,

    [Parameter(Mandatory = $false)]
    [switch]$TestMode,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorRecordingServer,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorAdminService,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorManagementServer,

    [Parameter(Mandatory = $false)]
    [ValidateSet('ERROR', 'WARNING', 'INFO', 'DEBUG')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [string]$ConfigFilePath = "",

    [Parameter(Mandatory = $false)]
    [switch]$UseEnvironmentVariables,

    [Parameter(Mandatory = $false)]
    [switch]$ForceRegistryLookup,

    [Parameter(Mandatory = $false)]
    [string]$InstallBasePath = "",

    [Parameter(Mandatory = $false)]
    [switch]$SkipSqlModuleCheck,

    [Parameter(Mandatory = $false)]
    [switch]$SkipOdbcFallback,

    [Parameter(Mandatory = $false)]
    [switch]$SkipTcpFallback,

    [Parameter(Mandatory = $false)]
    [string]$SqlConnectionString = ""
)

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$moduleDir = Join-Path -Path $scriptDir -ChildPath "CVDemoMonitor"

# Import the module
if (Test-Path -Path $moduleDir) {
    Import-Module -Name $moduleDir -Force
    Write-Host "Imported CVDemoMonitor module from $moduleDir" -ForegroundColor Green
}
else {
    Write-Host "Error: CVDemoMonitor module not found at $moduleDir" -ForegroundColor Red
    exit 1
}

# Create a hashtable of parameters to splat to Start-CVMonitoring
$params = @{}

# Add all parameters that were passed to this script
foreach ($key in $PSBoundParameters.Keys) {
    $params[$key] = $PSBoundParameters[$key]
}

# Call the main function
try {
    Start-CVMonitoring @params
    exit 0
}
catch {
    Write-Host "Error running CVDemoMonitor: $_" -ForegroundColor Red
    exit 1
}
