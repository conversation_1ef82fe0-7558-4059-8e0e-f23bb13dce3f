<#
.SYNOPSIS
    Main script for the AutoCertLE module for Salient CompleteView.

.DESCRIPTION
    Provides a menu-driven interface for managing Let's Encrypt certificates for Salient CompleteView.
    This script handles certificate registration, installation, renewal, and distribution specifically for
    Salient CompleteView Management Servers and Recording Servers.

.EXAMPLE
    Start-AutoCertLE
#>
function Start-AutoCertLE {
    [CmdletBinding()]
    param ()

    # Import required modules
    if (-not (Get-Module -Name Posh-ACME -ListAvailable)) {
        Write-Warning "Posh-ACME module not found. Installing..."
        Install-Module -Name Posh-ACME -Scope CurrentUser -Force
    }

    Import-Module -Name Posh-ACME -ErrorAction Stop
    Import-Module -Name AutoCertLE -ErrorAction Stop

    while ($true) {
        # Display the main menu
        Show-Menu

        # Get user choice
        $choice = Read-Host "Enter your choice"

        switch ($choice) {
            "1" {
                # Register a new certificate
                Register-Certificate
            }
            "2" {
                # Install certificate (generic)
                Install-Certificate
            }
            "3" {
                # Configure automatic renewal
                Set-AutomaticRenewal
            }
            "4" {
                # View existing certificates
                Get-ExistingCertificates | Format-Table -AutoSize
                Read-Host "`nPress Enter to continue"
            }
            "5" {
                # Revoke a certificate
                Revoke-Certificate
            }
            "6" {
                # Delete a certificate
                Remove-Certificate
            }
            "7" {
                # Install certificate to Salient server
                Install-SalientCertificate
                Read-Host "`nPress Enter to continue"
            }
            "8" {
                # Distribute certificate to multiple Salient servers
                $useWinRM = Read-Host "Use Windows Remote Management (WinRM) for distribution? (Y/N)"
                $useWinRMSwitch = $useWinRM -match '^[Yy]$'

                if ($useWinRMSwitch) {
                    $useCredential = Read-Host "Use specific credentials for WinRM? (Y/N)"
                    if ($useCredential -match '^[Yy]$') {
                        $cred = Get-Credential -Message "Enter credentials for remote servers"
                        Send-SalientCertificate -UseWinRM -Credential $cred
                    } else {
                        Send-SalientCertificate -UseWinRM
                    }
                } else {
                    Send-SalientCertificate
                }

                Read-Host "`nPress Enter to continue"
            }
            "9" {
                # Certificate monitoring dashboard
                Show-MonitoringDashboard
            }
            "10" {
                # Advanced options
                Show-AdvancedOptions
            }
            "11" {
                # Exit
                Write-Host "Exiting..." -ForegroundColor Yellow
                return
            }
            default {
                Write-Warning "Invalid choice. Please try again."
                Start-Sleep -Seconds 1
            }
        }
    }
}

# Start the script
Start-AutoCertLE
