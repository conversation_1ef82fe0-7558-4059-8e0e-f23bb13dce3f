<#
.SYNOPSIS
    Integration tests for the AutoCertLE module.

.DESCRIPTION
    This script performs integration tests for the AutoCertLE module using the Let's Encrypt staging environment.

.PARAMETER Domain
    The domain to use for testing. You must own this domain and be able to modify its DNS records.

.PARAMETER DnsPlugin
    The DNS plugin to use for testing. Default is 'Manual'.

.PARAMETER SkipCleanup
    If specified, the test certificates will not be removed after testing.

.EXAMPLE
    .\Test-Integration.ps1 -Domain "test.example.com" -DnsPlugin "Cloudflare"
#>
[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [string]$Domain,
    
    [Parameter()]
    [string]$DnsPlugin = 'Manual',
    
    [Parameter()]
    [switch]$SkipCleanup
)

# Import the module
$modulePath = Join-Path -Path $PSScriptRoot -ChildPath '..\AutoCertLE\AutoCertLE.psd1'
Import-Module $modulePath -Force

# Ensure we're using the staging environment
Write-Host "Setting Let's Encrypt staging environment..." -ForegroundColor Cyan
Set-ACMEServer -StagingMode

# Test DNS provider detection
Write-Host "`nTesting DNS provider detection for $Domain..." -ForegroundColor Cyan
$dnsInfo = Get-SuggestedDNSPlugin -Domain $Domain
Write-Host "Detected provider: $($dnsInfo.DetectedProvider) (Confidence: $($dnsInfo.Confidence)%)" -ForegroundColor Green
Write-Host "Suggested plugin: $($dnsInfo.SuggestedPlugin)" -ForegroundColor Green

# Test certificate registration
Write-Host "`nTesting certificate registration for $Domain..." -ForegroundColor Cyan
try {
    if ($DnsPlugin -eq 'Manual') {
        Write-Host "Using Manual plugin. You will need to create DNS records manually." -ForegroundColor Yellow
        $cert = Register-Certificate -Domain $Domain -Email "<EMAIL>" -AcceptTOS -Force
    } else {
        # This is just a test - you would need to provide actual credentials for non-manual plugins
        Write-Host "Using $DnsPlugin plugin. This is a test and will likely fail without proper credentials." -ForegroundColor Yellow
        $cert = Register-Certificate -Domain $Domain -Email "<EMAIL>" -AcceptTOS -Force -Plugin $DnsPlugin
    }
    
    if ($cert) {
        Write-Host "Certificate registration successful!" -ForegroundColor Green
        Write-Host "Certificate details:" -ForegroundColor Cyan
        Write-Host "  Main domain: $($cert.MainDomain)" -ForegroundColor Cyan
        Write-Host "  Expires: $($cert.Certificate.NotAfter)" -ForegroundColor Cyan
        Write-Host "  Issuer: $($cert.Certificate.Issuer)" -ForegroundColor Cyan
        
        # Test certificate retrieval
        Write-Host "`nTesting Get-ExistingCertificates..." -ForegroundColor Cyan
        $certs = Get-ExistingCertificates
        if ($certs -and $certs.Count -gt 0) {
            Write-Host "Found $($certs.Count) certificates" -ForegroundColor Green
            $foundCert = $certs | Where-Object { $_.MainDomain -eq $Domain }
            if ($foundCert) {
                Write-Host "Successfully retrieved the test certificate" -ForegroundColor Green
            } else {
                Write-Host "Could not find the test certificate in the list" -ForegroundColor Red
            }
        } else {
            Write-Host "No certificates found" -ForegroundColor Red
        }
        
        # Test Salient-specific functions if on a test server
        $serverInfo = Get-SalientServerType
        if ($serverInfo.IsManagementServer -or $serverInfo.IsRecordingServer) {
            Write-Host "`nTesting Salient-specific functions..." -ForegroundColor Cyan
            
            if ($serverInfo.IsManagementServer) {
                Write-Host "Testing Management Server certificate installation..." -ForegroundColor Cyan
                $result = Install-CertificateToManagementServer -Certificate $cert
                if ($result.Success) {
                    Write-Host "Management Server certificate installation successful!" -ForegroundColor Green
                } else {
                    Write-Host "Management Server certificate installation failed: $($result.ErrorMessage)" -ForegroundColor Red
                }
            }
            
            if ($serverInfo.IsRecordingServer) {
                Write-Host "Testing Recording Server certificate installation..." -ForegroundColor Cyan
                $result = Install-CertificateToRecordingServer -Certificate $cert -CreateFolder
                if ($result.Success) {
                    Write-Host "Recording Server certificate installation successful!" -ForegroundColor Green
                    Write-Host "Certificate number: $($result.CertificateNumber)" -ForegroundColor Cyan
                } else {
                    Write-Host "Recording Server certificate installation failed: $($result.ErrorMessage)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "`nNot running on a Salient server, skipping Salient-specific tests" -ForegroundColor Yellow
        }
        
        # Test certificate monitoring
        Write-Host "`nTesting certificate monitoring..." -ForegroundColor Cyan
        $monitoringReport = Get-CertificateMonitoringReport
        Write-Host "Monitoring report generated with $($monitoringReport.TotalCount) certificates" -ForegroundColor Green
        
        # Cleanup
        if (-not $SkipCleanup) {
            Write-Host "`nCleaning up test certificate..." -ForegroundColor Cyan
            Remove-Certificate -Domain $Domain -Force
            Write-Host "Test certificate removed" -ForegroundColor Green
        }
    } else {
        Write-Host "Certificate registration failed" -ForegroundColor Red
    }
} catch {
    Write-Host "Error during testing: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}

Write-Host "`nIntegration testing completed" -ForegroundColor Cyan
