# Function to load revoked certificates
function Get-RevokedCertificates {
    [CmdletBinding()]
    param ()
    if (Test-Path $script:RevokedCertsFile) {
        try {
            $revokedCerts = Get-Content $script:RevokedCertsFile | ConvertFrom-Json
        } catch {
            Write-Warning "Failed to load revoked certificates: $($_)"
            Write-Log "Failed to load revoked certificates: $($_)" -Level 'Warning'
            $revokedCerts = @()
        }
    } else {
        $revokedCerts = @()
    }
    return $revokedCerts
}

# Function to save revoked certificates
function Save-RevokedCertificates {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$revokedCerts
    )
    try {
        $revokedCerts | ConvertTo-Json | Set-Content -Path $script:RevokedCertsFile
    } catch {
        Write-Warning "Failed to save revoked certificates: $($_)"
        Write-Log "Failed to save revoked certificates: $($_)" -Level 'Warning'
    }
}

# Function for challenge validation with retry
function Complete-ChallengeWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [object]$AuthChain,
        
        [Parameter()]
        [int]$MaxAttempts = 3,
        
        [Parameter()]
        [int]$PropagationWait = 30
    )

    Write-ProgressHelper -Activity "Validating Domain Ownership" -Status "Starting validation process..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        Write-ProgressHelper -Activity "Validating Domain Ownership" `
            -Status "Attempt $attempt of $MaxAttempts" `
            -StepNumber $attempt -TotalSteps $MaxAttempts

        try {
            Complete-AuthChallenge -AuthChain $AuthChain -DnsSleep 0
            
            # Verify authorizations with progress
            $authCount = $AuthChain.Authorization.Count
            $currentAuth = 0
            $allValid = $true
            
            foreach ($authz in $AuthChain.Authorization) {
                $currentAuth++
                Write-ProgressHelper -Activity "Checking Authorization Status" `
                    -Status "Verifying $($authz.Identifier)" `
                    -StepNumber $currentAuth -TotalSteps $authCount
                
                $status = Get-PAAuthorization -AuthUrl $authz.location
                Write-Debug "Authorization status for $($authz.Identifier): $($status.status)"
                
                if ($status.status -ne 'valid') {
                    $allValid = $false
                    $details = $status.challenges | Where-Object { $_.type -eq 'dns-01' } | Select-Object -ExpandProperty error
                    Write-Warning "Authorization failed for $($authz.Identifier): $($details.detail)"
                    Write-Log "Authorization failed for $($authz.Identifier): $($details.detail)" -Level 'Warning'
                }
            }

            if ($allValid) {
                Write-Progress -Activity "Validating Domain Ownership" -Completed
                return $true
            }
        } catch {
            Write-Error "Challenge validation attempt $attempt failed: $($_.Exception.Message)"
            Write-Log "Challenge validation attempt $attempt failed: $($_.Exception.Message)" -Level 'Error'
            
            if ($attempt -eq $MaxAttempts) {
                throw "Challenge validation failed after $MaxAttempts attempts"
            }
            Start-Sleep -Seconds $PropagationWait
        }
    }
    
    Write-Progress -Activity "Validating Domain Ownership" -Completed
    return $false
}
