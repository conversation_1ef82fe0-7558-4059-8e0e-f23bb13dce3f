# Database-Functions.ps1
# Contains functions related to database connectivity

function Test-TcpConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Server,

        [Parameter(Mandatory = $false)]
        [int]$Port = 1433,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMilliseconds = 3000
    )

    try {
        # Extract server name and port if provided in format "server,port"
        if ($Server -match '^([^,]+),(\d+)$') {
            $Server = $matches[1]
            $Port = [int]$matches[2]
        }

        Write-Log "Testing TCP connection to $Server on port $Port" -Level 'DEBUG'

        # Create TCP client
        $tcpClient = New-Object System.Net.Sockets.TcpClient

        # Begin async connection attempt
        $connectResult = $tcpClient.BeginConnect($Server, $Port, $null, $null)

        # Wait for connection with timeout
        $waitResult = $connectResult.AsyncWaitHandle.WaitOne($TimeoutMilliseconds, $false)

        # Check if connection succeeded
        if ($waitResult) {
            try {
                # Complete the connection
                $tcpClient.EndConnect($connectResult)
                $connected = $tcpClient.Connected
            }
            catch {
                $connected = $false
                Write-Log "TCP connection error: $($_.Exception.Message)" -Level 'DEBUG'
            }
        }
        else {
            $connected = $false
            Write-Log "TCP connection timed out after $TimeoutMilliseconds ms" -Level 'DEBUG'
        }

        # Close the connection
        if ($tcpClient.Connected) {
            $tcpClient.Close()
        }

        # Dispose of the client
        $tcpClient.Dispose()

        return $connected
    }
    catch {
        Write-Log "Error in Test-TcpConnection: $($_.Exception.Message)" -Level 'DEBUG'
        return $false
    }
}

function Test-DatabaseConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConnectionString = "",

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [switch]$SkipModuleCheck,

        [Parameter(Mandatory = $false)]
        [switch]$SkipOdbcFallback,

        [Parameter(Mandatory = $false)]
        [switch]$SkipTcpFallback
    )

    # If no connection string is provided, try to get it from CompleteView config
    if ([string]::IsNullOrEmpty($ConnectionString)) {
        try {
            # Get the Management Server path from the global config if available
            $managementServerPath = $null

            if ($script:config -and $script:config.InstallPaths -and $script:config.InstallPaths.ManagementServer) {
                $managementServerPath = $script:config.InstallPaths.ManagementServer
                Write-Log "Using Management Server path from config: $managementServerPath" -Level 'DEBUG'
            }
            else {
                # Try to find the path using our path resolution function
                $installPaths = Find-InstallPath
                if ($installPaths -and $installPaths.ManagementServer) {
                    $managementServerPath = $installPaths.ManagementServer
                    Write-Log "Found Management Server path: $managementServerPath" -Level 'DEBUG'
                }
                else {
                    # Fall back to default path
                    $managementServerPath = "C:\Program Files\Salient Security Platform\CompleteView\Management Server"
                    Write-Log "Using default Management Server path: $managementServerPath" -Level 'WARNING'
                }
            }

            # Path to CompleteView config file that contains DB connection info
            $configPath = Join-Path -Path $managementServerPath -ChildPath "appsettings.json"

            if (Test-Path $configPath) {
                $config = Get-Content $configPath -Raw | ConvertFrom-Json
                $ConnectionString = $config.ConnectionString

                Write-Log "Successfully retrieved database connection string from $configPath" -Level 'INFO'
            }
            else {
                Write-Log "Management Server config file not found at: $configPath" -Level 'WARNING'

                # Try alternative locations
                $altPaths = @(
                    "C:\Program Files\Salient Security Platform\CompleteView\Management Server\appsettings.json",
                    "C:\Program Files (x86)\Salient Security Platform\CompleteView\Management Server\appsettings.json",
                    "C:\Program Files\Salient Systems\CompleteView\Management Server\appsettings.json",
                    "C:\Program Files (x86)\Salient Systems\CompleteView\Management Server\appsettings.json"
                )

                foreach ($altPath in $altPaths) {
                    if (Test-Path $altPath) {
                        Write-Log "Found alternative config file at: $altPath" -Level 'INFO'
                        try {
                            $config = Get-Content $altPath -Raw | ConvertFrom-Json
                            $ConnectionString = $config.ConnectionString
                            break
                        }
                        catch {
                            Write-Log "Error reading alternative config file: $($_.Exception.Message)" -Level 'WARNING'
                        }
                    }
                }
            }

            if ([string]::IsNullOrEmpty($ConnectionString)) {
                Write-Log "Could not find database connection string in appsettings.json" -Level 'WARNING'
                return $false
            }
        }
        catch {
            Write-Log "Error retrieving database connection string: $($_.Exception.Message)" -Level 'ERROR'
            return $false
        }
    }

    try {
        # Extract server and database name from connection string
        $serverPattern = "Server=([^;]+)"
        $dbPattern = "Database=([^;]+)"
        $userPattern = "User ID=([^;]+)"
        $passwordPattern = "Password=([^;]+)"

        $server = if ($ConnectionString -match $serverPattern) { $matches[1] } else { $null }
        $database = if ($ConnectionString -match $dbPattern) { $matches[1] } else { $null }
        $userId = if ($ConnectionString -match $userPattern) { $matches[1] } else { $null }
        $password = if ($ConnectionString -match $passwordPattern) { $matches[1] } else { $null }

        if (-not $server -or -not $database) {
            Write-Log "Could not parse server or database from connection string" -Level 'WARNING'
            return $false
        }

        $connected = $false
        $errorMessages = @()

        # Method 1: Try using SQL Server PowerShell module if available and not skipped
        if (-not $SkipModuleCheck) {
            $sqlServerModuleAvailable = Get-Module -ListAvailable -Name SqlServer
            if ($sqlServerModuleAvailable) {
                try {
                    Write-Log "Testing database connection using SqlServer PowerShell module" -Level 'INFO'
                    Import-Module SqlServer -ErrorAction Stop

                    # Create parameter hashtable
                    $sqlParams = @{
                        ServerInstance = $server
                        Database = $database
                        Query = "SELECT 1 AS IsConnected"
                        ConnectionTimeout = 10
                        ErrorAction = 'Stop'
                    }

                    # Add credentials if using SQL authentication
                    if ($userId -and $password) {
                        $sqlCredential = New-Object System.Management.Automation.PSCredential($userId, (ConvertTo-SecureString $password -AsPlainText -Force))
                        $sqlParams.Credential = $sqlCredential
                    }

                    $result = Invoke-Sqlcmd @sqlParams
                    $connected = $result.IsConnected -eq 1

                    if ($connected) {
                        Write-Log "Successfully connected to database using SqlServer module" -Level 'INFO'
                        return $true
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    $errorMessages += "SqlServer module error: $errorMsg"
                    Write-Log "SqlServer module connection failed: $errorMsg" -Level 'WARNING'
                }
            }
            else {
                Write-Log "SqlServer PowerShell module not available" -Level 'INFO'
            }
        }

        # Method 2: Try using .NET SqlClient
        try {
            Write-Log "Testing database connection using .NET SqlClient" -Level 'INFO'
            $conn = New-Object System.Data.SqlClient.SqlConnection
            $conn.ConnectionString = $ConnectionString
            $conn.Open()
            $connected = $conn.State -eq [System.Data.ConnectionState]::Open

            if ($connected) {
                Write-Log "Successfully connected to database using .NET SqlClient" -Level 'INFO'
                $conn.Close()
                return $true
            }
            $conn.Close()
        }
        catch {
            $errorMsg = $_.Exception.Message
            $errorMessages += ".NET SqlClient error: $errorMsg"
            Write-Log ".NET SqlClient connection failed: $errorMsg" -Level 'WARNING'
        }

        # Method 3: Try using ODBC if not skipped
        if (-not $SkipOdbcFallback) {
            try {
                Write-Log "Testing database connection using ODBC" -Level 'INFO'

                # Build ODBC connection string
                $odbcConnString = "Driver={SQL Server};"
                $odbcConnString += "Server=$server;"
                $odbcConnString += "Database=$database;"

                if ($userId -and $password) {
                    $odbcConnString += "UID=$userId;PWD=$password;"
                }
                else {
                    $odbcConnString += "Trusted_Connection=Yes;"
                }

                # Create and open connection
                $odbcConn = New-Object System.Data.Odbc.OdbcConnection
                $odbcConn.ConnectionString = $odbcConnString
                $odbcConn.Open()
                $connected = $odbcConn.State -eq [System.Data.ConnectionState]::Open

                if ($connected) {
                    Write-Log "Successfully connected to database using ODBC" -Level 'INFO'
                    $odbcConn.Close()
                    return $true
                }
                $odbcConn.Close()
            }
            catch {
                $errorMsg = $_.Exception.Message
                $errorMessages += "ODBC error: $errorMsg"
                Write-Log "ODBC connection failed: $errorMsg" -Level 'WARNING'
            }
        }

        # Method 4: Last resort - just check TCP connectivity if not skipped
        if (-not $SkipTcpFallback) {
            try {
                Write-Log "Testing basic TCP connectivity to SQL Server" -Level 'INFO'
                $tcpConnected = Test-TcpConnection -Server $server -TimeoutMilliseconds 5000

                if ($tcpConnected) {
                    Write-Log "TCP connection to SQL Server successful, but database connectivity failed" -Level 'WARNING'
                    # We don't return true here because we only verified network connectivity, not database access
                }
                else {
                    $errorMessages += "TCP connection failed: Cannot establish network connection to SQL Server"
                    Write-Log "TCP connection to SQL Server failed - server may be unreachable" -Level 'WARNING'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                $errorMessages += "TCP test error: $errorMsg"
                Write-Log "TCP connection test failed: $errorMsg" -Level 'WARNING'
            }
        }

        # If we get here, all connection methods failed
        Write-Log "All database connection methods failed" -Level 'WARNING'

        # Combine error messages for notification
        $combinedErrorMsg = $errorMessages -join "`n"

        # Send notification if Slack is enabled
        if (-not $NoSlack -and $WebhookUrl) {
            $messageText = ":warning: *Database Connection Failure*

*Host:* $env:COMPUTERNAME
*Server:* $server
*Database:* $database
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            if ($combinedErrorMsg) {
                $messageText += "`n*Errors:*`n$combinedErrorMsg"
            }

            try {
                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText -QueueOnFailure
                Write-Log "Sent database connection failure notification" -Level 'INFO'
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Failed to send database connection failure notification: $errorMsg" -Level 'ERROR'
            }
        }

        return $false
    }
    catch {
        $mainErrorMsg = $_.Exception.Message
        Write-Log "Error testing database connection: $mainErrorMsg" -Level 'ERROR'

        # Send notification if Slack is enabled
        if (-not $NoSlack -and $WebhookUrl) {
            $messageText = ":warning: *Database Connection Error*

*Host:* $env:COMPUTERNAME
*Server:* $server
*Database:* $database
*Error:* $mainErrorMsg
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            try {
                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText -QueueOnFailure
                Write-Log "Sent database connection error notification" -Level 'INFO'
            }
            catch {
                $slackErrorMsg = $_.Exception.Message
                Write-Log "Failed to send database connection error notification: $slackErrorMsg" -Level 'ERROR'
            }
        }

        return $false
    }
}
