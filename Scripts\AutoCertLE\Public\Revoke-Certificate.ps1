<#
.SYNOPSIS
    Revokes a certificate with the ACME server.

.DESCRIPTION
    Revokes a certificate with the ACME server and adds it to the list of revoked certificates.

.PARAMETER MainDomain
    The main domain of the certificate to revoke.

.EXAMPLE
    Revoke-Certificate -MainDomain "example.com"
#>
function Revoke-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
    param (
        [Parameter(Mandatory=$true)]
        [string]$MainDomain
    )

    # Ensure the ACME server is set
    Initialize-ACMEServer

    try {
        # Get the certificate
        $cert = Get-CachedPACertificate -MainDomain $MainDomain
        if (-not $cert) {
            Write-Error "Certificate for $MainDomain not found."
            Write-Log "Certificate for $MainDomain not found during revocation attempt." -Level 'Error'
            return
        }

        # Display certificate information
        Write-Host "`nCertificate Information:" -ForegroundColor Cyan
        Write-Host "Domain: $MainDomain"
        Write-Host "Issuer: $($cert.Certificate.Issuer)"
        Write-Host "Valid Until: $($cert.Certificate.NotAfter)"
        Write-Host "Subject Alternative Names: $($cert.Certificate.DnsNameList -join ', ')"

        # Confirm revocation
        $confirm = Read-Host "`nAre you sure you want to revoke this certificate? This action cannot be undone. (Y/N)"
        if ($confirm -notmatch '^[Yy]$') {
            Write-Host "`nRevocation cancelled." -ForegroundColor Yellow
            return
        }

        # Revoke the certificate
        if ($PSCmdlet.ShouldProcess($MainDomain, "Revoke certificate")) {
            Revoke-PACertificate -MainDomain $MainDomain -Force
            
            # Add to revoked certificates list
            $revokedCerts = Get-RevokedCertificates
            $revokedCerts += [PSCustomObject]@{
                MainDomain = $MainDomain
                RevokedDate = (Get-Date).ToString('o')
                Issuer = $cert.Certificate.Issuer
                NotAfter = $cert.Certificate.NotAfter.ToString('o')
                Thumbprint = $cert.Certificate.Thumbprint
            }
            Save-RevokedCertificates -revokedCerts $revokedCerts
            
            Write-Host "`nCertificate for $MainDomain has been revoked." -ForegroundColor Green
            Write-Log "Certificate for $MainDomain has been revoked." -Level 'Success'
            
            # Ask if the user wants to delete the certificate as well
            $deleteAlso = Read-Host "`nDo you also want to delete the certificate from Posh-ACME? (Y/N)"
            if ($deleteAlso -match '^[Yy]$') {
                Remove-Certificate -MainDomain $MainDomain
            }
        }
    } catch {
        Write-Error "Failed to revoke certificate: $($_)"
        Write-Log "Failed to revoke certificate for ${MainDomain}: $($_)" -Level 'Error'
    }
    
    Read-Host "`nPress Enter to return to the main menu"
}
