# Test-SlackNotification.ps1
# Script to test the Slack notification functionality in CVDemoCrashAlert.ps1

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidatePattern('^https://hooks\.slack\.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$')]
    [string]$SlackWebhookUrl = "*******************************************************************************",

    [Parameter(Mandatory = $false)]
    [string]$LogPath = "C:\ProgramData\Salient Security Platform\CrashDumps\SlackTest.log",

    [Parameter(Mandatory = $false)]
    [switch]$TestProxy,

    [Parameter(Mandatory = $false)]
    [switch]$TestRecordingServer = $true,

    [Parameter(Mandatory = $false)]
    [switch]$TestAdminService = $true,

    [Parameter(Mandatory = $false)]
    [switch]$TestManagementServer = $true
)

# Source the functions from CVDemoCrashAlert.ps1
. .\CVDemoCrashAlert.ps1

# Create a test message
function Test-SlackNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [string]$TestName = "Basic Test",

        [Parameter(Mandatory = $false)]
        [bool]$SimulateDumpAvailable = $true,

        [Parameter(Mandatory = $false)]
        [bool]$InitialAlert = $true,

        [Parameter(Mandatory = $false)]
        [ValidateSet("RecordingServer", "AdminService", "ManagementServer")]
        [string]$ServiceType = "RecordingServer"
    )

    Write-Log "Running Slack notification test: $TestName" -Level 'INFO'

    try {
        $hostname = $env:COMPUTERNAME
        $crashTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $faultingModule = "test_module.dll"
        $exceptionCode = "0xC0000005"

        # Configure service-specific settings
        switch ($ServiceType) {
            "RecordingServer" {
                $appName = "RecordingServer64.exe"
                $dumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\RecordingServer\RecordingServer64.12345.dmp"
            }
            "AdminService" {
                $appName = "AdminService64.exe"
                $dumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\AdminService\AdminService64.12345.dmp"
            }
            "ManagementServer" {
                $appName = "ManagementServer.exe"
                $dumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\ManagementServer\ManagementServer.12345.dmp"
            }
            default {
                $appName = "RecordingServer64.exe"
                $dumpPath = "C:\ProgramData\Salient Security Platform\CrashDumps\RecordingServer\RecordingServer64.12345.dmp"
            }
        }

        # Send the notification
        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $crashTime `
            -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $dumpPath `
            -WebhookUrl $WebhookUrl -DumpAvailable:$SimulateDumpAvailable -InitialAlert:$InitialAlert

        Write-Log "Test '$TestName' completed successfully" -Level 'INFO'
        return $true
    }
    catch {
        Write-Log "Test '$TestName' failed: $($_.Exception.Message)" -Level 'ERROR'
        return $false
    }
}

# Main test execution
try {
    Write-Log "Starting Slack notification tests" -Level 'INFO'

    # Define test scenarios
    $testScenarios = @(
        @{ Name = "Initial Alert (Dump Available)"; DumpAvailable = $true; InitialAlert = $true },
        @{ Name = "Initial Alert (No Dump)"; DumpAvailable = $false; InitialAlert = $true },
        @{ Name = "Follow-up Alert (Dump Available)"; DumpAvailable = $true; InitialAlert = $false },
        @{ Name = "Follow-up Alert (No Dump)"; DumpAvailable = $false; InitialAlert = $false }
    )

    # Define services to test
    $servicesToTest = @()

    if ($TestRecordingServer) {
        $servicesToTest += "RecordingServer"
    }

    if ($TestAdminService) {
        $servicesToTest += "AdminService"
    }

    if ($TestManagementServer) {
        $servicesToTest += "ManagementServer"
    }

    # Check if any services are selected for testing
    if ($servicesToTest.Count -eq 0) {
        Write-Log "No services selected for testing. Please enable at least one service." -Level 'WARNING'
        return
    }

    # Log which services are being tested
    $servicesList = $servicesToTest -join ", "
    Write-Log "Testing the following services: $servicesList" -Level 'INFO'

    # Run tests for each service and scenario
    foreach ($service in $servicesToTest) {
        foreach ($scenario in $testScenarios) {
            $testName = "$($scenario.Name) - $service"

            Test-SlackNotification -WebhookUrl $SlackWebhookUrl -TestName $testName `
                -SimulateDumpAvailable $scenario.DumpAvailable -InitialAlert $scenario.InitialAlert `
                -ServiceType $service

            # Wait a moment between tests
            Start-Sleep -Seconds 2
        }
    }

    # Test proxy if requested
    if ($TestProxy) {
        Write-Log "Testing with proxy settings..." -Level 'INFO'

        # Create a test with explicit proxy settings
        $params = @{
            Uri = $SlackWebhookUrl
            Method = 'Post'
            Body = (@{ text = "Test message with explicit proxy settings" } | ConvertTo-Json)
            ContentType = 'application/json'
            TimeoutSec = 30
            ErrorAction = 'Stop'
        }

        # Add proxy support if system is configured to use a proxy
        try {
            $systemProxy = [System.Net.WebRequest]::GetSystemWebProxy()
            if ($systemProxy -and $systemProxy.GetProxy([uri]$SlackWebhookUrl) -ne $SlackWebhookUrl) {
                $proxyUrl = $systemProxy.GetProxy([uri]$SlackWebhookUrl)
                Write-Log "Using system proxy for Slack API request: $proxyUrl" -Level 'INFO'

                # Only set the proxy if we have a valid URI
                if ($proxyUrl -and $proxyUrl.AbsoluteUri) {
                    $params.UseDefaultCredentials = $true
                    $params.Proxy = $proxyUrl.AbsoluteUri
                    $params.ProxyUseDefaultCredentials = $true
                }
            }
        }
        catch {
            Write-Log "Error detecting proxy settings: $($_.Exception.Message)" -Level 'WARNING'
            # Continue without proxy settings
        }

        $response = Invoke-RestMethod @params
        Write-Log "Proxy test response: $response" -Level 'INFO'
    }

    Write-Log "All tests completed" -Level 'INFO'
}
catch {
    Write-Log "Error in test script: $($_.Exception.Message)" -Level 'ERROR'
}
