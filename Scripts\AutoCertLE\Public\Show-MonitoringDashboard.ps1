<#
.SYNOPSIS
    Displays the certificate monitoring dashboard.

.DESCRI<PERSON><PERSON><PERSON>
    Shows the certificate monitoring dashboard with expiration status and renewal options.

.PARAMETER AutoRenew
    Automatically renew certificates that are nearing expiration.

.PARAMETER SendNotification
    Send email notifications for certificates that need attention.

.EXAMPLE
    Show-MonitoringDashboard

.EXAMPLE
    Show-MonitoringDashboard -AutoRenew

.EXAMPLE
    Show-MonitoringDashboard -SendNotification
#>
function Show-MonitoringDashboard {
    [CmdletBinding()]
    param (
        [Parameter()]
        [switch]$AutoRenew,
        
        [Parameter()]
        [switch]$SendNotification
    )
    
    while ($true) {
        Clear-Host
        $settings = Get-ScriptSettings
        
        Write-Host "=== Certificate Monitoring Dashboard ===`n" -ForegroundColor Cyan
        
        if (-not $settings.EnableExpirationMonitoring) {
            Write-Warning "Certificate monitoring is disabled. Enable it in the settings menu."
            Write-Host "`nOptions:"
            Write-Host "1) Enable monitoring"
            Write-Host "0) Back"
            
            $choice = Read-Host "`nEnter your choice (0-1)"
            
            switch ($choice) {
                '0' { return }
                '1' {
                    $settings.EnableExpirationMonitoring = $true
                    Save-ScriptSettings -Settings $settings
                    Write-Host "`nCertificate monitoring enabled." -ForegroundColor Green
                    Start-Sleep -Seconds 1
                }
                default { Write-Warning "`nInvalid selection." }
            }
            
            continue
        }
        
        # Display monitoring report
        $report = Show-CertificateMonitoringReport -AutoRenew:$AutoRenew -SendNotification:$SendNotification
        
        Write-Host "`nOptions:"
        Write-Host "1) Refresh report"
        Write-Host "2) Configure monitoring settings"
        Write-Host "3) Schedule monitoring"
        Write-Host "4) Renew selected certificate"
        Write-Host "5) Renew all critical certificates"
        Write-Host "0) Back"
        
        $choice = Read-Host "`nEnter your choice (0-5)"
        
        switch ($choice) {
            '0' { return }
            '1' {
                # Refresh report (will happen automatically on next loop)
                Write-Host "`nRefreshing report..." -ForegroundColor Yellow
                Start-Sleep -Seconds 1
            }
            '2' {
                # Configure monitoring settings
                Write-Host "`nMonitoring Settings:" -ForegroundColor Cyan
                Write-Host "1) Warning threshold (current: $($settings.ExpirationWarningThresholdDays) days)"
                Write-Host "2) Critical threshold (current: $($settings.ExpirationCriticalThresholdDays) days)"
                Write-Host "3) Auto-renew (current: $($AutoRenew))"
                Write-Host "4) Send notifications (current: $($SendNotification))"
                Write-Host "0) Back"
                
                $settingChoice = Read-Host "`nEnter your choice (0-4)"
                
                switch ($settingChoice) {
                    '0' { }
                    '1' {
                        $threshold = Read-Host "`nEnter warning threshold in days"
                        if ($threshold -match '^\d+$') {
                            $settings.ExpirationWarningThresholdDays = [int]$threshold
                            Save-ScriptSettings -Settings $settings
                            Write-Host "`nWarning threshold updated." -ForegroundColor Green
                        } else {
                            Write-Warning "`nInvalid threshold value."
                        }
                    }
                    '2' {
                        $threshold = Read-Host "`nEnter critical threshold in days"
                        if ($threshold -match '^\d+$') {
                            $settings.ExpirationCriticalThresholdDays = [int]$threshold
                            Save-ScriptSettings -Settings $settings
                            Write-Host "`nCritical threshold updated." -ForegroundColor Green
                        } else {
                            Write-Warning "`nInvalid threshold value."
                        }
                    }
                    '3' {
                        $enable = Read-Host "`nEnable auto-renew? (Y/N)"
                        $AutoRenew = $enable -match '^[Yy]$'
                        Write-Host "`nAuto-renew $(if ($AutoRenew) { 'enabled' } else { 'disabled' })." -ForegroundColor Green
                    }
                    '4' {
                        $enable = Read-Host "`nEnable notifications? (Y/N)"
                        $SendNotification = $enable -match '^[Yy]$'
                        Write-Host "`nNotifications $(if ($SendNotification) { 'enabled' } else { 'disabled' })." -ForegroundColor Green
                    }
                    default { Write-Warning "`nInvalid selection." }
                }
            }
            '3' {
                # Schedule monitoring
                Write-Host "`nSchedule Monitoring:" -ForegroundColor Cyan
                Write-Host "1) Enable scheduled monitoring"
                Write-Host "2) Disable scheduled monitoring"
                Write-Host "0) Back"
                
                $scheduleChoice = Read-Host "`nEnter your choice (0-2)"
                
                switch ($scheduleChoice) {
                    '0' { }
                    '1' {
                        $hour = Read-Host "`nEnter hour (0-23)"
                        $minute = Read-Host "Enter minute (0-59)"
                        
                        if ($hour -match '^\d+$' -and [int]$hour -ge 0 -and [int]$hour -le 23 -and
                            $minute -match '^\d+$' -and [int]$minute -ge 0 -and [int]$minute -le 59) {
                            
                            $autoRenewChoice = Read-Host "Enable auto-renew? (Y/N)"
                            $autoRenew = $autoRenewChoice -match '^[Yy]$'
                            
                            $notifyChoice = Read-Host "Enable notifications? (Y/N)"
                            $notify = $notifyChoice -match '^[Yy]$'
                            
                            Set-CertificateMonitoringSchedule -Enable -DailyHour ([int]$hour) -DailyMinute ([int]$minute) -AutoRenew:$autoRenew -SendNotification:$notify
                        } else {
                            Write-Warning "`nInvalid time values."
                        }
                    }
                    '2' {
                        Set-CertificateMonitoringSchedule -Disable
                    }
                    default { Write-Warning "`nInvalid selection." }
                }
            }
            '4' {
                # Renew selected certificate
                if (-not $report -or $report.Certificates.Count -eq 0) {
                    Write-Warning "`nNo certificates found."
                    continue
                }
                
                Write-Host "`nSelect certificate to renew:"
                for ($i = 0; $i -lt $report.Certificates.Count; $i++) {
                    $cert = $report.Certificates[$i]
                    $statusColor = switch ($cert.Status) {
                        'OK' { 'Green' }
                        'Warning' { 'Yellow' }
                        'Critical' { 'Red' }
                        'Expired' { 'Red' }
                        default { 'White' }
                    }
                    
                    Write-Host "$($i + 1)) $($cert.Certificate.MainDomain) ($($cert.Status))" -ForegroundColor $statusColor
                }
                
                $certChoice = Read-Host "`nEnter certificate number (1-$($report.Certificates.Count))"
                if ($certChoice -match '^\d+$' -and [int]$certChoice -ge 1 -and [int]$certChoice -le $report.Certificates.Count) {
                    $selectedCert = $report.Certificates[[int]$certChoice - 1]
                    
                    Write-Host "`nRenewing certificate for $($selectedCert.Certificate.MainDomain)..." -ForegroundColor Yellow
                    $result = Invoke-AutoCertRenewal -MainDomain $selectedCert.Certificate.MainDomain -Force
                    
                    if ($result) {
                        Write-Host "`nCertificate renewed successfully." -ForegroundColor Green
                    } else {
                        Write-Warning "`nFailed to renew certificate."
                    }
                } else {
                    Write-Warning "`nInvalid selection."
                }
            }
            '5' {
                # Renew all critical certificates
                if (-not $report) {
                    Write-Warning "`nNo certificates found."
                    continue
                }
                
                $criticalCerts = $report.Certificates | Where-Object { $_.IsCritical -or $_.IsExpired }
                
                if ($criticalCerts.Count -eq 0) {
                    Write-Host "`nNo critical certificates found." -ForegroundColor Green
                    continue
                }
                
                Write-Host "`nRenewing $($criticalCerts.Count) critical certificates..." -ForegroundColor Yellow
                
                $successCount = 0
                $failCount = 0
                
                foreach ($cert in $criticalCerts) {
                    Write-Host "Renewing $($cert.Certificate.MainDomain)..." -ForegroundColor Yellow
                    $result = Invoke-AutoCertRenewal -MainDomain $cert.Certificate.MainDomain -Force
                    
                    if ($result) {
                        Write-Host "  Success" -ForegroundColor Green
                        $successCount++
                    } else {
                        Write-Host "  Failed" -ForegroundColor Red
                        $failCount++
                    }
                }
                
                Write-Host "`nRenewal complete: $successCount succeeded, $failCount failed." -ForegroundColor $(if ($failCount -eq 0) { 'Green' } else { 'Yellow' })
            }
            default { Write-Warning "`nInvalid selection." }
        }
        
        Read-Host "`nPress Enter to continue"
    }
}
