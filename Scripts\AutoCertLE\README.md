# AutoC<PERSON>LE

A PowerShell module for automated Let's Encrypt certificate management, specifically designed for Salient CompleteView environments.

![AutoCertLE Logo](https://raw.githubusercontent.com/zaaarin/AutoCertLE/main/docs/images/autocertle-logo.png)

## Overview

AutoCertLE simplifies the process of obtaining, installing, and renewing Let's Encrypt SSL certificates for Salient CompleteView environments. It handles the unique requirements of both Management Servers and Recording Servers, ensuring proper certificate installation in the correct formats and locations.

### Key Features

- **Automated Certificate Management**: Request, install, and renew Let's Encrypt certificates
- **Salient CompleteView Integration**: Specialized support for Management and Recording Servers
- **Automatic DNS Provider Detection**: Identifies your DNS provider for streamlined setup
- **Multi-Server Distribution**: Deploy certificates to multiple servers with a single command
- **Certificate Monitoring**: Track certificate expiration and receive alerts
- **Secure Credential Management**: Safely store and manage API credentials
- **Robust Error Handling**: Comprehensive error detection and recovery
- **WinRM Support**: Secure remote certificate deployment using Windows Remote Management

## Module Structure

```
AutoCertLE/
├── Public/                  # Public functions (exported)
│   ├── Register-Certificate.ps1
│   ├── Install-Certificate.ps1
│   ├── Install-SalientCertificate.ps1
│   ├── Send-SalientCertificate.ps1
│   └── ...
├── Private/                 # Private functions (internal)
│   ├── Salient-Functions.ps1
│   ├── DNS-Detection.ps1
│   ├── WinRM-Functions.ps1
│   └── ...
├── Tests/                   # Test scripts
├── AutoCertLE.psd1          # Module manifest
├── AutoCertLE.psm1          # Module loader
└── Start-AutoCertLE.ps1     # Main entry point
```

## Installation

### Prerequisites

- Windows PowerShell 5.1 or PowerShell Core 7.0+
- .NET Framework 4.7.2 or .NET Core 3.1+
- Administrator privileges
- Internet connectivity
- DNS provider API access (for automatic validation)

### Install from PowerShell Gallery

```powershell
Install-Module -Name AutoCertLE -Scope CurrentUser
```

### Manual Installation

1. Download the latest release from [GitHub](https://github.com/zaaarin/AutoCertLE/releases)
2. Extract the ZIP file to a directory of your choice
3. Import the module:

```powershell
Import-Module -Path "C:\Path\To\AutoCertLE\AutoCertLE.psd1"
```

## Quick Start

### Launch the Interactive Menu

```powershell
Start-AutoCertLE
```

### Register a New Certificate

```powershell
Register-Certificate -Domain "example.com" -Email "<EMAIL>" -AcceptTOS
```

### Install a Certificate to a Salient Server

```powershell
# Get existing certificates
$certs = Get-ExistingCertificates

# Install to a Management Server
Install-SalientCertificate -Certificate $certs[0] -ServerType ManagementServer

# Install to a Recording Server
Install-SalientCertificate -Certificate $certs[0] -ServerType RecordingServer
```

### Distribute a Certificate to Multiple Servers

```powershell
Send-SalientCertificate -Certificate $certs[0] -ManagementServers "MS01","MS02" -RecordingServers "RS01","RS02"
```

## Detailed Usage

### Certificate Registration

The module supports multiple validation methods:

#### DNS Validation (Recommended)

```powershell
Register-Certificate -Domain "example.com" -Email "<EMAIL>" -AcceptTOS -Plugin "Cloudflare"
```

The module will automatically detect your DNS provider and suggest the appropriate plugin. Supported providers include:

- Cloudflare
- AWS Route53
- Azure DNS
- GoDaddy
- Namecheap
- DigitalOcean
- And many more...

#### HTTP Validation

```powershell
Register-Certificate -Domain "example.com" -Email "<EMAIL>" -AcceptTOS -ValidationMethod "Http"
```

### Wildcard Certificates

```powershell
Register-Certificate -Domain "*.example.com" -Email "<EMAIL>" -AcceptTOS -Plugin "Cloudflare"
```

Note: Wildcard certificates require DNS validation.

### Certificate Installation

#### Management Server Installation

Management Servers use the Windows certificate store:

```powershell
Install-SalientCertificate -Certificate $cert -ServerType ManagementServer -InstallToLocalMachine
```

#### Recording Server Installation

Recording Servers use PEM files with specific naming conventions:

```powershell
Install-SalientCertificate -Certificate $cert -ServerType RecordingServer -CreateFolders
```

#### Remote Installation

```powershell
# Using file shares
Install-SalientCertificate -Certificate $cert -ServerType RecordingServer -ComputerName "RS01"

# Using WinRM
Install-SalientCertificate -Certificate $cert -ServerType RecordingServer -ComputerName "RS01" -UseWinRM
```

### Certificate Distribution

Distribute certificates to multiple servers:

```powershell
# Using file shares
Send-SalientCertificate -Certificate $cert -ManagementServers "MS01","MS02" -RecordingServers "RS01","RS02"

# Using WinRM
Send-SalientCertificate -Certificate $cert -ManagementServers "MS01","MS02" -RecordingServers "RS01","RS02" -UseWinRM
```

### Automatic Renewal

Configure automatic renewal for certificates:

```powershell
Set-AutomaticRenewal -Domain "example.com" -Enable
```

### Certificate Monitoring

Monitor certificate expiration:

```powershell
# Generate a monitoring report
Get-CertificateMonitoringReport

# Show the monitoring dashboard
Show-MonitoringDashboard

# Schedule monitoring with email notifications
Set-CertificateMonitoringSchedule -Enable -DailyHour 8 -DailyMinute 0 -AutoRenew -SendNotification
```

### Security Features

```powershell
# Backup ACME account key
Backup-ACMEAccountKey -BackupPath "C:\Backups\ACME_Backup.zip"

# Restore ACME account key
Restore-ACMEAccountKey -BackupPath "C:\Backups\ACME_Backup.zip"

# Test certificate key security
Test-CertificateKeySecurity -Certificate $cert
```

## Salient CompleteView Integration

### Server Type Detection

The module automatically detects whether a server is a Management Server, Recording Server, or both:

```powershell
$serverInfo = Get-SalientServerType -ComputerName "Server01"
$serverInfo.IsManagementServer # True/False
$serverInfo.IsRecordingServer # True/False
```

### Certificate Folder Detection

For Recording Servers, the module automatically finds the certificate folder:

```powershell
$certFolder = Get-RecordingServerCertFolder -ComputerName "RS01"
```

### Certificate Numbering

Recording Server certificates use a specific naming convention (cert000.pem, pvkey000.pem). The module automatically determines the next available number:

```powershell
$nextNumber = Get-NextCertificateNumber -CertFolder $certFolder
$formattedNumber = Format-CertificateNumber -Number $nextNumber
# Results in "001", "002", etc.
```

## Advanced Features

### WinRM-Based Certificate Distribution

For environments where file shares are restricted, use WinRM:

```powershell
# Test WinRM connectivity
Test-WinRMConnectivity -ComputerName "Server01"

# Copy certificate using WinRM
Copy-CertificateViaWinRM -ComputerName "Server01" -Certificate $cert -ServerType "ManagementServer"
```

### DNS Provider Detection

The module can automatically detect your DNS provider:

```powershell
# Get basic provider information
$provider = Get-DomainDNSProvider -Domain "example.com"

# Get detailed provider information
$providerDetails = Get-DomainDNSProvider -Domain "example.com" -IncludeDetails

# Get suggested DNS plugin
$dnsPlugin = Get-SuggestedDNSPlugin -Domain "example.com"
```

### Email Notifications

Configure email notifications for certificate events:

```powershell
Set-EmailNotificationSettings -SMTPServer "smtp.example.com" -From "<EMAIL>" -To "<EMAIL>"
```

### Pre/Post Renewal Hooks

Execute custom scripts before or after certificate renewal:

```powershell
Set-RenewalHooks -Domain "example.com" -PreRenewalScript "C:\Scripts\PreRenewal.ps1" -PostRenewalScript "C:\Scripts\PostRenewal.ps1"
```

## Troubleshooting

### Logging

The module logs all operations to a log file:

```powershell
# Export logs for troubleshooting
Export-Logs -Path "C:\Logs\AutoCertLE.log"
```

### Common Issues

#### Certificate Registration Fails

- Verify DNS provider credentials
- Check domain ownership
- Ensure proper DNS propagation

#### Certificate Installation Fails

- Verify server connectivity
- Check permissions
- Ensure certificate folders exist

#### WinRM Connectivity Issues

- Enable WinRM on target servers: `Enable-PSRemoting -Force`
- Configure firewall rules: `Set-NetFirewallRule -Name "WINRM-HTTP-In-TCP" -Enabled True`
- Use HTTPS for WinRM: `New-SelfSignedCertificate` and `New-WSManInstance`

## Testing

The module includes a comprehensive test suite:

```powershell
# Run all tests
.\Tests\Run-AllTests.ps1 -Domain "test.example.com" -Email "<EMAIL>"

# Run specific tests
.\Tests\Run-Tests.ps1                # Unit tests
.\Tests\Test-Integration.ps1         # Integration tests
.\Tests\Test-EndToEnd.ps1            # End-to-end tests
.\Tests\Test-Load.ps1                # Load tests
```

### Testing Best Practices

1. **Use the Let's Encrypt Staging Environment**: All test scripts use the staging environment to avoid rate limits.

2. **Test with Real DNS Providers**: For thorough testing, use real DNS providers with actual credentials.

3. **Test on Both Management and Recording Servers**: Ensure the module works correctly on both server types.

4. **Test Remote Operations**: Verify that certificate distribution works across multiple servers.

5. **Test Error Handling**: Deliberately introduce errors to verify proper error handling.

6. **Test with Different Domain Types**: Test with regular domains, wildcard domains, and multiple domains.

7. **Test Automatic Renewal**: Verify that automatic renewal works correctly.

8. **Test Security Features**: Verify that credentials are stored securely and certificate keys are properly protected.

9. **Test Performance**: Measure the time taken for various operations and optimize as needed.

10. **Test User Experience**: Ensure the module is user-friendly and provides clear guidance.

## FAQ

### Q: Can I use this module with other systems besides Salient CompleteView?
A: Yes, the core certificate management functions work with any system. The Salient-specific functions are optional.

### Q: Does this module work with Let's Encrypt rate limits?
A: Yes, the module uses the staging environment for testing and includes safeguards to prevent hitting rate limits in production.

### Q: Can I use wildcard certificates?
A: Yes, but they require DNS validation, which means you need API access to your DNS provider.

### Q: How do I update the module?
A: If installed from the PowerShell Gallery: `Update-Module -Name AutoCertLE`

### Q: Can I use this module in a scheduled task?
A: Yes, the module includes functions for unattended operation. Use `Invoke-AutoCertRenewal` in your scheduled tasks.

### Q: How do I migrate from another certificate management solution?
A: The module can import existing certificates. Contact us for migration assistance.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Posh-ACME](https://github.com/rmbolger/Posh-ACME) for the underlying ACME client
- [Salient Systems](https://www.salientsys.com/) for CompleteView
- All contributors and testers

---

For more information, visit the [GitHub repository](https://github.com/zaaarin/AutoCertLE) or contact the author at [<EMAIL>](mailto:<EMAIL>).
