# Functions for automatic DNS provider detection

# Function to detect DNS provider for a domain
function Get-DomainDNSProvider {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Domain,
        
        [Parameter()]
        [switch]$IncludeDetails,
        
        [Parameter()]
        [switch]$Force
    )
    
    try {
        Write-Verbose "Detecting DNS provider for domain: $Domain"
        
        # Check if we have cached results for this domain
        $cacheFile = Join-Path -Path "$env:LOCALAPPDATA\PoshACME" -ChildPath "dns_provider_cache.json"
        $cacheData = @{}
        
        if (Test-Path $cacheFile -and -not $Force) {
            try {
                $cacheData = Get-Content -Path $cacheFile -Raw | ConvertFrom-Json -AsHashtable
                
                if ($cacheData.ContainsKey($Domain)) {
                    $cachedResult = $cacheData[$Domain]
                    $cacheAge = (Get-Date) - [DateTime]::Parse($cachedResult.Timestamp)
                    
                    # Use cached result if it's less than 7 days old
                    if ($cacheAge.TotalDays -lt 7) {
                        Write-Verbose "Using cached DNS provider information for $Domain (cached $($cacheAge.TotalDays.ToString('0.0')) days ago)"
                        
                        if ($IncludeDetails) {
                            return $cachedResult
                        } else {
                            return $cachedResult.Provider
                        }
                    } else {
                        Write-Verbose "Cached DNS provider information for $Domain is older than 7 days, refreshing..."
                    }
                }
            } catch {
                Write-Warning "Error reading DNS provider cache: $($_.Exception.Message). Will detect provider again."
            }
        }
        
        # Initialize result object
        $result = @{
            Domain = $Domain
            Provider = $null
            NameServers = @()
            DetectionMethod = $null
            Confidence = 0
            Timestamp = (Get-Date).ToString('o')
            AdditionalProviders = @()
        }
        
        # Method 1: Check NS records to identify nameservers
        try {
            $nsRecords = Resolve-DnsName -Name $Domain -Type NS -ErrorAction Stop | 
                Where-Object { $_.Type -eq 'NS' } | 
                Select-Object -ExpandProperty NameHost
            
            if ($nsRecords -and $nsRecords.Count -gt 0) {
                $result.NameServers = $nsRecords
                Write-Verbose "Found nameservers for ${Domain}: $($nsRecords -join ', ')"
            } else {
                Write-Verbose "No NS records found for $Domain"
            }
        } catch {
            Write-Warning "Error resolving NS records for ${Domain}: $($_.Exception.Message)"
        }
        
        # Method 2: Check for CNAME records that might indicate a DNS provider
        try {
            # Common subdomains used for DNS provider verification
            $checkSubdomains = @(
                "_acme-challenge",
                "www",
                "_domainconnect",
                "domain-verify"
            )
            
            foreach ($subdomain in $checkSubdomains) {
                try {
                    $cnameRecord = Resolve-DnsName -Name "$subdomain.$Domain" -Type CNAME -ErrorAction Stop | 
                        Where-Object { $_.Type -eq 'CNAME' } | 
                        Select-Object -ExpandProperty NameHost
                    
                    if ($cnameRecord) {
                        Write-Verbose "Found CNAME record for $subdomain.${Domain}: $cnameRecord"
                        $result.CNAMERecords += @{
                            Subdomain = $subdomain
                            Target = $cnameRecord
                        }
                    }
                } catch {
                    # Ignore errors for non-existent records
                }
            }
        } catch {
            Write-Verbose "Error checking CNAME records: $($_.Exception.Message)"
        }
        
        # Method 3: Check for SOA record
        try {
            $soaRecord = Resolve-DnsName -Name $Domain -Type SOA -ErrorAction Stop | 
                Where-Object { $_.Type -eq 'SOA' } | 
                Select-Object -First 1
            
            if ($soaRecord) {
                $result.SOARecord = @{
                    PrimaryServer = $soaRecord.PrimaryServer
                    ResponsiblePerson = $soaRecord.ResponsiblePerson
                    SerialNumber = $soaRecord.SerialNumber
                }
                Write-Verbose "Found SOA record for ${Domain}: Primary server = $($soaRecord.PrimaryServer)"
            }
        } catch {
            Write-Verbose "Error checking SOA record: $($_.Exception.Message)"
        }
        
        # Method 4: Check for MX records that might indicate a provider
        try {
            $mxRecords = Resolve-DnsName -Name $Domain -Type MX -ErrorAction Stop | 
                Where-Object { $_.Type -eq 'MX' } | 
                Select-Object -ExpandProperty NameExchange
            
            if ($mxRecords -and $mxRecords.Count -gt 0) {
                $result.MXRecords = $mxRecords
                Write-Verbose "Found MX records for ${Domain}: $($mxRecords -join ', ')"
            }
        } catch {
            Write-Verbose "Error checking MX records: $($_.Exception.Message)"
        }
        
        # Method 5: Check for TXT records that might indicate a provider
        try {
            $txtRecords = Resolve-DnsName -Name $Domain -Type TXT -ErrorAction Stop | 
                Where-Object { $_.Type -eq 'TXT' } | 
                Select-Object -ExpandProperty Strings
            
            if ($txtRecords -and $txtRecords.Count -gt 0) {
                $result.TXTRecords = $txtRecords
                Write-Verbose "Found TXT records for $Domain"
            }
        } catch {
            Write-Verbose "Error checking TXT records: $($_.Exception.Message)"
        }
        
        # Method 6: WHOIS lookup for registrar information
        try {
            # Use a simple web API for WHOIS information
            $whoisUrl = "https://www.whoisxmlapi.com/whoisserver/WhoisService?apiKey=at_demo&domainName=$Domain&outputFormat=json"
            $whoisResponse = Invoke-RestMethod -Uri $whoisUrl -Method Get -ErrorAction Stop
            
            if ($whoisResponse.WhoisRecord) {
                $result.Registrar = $whoisResponse.WhoisRecord.registrarName
                Write-Verbose "Found registrar information for ${Domain}: $($result.Registrar)"
            }
        } catch {
            Write-Verbose "Error performing WHOIS lookup: $($_.Exception.Message)"
        }
        
        # Now analyze the collected data to determine the DNS provider
        $providerSignatures = @{
            'Cloudflare' = @{
                NameServerPatterns = @('cloudflare.com', 'ns.cloudflare.com', 'clara.ns.cloudflare.com', 'lloyd.ns.cloudflare.com')
                SOAPatterns = @('cloudflare.com')
                CNAMEPatterns = @('cloudflare.com')
                Confidence = 90
            }
            'Route53' = @{
                NameServerPatterns = @('awsdns', 'amazonaws.com', 'aws')
                SOAPatterns = @('awsdns')
                Confidence = 90
            }
            'GoDaddy' = @{
                NameServerPatterns = @('domaincontrol.com', 'godaddy.com', 'secureserver.net')
                SOAPatterns = @('domaincontrol.com')
                RegistrarPatterns = @('GoDaddy')
                Confidence = 85
            }
            'Namecheap' = @{
                NameServerPatterns = @('namecheap.com', 'registrar-servers.com')
                RegistrarPatterns = @('NAMECHEAP')
                Confidence = 85
            }
            'DigitalOcean' = @{
                NameServerPatterns = @('digitalocean.com', 'ns1.digitalocean.com', 'ns2.digitalocean.com', 'ns3.digitalocean.com')
                Confidence = 85
            }
            'DNSMadeEasy' = @{
                NameServerPatterns = @('dnsmadeeasy.com')
                Confidence = 85
            }
            'Gandi' = @{
                NameServerPatterns = @('gandi.net')
                RegistrarPatterns = @('GANDI SAS')
                Confidence = 85
            }
            'Google' = @{
                NameServerPatterns = @('googledomains.com', 'google.com', 'googleusercontent.com')
                SOAPatterns = @('google.com', 'googledomains.com')
                MXPatterns = @('google.com', 'googlemail.com')
                Confidence = 85
            }
            'Azure' = @{
                NameServerPatterns = @('azure-dns.com', 'azure-dns.net', 'azure-dns.org', 'azure-dns.info')
                Confidence = 90
            }
            'Linode' = @{
                NameServerPatterns = @('linode.com', 'linode-dns')
                Confidence = 85
            }
            'OVH' = @{
                NameServerPatterns = @('ovh.net', 'ovh.com')
                RegistrarPatterns = @('OVH')
                Confidence = 85
            }
            'Vultr' = @{
                NameServerPatterns = @('vultr.com')
                Confidence = 85
            }
            'DreamHost' = @{
                NameServerPatterns = @('dreamhost.com')
                Confidence = 85
            }
            'Hover' = @{
                NameServerPatterns = @('hover.com')
                RegistrarPatterns = @('HOVER')
                Confidence = 85
            }
            'Name.com' = @{
                NameServerPatterns = @('name.com')
                RegistrarPatterns = @('Name.com')
                Confidence = 85
            }
            'Netlify' = @{
                NameServerPatterns = @('netlify.com')
                CNAMEPatterns = @('netlify.com', 'netlify.app')
                Confidence = 85
            }
            'Vercel' = @{
                NameServerPatterns = @('vercel-dns.com')
                CNAMEPatterns = @('vercel.app', 'vercel-dns.com')
                Confidence = 85
            }
            'Cloudways' = @{
                NameServerPatterns = @('cloudways.com')
                Confidence = 80
            }
            'Rackspace' = @{
                NameServerPatterns = @('rackspace.com')
                Confidence = 85
            }
            'Bluehost' = @{
                NameServerPatterns = @('bluehost.com')
                Confidence = 85
            }
            'HostGator' = @{
                NameServerPatterns = @('hostgator.com')
                Confidence = 85
            }
            'SiteGround' = @{
                NameServerPatterns = @('siteground.com')
                Confidence = 85
            }
            'InMotion' = @{
                NameServerPatterns = @('inmotionhosting.com')
                Confidence = 85
            }
            'A2Hosting' = @{
                NameServerPatterns = @('a2hosting.com')
                Confidence = 85
            }
            'Hostinger' = @{
                NameServerPatterns = @('hostinger.com')
                Confidence = 85
            }
            'DynDNS' = @{
                NameServerPatterns = @('dyn.com')
                Confidence = 85
            }
            'Akamai' = @{
                NameServerPatterns = @('akam.net', 'akamai.com')
                Confidence = 85
            }
            'Cloudaccess' = @{
                NameServerPatterns = @('cloudaccess.net')
                Confidence = 85
            }
            'Hetzner' = @{
                NameServerPatterns = @('hetzner.com', 'your-server.de')
                Confidence = 85
            }
            'Infomaniak' = @{
                NameServerPatterns = @('infomaniak.ch')
                Confidence = 85
            }
            'Ionos' = @{
                NameServerPatterns = @('ionos.com', '1and1.com')
                RegistrarPatterns = @('1&1 IONOS')
                Confidence = 85
            }
            'LuaDNS' = @{
                NameServerPatterns = @('luadns.com')
                Confidence = 85
            }
            'NS1' = @{
                NameServerPatterns = @('nsone.net')
                Confidence = 85
            }
            'Plesk' = @{
                NameServerPatterns = @('plesk.com')
                Confidence = 80
            }
            'cPanel' = @{
                SOAPatterns = @('cpanel')
                Confidence = 75
            }
            'Webflow' = @{
                CNAMEPatterns = @('webflow.com', 'webflow.io')
                Confidence = 85
            }
            'Wix' = @{
                CNAMEPatterns = @('wix.com', 'wixdns.net')
                Confidence = 85
            }
            'Squarespace' = @{
                CNAMEPatterns = @('squarespace.com')
                Confidence = 85
            }
            'Shopify' = @{
                CNAMEPatterns = @('shopify.com')
                Confidence = 85
            }
            'WordPress.com' = @{
                NameServerPatterns = @('wordpress.com')
                CNAMEPatterns = @('wordpress.com')
                Confidence = 85
            }
        }
        
        # Score each provider based on the collected data
        $providerScores = @{}
        
        foreach ($provider in $providerSignatures.Keys) {
            $score = 0
            $signature = $providerSignatures[$provider]
            
            # Check nameservers
            if ($signature.NameServerPatterns -and $result.NameServers) {
                foreach ($ns in $result.NameServers) {
                    foreach ($pattern in $signature.NameServerPatterns) {
                        if ($ns -like "*$pattern*") {
                            $score += 30
                            Write-Verbose "Nameserver match for ${provider}: $ns contains $pattern (+30 points)"
                            break
                        }
                    }
                }
            }
            
            # Check SOA record
            if ($signature.SOAPatterns -and $result.SOARecord) {
                foreach ($pattern in $signature.SOAPatterns) {
                    if ($result.SOARecord.PrimaryServer -like "*$pattern*") {
                        $score += 20
                        Write-Verbose "SOA match for ${provider}: $($result.SOARecord.PrimaryServer) contains $pattern (+20 points)"
                        break
                    }
                }
            }
            
            # Check CNAME records
            if ($signature.CNAMEPatterns -and $result.CNAMERecords) {
                foreach ($cname in $result.CNAMERecords) {
                    foreach ($pattern in $signature.CNAMEPatterns) {
                        if ($cname.Target -like "*$pattern*") {
                            $score += 15
                            Write-Verbose "CNAME match for ${provider}: $($cname.Target) contains $pattern (+15 points)"
                            break
                        }
                    }
                }
            }
            
            # Check MX records
            if ($signature.MXPatterns -and $result.MXRecords) {
                foreach ($mx in $result.MXRecords) {
                    foreach ($pattern in $signature.MXPatterns) {
                        if ($mx -like "*$pattern*") {
                            $score += 10
                            Write-Verbose "MX match for ${provider}: $mx contains $pattern (+10 points)"
                            break
                        }
                    }
                }
            }
            
            # Check registrar information
            if ($signature.RegistrarPatterns -and $result.Registrar) {
                foreach ($pattern in $signature.RegistrarPatterns) {
                    if ($result.Registrar -like "*$pattern*") {
                        $score += 25
                        Write-Verbose "Registrar match for ${provider}: $($result.Registrar) contains $pattern (+25 points)"
                        break
                    }
                }
            }
            
            # Only consider providers with a score above 0
            if ($score -gt 0) {
                $providerScores[$provider] = @{
                    Score = $score
                    Confidence = [Math]::Min(100, [Math]::Round(($score / 100) * $signature.Confidence))
                }
            }
        }
        
        # Sort providers by score
        $sortedProviders = $providerScores.GetEnumerator() | Sort-Object { $_.Value.Score } -Descending
        
        # Set the primary provider (highest score)
        if ($sortedProviders.Count -gt 0) {
            $primaryProvider = $sortedProviders[0].Key
            $result.Provider = $primaryProvider
            $result.Confidence = $providerScores[$primaryProvider].Confidence
            $result.DetectionMethod = "Automatic"
            
            Write-Verbose "Primary DNS provider detected: $primaryProvider (Confidence: $($result.Confidence)%)"
            
            # Add additional providers with significant scores
            foreach ($provider in $sortedProviders | Select-Object -Skip 1) {
                if ($provider.Value.Score -gt 20) {
                    $result.AdditionalProviders += @{
                        Provider = $provider.Key
                        Confidence = $provider.Value.Confidence
                    }
                    
                    Write-Verbose "Additional DNS provider detected: $($provider.Key) (Confidence: $($provider.Value.Confidence)%)"
                }
            }
        } else {
            Write-Warning "Could not automatically detect DNS provider for $Domain"
            $result.Provider = "Manual"
            $result.DetectionMethod = "Failed"
            $result.Confidence = 0
        }
        
        # Cache the result
        try {
            if (-not (Test-Path "$env:LOCALAPPDATA\PoshACME")) {
                New-Item -ItemType Directory -Path "$env:LOCALAPPDATA\PoshACME" -Force | Out-Null
            }
            
            if (Test-Path $cacheFile) {
                $cacheData = Get-Content -Path $cacheFile -Raw | ConvertFrom-Json -AsHashtable
            } else {
                $cacheData = @{}
            }
            
            $cacheData[$Domain] = $result
            $cacheData | ConvertTo-Json -Depth 10 | Set-Content -Path $cacheFile -Force
            Write-Verbose "Cached DNS provider information for $Domain"
        } catch {
            Write-Warning "Error caching DNS provider information: $($_.Exception.Message)"
        }
        
        # Return the result
        if ($IncludeDetails) {
            return $result
        } else {
            return $result.Provider
        }
    } catch {
        Write-Error "Error detecting DNS provider for ${Domain}: $($_.Exception.Message)"
        Write-Log "Error detecting DNS provider for ${Domain}: $($_.Exception.Message)" -Level 'Error'
        return "Manual"
    }
}

# Function to map detected provider to Posh-ACME plugin
function Get-PoshACMEPluginForProvider {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Provider
    )
    
    # Map provider names to Posh-ACME plugin names
    $pluginMap = @{
        'Cloudflare' = 'Cloudflare'
        'Route53' = 'Route53'
        'GoDaddy' = 'GoDaddy'
        'Namecheap' = 'Namecheap'
        'DigitalOcean' = 'DOcean'
        'DNSMadeEasy' = 'DNSMadeEasy'
        'Gandi' = 'Gandi'
        'Google' = 'GCloud'
        'Azure' = 'Azure'
        'Linode' = 'Linode'
        'OVH' = 'OVH'
        'Vultr' = 'Vultr'
        'DreamHost' = 'DreamHost'
        'Hover' = 'Hover'
        'Name.com' = 'NameCom'
        'Netlify' = 'Netlify'
        'Vercel' = 'Vercel'
        'Cloudways' = 'Manual'
        'Rackspace' = 'Rackspace'
        'Bluehost' = 'Manual'
        'HostGator' = 'Manual'
        'SiteGround' = 'Manual'
        'InMotion' = 'Manual'
        'A2Hosting' = 'Manual'
        'Hostinger' = 'Manual'
        'DynDNS' = 'DnsCom'
        'Akamai' = 'Akamai'
        'Cloudaccess' = 'Manual'
        'Hetzner' = 'Hetzner'
        'Infomaniak' = 'Infomaniak'
        'Ionos' = 'Ionos'
        'LuaDNS' = 'LuaDns'
        'NS1' = 'NS1'
        'Plesk' = 'Manual'
        'cPanel' = 'Manual'
        'Webflow' = 'Manual'
        'Wix' = 'Manual'
        'Squarespace' = 'Manual'
        'Shopify' = 'Manual'
        'WordPress.com' = 'Manual'
        'Manual' = 'Manual'
    }
    
    if ($pluginMap.ContainsKey($Provider)) {
        return $pluginMap[$Provider]
    } else {
        # Default to Manual if provider not in map
        return 'Manual'
    }
}

# Function to get required parameters for a DNS plugin
function Get-DNSPluginParameters {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$PluginName
    )
    
    # Define parameter requirements for each plugin
    $pluginParams = @{
        'Cloudflare' = @{
            Required = @('CFToken', 'CFZoneID')
            Optional = @()
            Notes = "You'll need a Cloudflare API Token with Zone:DNS:Edit permissions."
        }
        'Route53' = @{
            Required = @('R53AccessKey', 'R53SecretKey')
            Optional = @('R53Region')
            Notes = "You'll need AWS IAM credentials with Route53 permissions."
        }
        'GoDaddy' = @{
            Required = @('GDKey', 'GDSecret')
            Optional = @()
            Notes = "You'll need a GoDaddy API Key and Secret from the GoDaddy Developer Portal."
        }
        'Namecheap' = @{
            Required = @('NCUsername', 'NCApiKey')
            Optional = @()
            Notes = "You'll need your Namecheap username and API key. API access must be enabled from your Namecheap account."
        }
        'DOcean' = @{
            Required = @('DOToken')
            Optional = @()
            Notes = "You'll need a DigitalOcean API token with write access."
        }
        'DNSMadeEasy' = @{
            Required = @('DMEKey', 'DMESecret')
            Optional = @()
            Notes = "You'll need a DNSMadeEasy API key and secret."
        }
        'Gandi' = @{
            Required = @('GandiToken')
            Optional = @()
            Notes = "You'll need a Gandi API key."
        }
        'GCloud' = @{
            Required = @('GCKeyFile')
            Optional = @('GCProject')
            Notes = "You'll need a Google Cloud service account key file."
        }
        'Azure' = @{
            Required = @('AZTenantId', 'AZClientId', 'AZClientSecret')
            Optional = @('AZSubscriptionId', 'AZResourceGroup')
            Notes = "You'll need Azure AD credentials with DNS Zone Contributor permissions."
        }
        'Linode' = @{
            Required = @('LIToken')
            Optional = @()
            Notes = "You'll need a Linode API token with DNS permissions."
        }
        'OVH' = @{
            Required = @('OVHAppKey', 'OVHAppSecret', 'OVHConsumerKey')
            Optional = @('OVHEndpoint')
            Notes = "You'll need OVH API credentials created from the OVH API dashboard."
        }
        'Vultr' = @{
            Required = @('VultrApiKey')
            Optional = @()
            Notes = "You'll need a Vultr API key."
        }
        'Manual' = @{
            Required = @()
            Optional = @()
            Notes = "You'll need to manually create the required DNS records."
        }
    }
    
    if ($pluginParams.ContainsKey($PluginName)) {
        return $pluginParams[$PluginName]
    } else {
        # Return empty parameters for unknown plugins
        return @{
            Required = @()
            Optional = @()
            Notes = "Unknown plugin. You may need to check the Posh-ACME documentation for required parameters."
        }
    }
}

# Function to suggest DNS plugin based on domain
function Get-SuggestedDNSPlugin {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Domain
    )
    
    try {
        # Detect the DNS provider
        $providerDetails = Get-DomainDNSProvider -Domain $Domain -IncludeDetails
        
        # Map to Posh-ACME plugin
        $plugin = Get-PoshACMEPluginForProvider -Provider $providerDetails.Provider
        
        # Get plugin parameters
        $pluginParams = Get-DNSPluginParameters -PluginName $plugin
        
        # Build result
        $result = @{
            Domain = $Domain
            DetectedProvider = $providerDetails.Provider
            Confidence = $providerDetails.Confidence
            SuggestedPlugin = $plugin
            RequiredParameters = $pluginParams.Required
            OptionalParameters = $pluginParams.Optional
            Notes = $pluginParams.Notes
            NameServers = $providerDetails.NameServers
            AdditionalProviders = $providerDetails.AdditionalProviders
        }
        
        return $result
    } catch {
        Write-Error "Error suggesting DNS plugin for ${Domain}: $($_.Exception.Message)"
        Write-Log "Error suggesting DNS plugin for ${Domain}: $($_.Exception.Message)" -Level 'Error'
        
        # Return a fallback to Manual
        return @{
            Domain = $Domain
            DetectedProvider = "Unknown"
            Confidence = 0
            SuggestedPlugin = "Manual"
            RequiredParameters = @()
            OptionalParameters = @()
            Notes = "Could not automatically detect DNS provider. You'll need to manually create the required DNS records."
        }
    }
}
