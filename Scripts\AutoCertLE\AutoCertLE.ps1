<#
.SYNOPSIS
    Automates the management of Let's Encrypt TLS certificates using Posh-ACME for CompleteView servers.

.DESCRIPTION
    This script provides a menu-driven interface for registering, installing,
    renewing, revoking, and deleting TLS certificates using the Posh-ACME module.

.PARAMETER RenewAll
    Forces renewal of all certificates regardless of expiration date.

.PARAMETER NonInteractive
    Runs in non-interactive mode without prompting for user input.

.PARAMETER Force
    Forces renewal even if certificates are not close to expiration.

.EXAMPLE
    .\AutoCertLE.ps1
    Runs the script in interactive mode with the main menu.

.EXAMPLE
    .\AutoCertLE.ps1 -RenewAll -NonInteractive
    Renews all certificates in non-interactive mode.

.NOTES
    Compatible with Posh-ACME version 4.x.
#>
param(
    [switch]$RenewAll,
    [switch]$NonInteractive,
    [switch]$Force
)

# Ensure the script runs with administrative privileges
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole(
    [Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Warning "You need to run this script as an administrator."
    Exit
}

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Import the module
$modulePath = Join-Path -Path $scriptDir -ChildPath "AutoCertLE.psd1"
if (Test-Path $modulePath) {
    Import-Module $modulePath -Force
} else {
    # Try to import from installed modules
    if (-not (Get-Module -ListAvailable -Name AutoCertLE)) {
        Write-Error "AutoCertLE module not found. Please ensure it is installed."
        Exit
    }
    Import-Module AutoCertLE -Force
}

# Check if Posh-ACME module is installed; if not, install it
if (-not (Get-Module -ListAvailable -Name Posh-ACME)) {
    Write-Host "Posh-ACME module not found. Installing..." -ForegroundColor Yellow
    try {
        Install-Module -Name Posh-ACME -Scope CurrentUser -Force -ErrorAction Stop
        Import-Module Posh-ACME
        Write-Host "Posh-ACME module installed successfully." -ForegroundColor Green
    } catch {
        Write-Error "Failed to install Posh-ACME module: $($_)"
        Read-Host "Press Enter to exit"
        exit
    }
} else {
    Import-Module Posh-ACME
}

# Check if running in non-interactive mode with RenewAll parameter
if ($RenewAll) {
    Invoke-AutoCertRenewal -RenewAll -NonInteractive:$NonInteractive -Force:$Force
    exit
}

# Interactive mode
try {
    # Main menu loop
    while ($true) {
        Show-Menu
        $choice = Read-Host "`nEnter your choice (1-8)"

        switch ($choice) {
            '1' { Register-Certificate }
            '2' { Install-Certificate -MainDomain (Read-Host "Enter the domain name for the certificate to install") }
            '3' { Set-AutomaticRenewal }
            '4' { Get-ExistingCertificates }
            '5' { Revoke-Certificate -MainDomain (Read-Host "Enter the domain name for the certificate to revoke") }
            '6' { Remove-Certificate -MainDomain (Read-Host "Enter the domain name for the certificate to delete") }
            '7' { Show-AdvancedOptions }
            '8' {
                Write-Host "`nExiting script. Goodbye!"
                exit
            }
            default { Write-Warning "`nInvalid selection. Please choose 1-8." }
        }
    }
} catch {
    Write-Error "An unexpected error occurred: $($_)"
    Read-Host "`nPress Enter to exit"
}
