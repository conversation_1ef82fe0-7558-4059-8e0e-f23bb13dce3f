<#
.SYNOPSIS
    Sets up a test environment for the AutoCertLE module.

.DESCRIPTION
    This script sets up a test environment for the AutoCertLE module by creating mock Salient CompleteView services.
    This allows testing of Salient-specific features without an actual Salient CompleteView installation.

.PARAMETER CreateManagementServer
    If specified, creates a mock Management Server service.

.PARAMETER CreateRecordingServer
    If specified, creates a mock Recording Server service.

.PARAMETER CreateCertificateFolders
    If specified, creates the certificate folders used by Salient CompleteView.

.PARAMETER Cleanup
    If specified, removes the mock services and folders.

.EXAMPLE
    .\Setup-TestEnvironment.ps1 -CreateManagementServer -CreateRecordingServer -CreateCertificateFolders
#>
[CmdletBinding()]
param (
    [Parameter()]
    [switch]$CreateManagementServer,
    
    [Parameter()]
    [switch]$CreateRecordingServer,
    
    [Parameter()]
    [switch]$CreateCertificateFolders,
    
    [Parameter()]
    [switch]$Cleanup
)

# Requires admin privileges
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Error "This script requires administrator privileges. Please run as administrator."
    return
}

if ($Cleanup) {
    Write-Host "Cleaning up test environment..." -ForegroundColor Cyan
    
    # Remove mock services
    if (Get-Service -Name "CompleteView Management Server" -ErrorAction SilentlyContinue) {
        Write-Host "Removing mock Management Server service..." -ForegroundColor Yellow
        sc.exe delete "CompleteView Management Server"
    }
    
    if (Get-Service -Name "CompleteView Recording Server" -ErrorAction SilentlyContinue) {
        Write-Host "Removing mock Recording Server service..." -ForegroundColor Yellow
        sc.exe delete "CompleteView Recording Server"
    }
    
    # Remove certificate folders
    $certFolders = @(
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
        "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates",
        "C:\Program Files\Symmetry Security Platform\Symmetry CompleteView\Recording Server\Certificates"
    )
    
    foreach ($folder in $certFolders) {
        if (Test-Path $folder) {
            Write-Host "Removing certificate folder: $folder" -ForegroundColor Yellow
            Remove-Item -Path $folder -Recurse -Force
        }
    }
    
    Write-Host "Cleanup completed!" -ForegroundColor Green
    return
}

# Create mock services
if ($CreateManagementServer) {
    Write-Host "Creating mock Management Server service..." -ForegroundColor Cyan
    
    if (-not (Get-Service -Name "CompleteView Management Server" -ErrorAction SilentlyContinue)) {
        sc.exe create "CompleteView Management Server" binPath= "C:\Windows\System32\svchost.exe -k netsvcs" start= auto
        sc.exe description "CompleteView Management Server" "Mock service for testing AutoCertLE module"
        Write-Host "Mock Management Server service created" -ForegroundColor Green
    } else {
        Write-Host "Management Server service already exists" -ForegroundColor Yellow
    }
}

if ($CreateRecordingServer) {
    Write-Host "Creating mock Recording Server service..." -ForegroundColor Cyan
    
    if (-not (Get-Service -Name "CompleteView Recording Server" -ErrorAction SilentlyContinue)) {
        sc.exe create "CompleteView Recording Server" binPath= "C:\Windows\System32\svchost.exe -k netsvcs" start= auto
        sc.exe description "CompleteView Recording Server" "Mock service for testing AutoCertLE module"
        Write-Host "Mock Recording Server service created" -ForegroundColor Green
    } else {
        Write-Host "Recording Server service already exists" -ForegroundColor Yellow
    }
}

# Create certificate folders
if ($CreateCertificateFolders) {
    Write-Host "Creating certificate folders..." -ForegroundColor Cyan
    
    $certFolders = @(
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
        "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates",
        "C:\Program Files\Symmetry Security Platform\Symmetry CompleteView\Recording Server\Certificates"
    )
    
    foreach ($folder in $certFolders) {
        if (-not (Test-Path $folder)) {
            Write-Host "Creating folder: $folder" -ForegroundColor Yellow
            New-Item -Path $folder -ItemType Directory -Force | Out-Null
        } else {
            Write-Host "Folder already exists: $folder" -ForegroundColor Yellow
        }
    }
    
    Write-Host "Certificate folders created" -ForegroundColor Green
}

Write-Host "`nTest environment setup completed!" -ForegroundColor Green
Write-Host "To clean up the test environment, run this script with the -Cleanup parameter." -ForegroundColor Cyan
