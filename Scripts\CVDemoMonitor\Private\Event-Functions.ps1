# Event-Functions.ps1
# Contains functions related to event processing

function Get-LastRunTime {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath,

        [Parameter(Mandatory = $false)]
        [int]$DefaultLookbackMinutes = 60
    )

    try {
        if (Test-Path $StateFilePath) {
            $lastRunTimeStr = Get-Content -Path $StateFilePath -ErrorAction Stop
            $lastRunTime = [datetime]::Parse($lastRunTimeStr)
            Write-Log "Retrieved last run time from state file: $($lastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
            return $lastRunTime
        }
        else {
            $defaultTime = (Get-Date).AddMinutes(-$DefaultLookbackMinutes)
            Write-Log "State file not found. Using default lookback time: $($defaultTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
            return $defaultTime
        }
    }
    catch {
        Write-Log "Error reading last run time: $($_.Exception.Message). Using default lookback time." -Level 'WARNING'
        return (Get-Date).AddMinutes(-$DefaultLookbackMinutes)
    }
}

function Save-LastRunTime {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath,

        [Parameter(Mandatory = $true)]
        [datetime]$LastRunTime
    )

    try {
        # Ensure directory exists
        $stateDir = Split-Path -Path $StateFilePath -Parent
        if (-not (Test-Path -Path $stateDir -ErrorAction SilentlyContinue)) {
            New-Item -Path $stateDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
            Write-Log "Created state directory: $stateDir" -Level 'INFO'
        }

        # Save the last run time
        $LastRunTime.ToString('o') | Out-File -FilePath $StateFilePath -Force -ErrorAction Stop
        Write-Log "Saved last run time: $($LastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
    }
    catch {
        Write-Log "Error saving last run time: $($_.Exception.Message)" -Level 'ERROR'
        throw $_
    }
}

function Get-CrashEvents {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [datetime]$StartTime,

        [Parameter(Mandatory = $false)]
        [string[]]$AppNames = @("RecordingServer64.exe", "AdminService64.exe", "ManagementServer.exe"),

        [Parameter(Mandatory = $false)]
        [int[]]$EventIDs = @(1000, 1001, 7031, 7034, 2004, 6008)
    )

    try {
        Write-Log "Searching for crash events since $($StartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'

        # Create a filter for the event log query
        $filter = @{
            LogName = 'Application', 'System'
            StartTime = $StartTime
            ID = $EventIDs
        }

        # Get events from the event log
        $events = Get-WinEvent -FilterHashtable $filter -ErrorAction SilentlyContinue

        if ($null -eq $events -or $events.Count -eq 0) {
            Write-Log "No crash events found in the specified time range" -Level 'INFO'
            return $null
        }

        Write-Log "Found $($events.Count) potential crash events" -Level 'INFO'

        # Filter events for our specific applications
        $filteredEvents = @()

        foreach ($eventItem in $events) {
            $eventXml = [xml]$eventItem.ToXml()
            $eventData = $eventXml.Event.EventData.Data

            # Different event IDs have different structures
            $isRelevant = $false

            switch ($eventItem.Id) {
                # Application Error
                1000 {
                    $faultingApp = ($eventData | Where-Object { $_.Name -eq 'Application' }).'#text'
                    if (-not $faultingApp) {
                        # Try to get it from the message if not in the structured data
                        if ($eventItem.Message -match 'Faulting application name: ([^,]+)') {
                            $faultingApp = $matches[1].Trim()
                        }
                    }

                    if ($faultingApp -and ($AppNames -contains $faultingApp)) {
                        $isRelevant = $true
                    }
                }
                # Application Hang
                1001 {
                    $faultingApp = ($eventData | Where-Object { $_.Name -eq 'Application' }).'#text'
                    if (-not $faultingApp) {
                        # Try to get it from the message if not in the structured data
                        if ($eventItem.Message -match 'Hanging application ([^,]+)') {
                            $faultingApp = $matches[1].Trim()
                        }
                    }

                    if ($faultingApp -and ($AppNames -contains $faultingApp)) {
                        $isRelevant = $true
                    }
                }
                # Service Control Manager - Service crashed
                { $_ -in @(7031, 7034) } {
                    $serviceName = $null

                    # Try to extract service name from message
                    if ($eventItem.Message -match 'The ([^(]+) service') {
                        $serviceName = $matches[1].Trim()
                    }

                    # Map service names to executable names
                    $serviceToExe = @{
                        'CompleteView Recording Server' = 'RecordingServer64.exe'
                        'CompleteView Administrative Service' = 'AdminService64.exe'
                        'CompleteView Management Server' = 'ManagementServer.exe'
                    }

                    if ($serviceName -and $serviceToExe.ContainsKey($serviceName) -and
                        ($AppNames -contains $serviceToExe[$serviceName])) {
                        $isRelevant = $true
                    }
                }
                # Low memory condition
                2004 {
                    # This is a system-wide event, so we include it if we're monitoring any services
                    $isRelevant = $true
                }
                # Unexpected shutdown
                6008 {
                    # This is a system-wide event, so we include it if we're monitoring any services
                    $isRelevant = $true
                }
            }

            if ($isRelevant) {
                $filteredEvents += $event
            }
        }

        Write-Log "Found $($filteredEvents.Count) relevant crash events for monitored applications" -Level 'INFO'
        return $filteredEvents
    }
    catch {
        Write-Log "Error getting crash events: $($_.Exception.Message)" -Level 'ERROR'
        return $null
    }
}

function Format-EventDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Diagnostics.Eventing.Reader.EventLogRecord]$LogEvent
    )

    try {
        # Convert the event to XML for easier parsing
        $eventXml = [xml]$LogEvent.ToXml()
        $eventData = $eventXml.Event.EventData.Data

        # Initialize details object
        $details = [PSCustomObject]@{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = $LogEvent.TimeCreated.ToString('yyyy-MM-dd HH:mm:ss')
            Computer = $LogEvent.MachineName
            FaultingApp = $null
            FaultingModule = $null
            ExceptionCode = $null
            ProcessIdDecimal = $null
        }

        # Extract details based on event ID
        switch ($LogEvent.Id) {
            # Application Error
            1000 {
                # Try to get structured data first
                $faultingApp = ($eventData | Where-Object { $_.Name -eq 'Application' }).'#text'
                $faultingModule = ($eventData | Where-Object { $_.Name -eq 'ModuleName' }).'#text'
                $exceptionCode = ($eventData | Where-Object { $_.Name -eq 'ExceptionCode' }).'#text'
                $processId = ($eventData | Where-Object { $_.Name -eq 'ProcessId' }).'#text'

                # If structured data not available, try to parse from message
                if (-not $faultingApp -and $LogEvent.Message -match 'Faulting application name: ([^,]+)') {
                    $faultingApp = $matches[1].Trim()
                }
                if (-not $faultingModule -and $LogEvent.Message -match 'Faulting module name: ([^,]+)') {
                    $faultingModule = $matches[1].Trim()
                }
                if (-not $exceptionCode -and $LogEvent.Message -match 'Exception code: (0x[0-9a-fA-F]+)') {
                    $exceptionCode = $matches[1].Trim()
                }
                if (-not $processId -and $LogEvent.Message -match 'Process ID: (0x[0-9a-fA-F]+)') {
                    $processId = $matches[1].Trim()
                }

                $details.FaultingApp = $faultingApp
                $details.FaultingModule = $faultingModule
                $details.ExceptionCode = $exceptionCode

                # Convert hex process ID to decimal if available
                if ($processId -and $processId -match '0x([0-9a-fA-F]+)') {
                    $details.ProcessIdDecimal = [Convert]::ToInt32($matches[1], 16)
                }
            }
            # Application Hang
            1001 {
                # Similar parsing logic as for 1000
                $faultingApp = ($eventData | Where-Object { $_.Name -eq 'Application' }).'#text'
                $processId = ($eventData | Where-Object { $_.Name -eq 'ProcessId' }).'#text'

                if (-not $faultingApp -and $LogEvent.Message -match 'Hanging application ([^,]+)') {
                    $faultingApp = $matches[1].Trim()
                }
                if (-not $processId -and $LogEvent.Message -match 'Process ID: (0x[0-9a-fA-F]+)') {
                    $processId = $matches[1].Trim()
                }

                $details.FaultingApp = $faultingApp
                $details.FaultingModule = "Application Hang"
                $details.ExceptionCode = "HANG"

                # Convert hex process ID to decimal if available
                if ($processId -and $processId -match '0x([0-9a-fA-F]+)') {
                    $details.ProcessIdDecimal = [Convert]::ToInt32($matches[1], 16)
                }
            }
            # Service Control Manager - Service crashed
            { $_ -in @(7031, 7034) } {
                $serviceName = $null

                # Try to extract service name from message
                if ($LogEvent.Message -match 'The ([^(]+) service') {
                    $serviceName = $matches[1].Trim()
                }

                # Map service names to executable names
                $serviceToExe = @{
                    'CompleteView Recording Server' = 'RecordingServer64.exe'
                    'CompleteView Administrative Service' = 'AdminService64.exe'
                    'CompleteView Management Server' = 'ManagementServer.exe'
                }

                if ($serviceName -and $serviceToExe.ContainsKey($serviceName)) {
                    $details.FaultingApp = $serviceToExe[$serviceName]
                    $details.FaultingModule = "Service Control Manager"
                    $details.ExceptionCode = "SERVICE_CRASH"

                    # Use timestamp for process ID since we don't have the actual PID
                    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
                    $details.ProcessIdDecimal = $timestamp
                }
            }
            # Low memory condition
            2004 {
                $details.FaultingApp = "System"
                $details.FaultingModule = "Memory Resource Notification"
                $details.ExceptionCode = "LOW_MEMORY"

                # Use timestamp for process ID
                $timestamp = Get-Date -Format "yyyyMMddHHmmss"
                $details.ProcessIdDecimal = $timestamp
            }
            # Unexpected shutdown
            6008 {
                $details.FaultingApp = "System"
                $details.FaultingModule = "EventLog"
                $details.ExceptionCode = "UNEXPECTED_SHUTDOWN"

                # Use timestamp for process ID
                $timestamp = Get-Date -Format "yyyyMMddHHmmss"
                $details.ProcessIdDecimal = $timestamp
            }
        }

        return $details
    }
    catch {
        Write-Log "Error formatting event details: $($_.Exception.Message)" -Level 'ERROR'
        return [PSCustomObject]@{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = $LogEvent.TimeCreated.ToString('yyyy-MM-dd HH:mm:ss')
            Computer = $LogEvent.MachineName
            FaultingApp = $null
            FaultingModule = $null
            ExceptionCode = $null
            ProcessIdDecimal = $null
        }
    }
}

function Wait-ForDumpFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMinutes = 5
    )

    try {
        Write-Log "Waiting for dump file to appear at: $DumpPath" -Level 'INFO'

        $startTime = Get-Date
        $timeout = New-TimeSpan -Minutes $TimeoutMinutes

        while ((Get-Date) - $startTime -lt $timeout) {
            if (Test-Path -Path $DumpPath) {
                $waitTime = (Get-Date) - $startTime
                Write-Log "Dump file found after waiting $($waitTime.TotalSeconds.ToString('0.00')) seconds" -Level 'INFO'
                return $true
            }

            # Wait a bit before checking again
            Start-Sleep -Seconds 5
        }

        Write-Log "Dump file did not appear after waiting $TimeoutMinutes minutes" -Level 'WARNING'
        return $false
    }
    catch {
        Write-Log "Error waiting for dump file: $($_.Exception.Message)" -Level 'ERROR'
        return $false
    }
}
