# Start-CVMonitoring.ps1
# Main entry point function for the CVDemoMonitor module

function Start-CVMonitoring {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^https://hooks\.slack\.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$')]
        [string]$SlackWebhookUrl = "*******************************************************************************",

        [Parameter(Mandatory = $false)]
        [string]$LogPath = "",

        [Parameter(Mandatory = $false)]
        [string]$StateFilePath = "",

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10080)] # Max 1 week (10080 minutes)
        [int]$LookbackMinutes = 60,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 60)]
        [int]$DumpWaitTimeoutMinutes = 5,

        [Parameter(Mandatory = $false)]
        [switch]$NoSlack,

        [Parameter(Mandatory = $false)]
        [switch]$TestMode,

        [Parameter(Mandatory = $false)]
        [switch]$MonitorRecordingServer,

        [Parameter(Mandatory = $false)]
        [switch]$MonitorAdminService,

        [Parameter(Mandatory = $false)]
        [switch]$MonitorManagementServer,

        [Parameter(Mandatory = $false)]
        [ValidateSet('ERROR', 'WARNING', 'INFO', 'DEBUG')]
        [string]$LogLevel = 'INFO',

        [Parameter(Mandatory = $false)]
        [string]$ConfigFilePath = "",

        [Parameter(Mandatory = $false)]
        [switch]$UseEnvironmentVariables,

        [Parameter(Mandatory = $false)]
        [switch]$ForceRegistryLookup,

        [Parameter(Mandatory = $false)]
        [string]$InstallBasePath = "",

        [Parameter(Mandatory = $false)]
        [switch]$SkipSqlModuleCheck,

        [Parameter(Mandatory = $false)]
        [switch]$SkipOdbcFallback,

        [Parameter(Mandatory = $false)]
        [switch]$SkipTcpFallback,

        [Parameter(Mandatory = $false)]
        [string]$SqlConnectionString = ""
    )

    try {
        # Record script start time
        $scriptStartTime = Get-Date
        
        # Set global log level
        $script:LogLevel = $LogLevel
        
        # Initialize script
        Write-Log "Starting CompleteView monitoring script" -Level 'INFO'
        Write-Log "Script version: 4.1" -Level 'INFO'
        Write-Log "PowerShell version: $($PSVersionTable.PSVersion)" -Level 'INFO'
        
        # Validate that at least one service is selected for monitoring
        if (-not ($MonitorRecordingServer -or $MonitorAdminService -or $MonitorManagementServer)) {
            Write-Log "No services selected for monitoring. Please specify at least one service using -MonitorRecordingServer, -MonitorAdminService, or -MonitorManagementServer." -Level 'ERROR'
            return
        }
        
        # Initialize environment and get configuration
        $initParams = @{
            ConfigFilePath = $ConfigFilePath
            UseEnvironmentVariables = $UseEnvironmentVariables
            ForceRegistryLookup = $ForceRegistryLookup
            InstallBasePath = $InstallBasePath
            LogPath = $LogPath
            StateFilePath = $StateFilePath
            MonitorRecordingServer = $MonitorRecordingServer
            MonitorAdminService = $MonitorAdminService
            MonitorManagementServer = $MonitorManagementServer
        }
        
        $script:config = Initialize-Environment @initParams
        
        # Update global variables with configuration
        $script:LogPath = $config.LogPath
        $serviceConfigs = $config.ServiceConfigs
        $StateFilePath = $config.StateFilePath
        
        Write-Log "Environment initialized successfully" -Level 'INFO'
        Write-Log "Log path: $script:LogPath" -Level 'INFO'
        Write-Log "State file path: $StateFilePath" -Level 'INFO'
        Write-Log "Base directory: $($config.BaseDirectory)" -Level 'INFO'
        
        # Log which services are being monitored
        $monitoredServices = $serviceConfigs.Keys | ForEach-Object { $serviceConfigs[$_].FriendlyName }
        Write-Log "Monitoring services: $($monitoredServices -join ', ')" -Level 'INFO'
        
        # Process any queued Slack messages from previous runs
        if (-not $NoSlack -and $SlackWebhookUrl) {
            try {
                $queueDir = Join-Path -Path $config.BaseDirectory -ChildPath "SlackQueue"
                $queuePath = Join-Path -Path $queueDir -ChildPath "slack_queue.json"
                
                if (Test-Path $queuePath) {
                    Write-Log "Found queued Slack messages, attempting to send them" -Level 'INFO'
                    
                    $queueContent = Get-Content -Path $queuePath -Raw -ErrorAction Stop
                    if (-not [string]::IsNullOrEmpty($queueContent)) {
                        $queue = $queueContent | ConvertFrom-Json -ErrorAction Stop
                        
                        # Ensure it's an array even if there's only one item
                        if ($queue -isnot [array]) {
                            $queue = @($queue)
                        }
                        
                        $queuedSent = 0
                        $remainingQueue = @()
                        
                        foreach ($message in $queue) {
                            try {
                                Send-SlackMessage -WebhookUrl $message.WebhookUrl -MessageText $message.MessageText
                                $queuedSent++
                            }
                            catch {
                                # If we still can't send it, keep it in the queue
                                $message.RetryCount++
                                $remainingQueue += $message
                                Write-Log "Failed to send queued message (ID: $($message.Id), Retries: $($message.RetryCount))" -Level 'WARNING'
                            }
                        }
                        
                        # Save remaining queue
                        if ($remainingQueue.Count -gt 0) {
                            $remainingQueue | ConvertTo-Json -Depth 5 | Out-File -FilePath $queuePath -Force
                            Write-Log "$($remainingQueue.Count) messages remain in queue" -Level 'INFO'
                        }
                        else {
                            # Queue is empty, delete the file
                            Remove-Item -Path $queuePath -Force -ErrorAction SilentlyContinue
                            Write-Log "Queue is now empty" -Level 'INFO'
                        }
                    }
                    
                    if ($queuedSent -gt 0) {
                        Write-Log "Sent $queuedSent previously queued Slack messages at startup" -Level 'INFO'
                    }
                }
            }
            catch {
                Write-Log "Error processing queued Slack messages at startup: $($_.Exception.Message)" -Level 'WARNING'
            }
        }
        
        # Perform health monitoring checks
        Write-Log "Starting health monitoring checks" -Level 'INFO'
        
        # Check service status
        Test-ServiceStatus -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack
        
        # Check resource usage
        Measure-ResourceUsage -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack
        
        # Check disk space
        Test-DiskSpace -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack
        
        # Check database connection (only for Management Server)
        if ($MonitorManagementServer) {
            Write-Log "Testing database connection with enhanced fallback options" -Level 'INFO'
            
            # Build parameters for database connection test
            $dbParams = @{
                WebhookUrl = $SlackWebhookUrl
                NoSlack = $NoSlack
            }
            
            # Add optional parameters if specified
            if ($SkipSqlModuleCheck) {
                $dbParams.SkipModuleCheck = $true
                Write-Log "SQL Server PowerShell module check will be skipped" -Level 'INFO'
            }
            
            if ($SkipOdbcFallback) {
                $dbParams.SkipOdbcFallback = $true
                Write-Log "ODBC fallback will be skipped" -Level 'INFO'
            }
            
            if ($SkipTcpFallback) {
                $dbParams.SkipTcpFallback = $true
                Write-Log "TCP connectivity fallback will be skipped" -Level 'INFO'
            }
            
            # Use custom connection string if provided
            if (-not [string]::IsNullOrEmpty($SqlConnectionString)) {
                $dbParams.ConnectionString = $SqlConnectionString
                Write-Log "Using custom SQL connection string" -Level 'INFO'
            }
            
            # Use enhanced database connection function with configured options
            Test-DatabaseConnection @dbParams
        }
        
        # Analyze log files
        Search-LogFiles -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack -LookbackMinutes $LookbackMinutes
        
        # Get last run time and crash events
        $lastRunTime = Get-LastRunTime -StateFilePath $StateFilePath -DefaultLookbackMinutes $LookbackMinutes
        Write-Log "Looking for events since $($lastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        
        # If in test mode, we can simulate a crash event for each service
        if ($TestMode) {
            Write-Log "Test mode: Simulating crash events for monitored services" -Level 'INFO'
            
            # Process each service in test mode
            foreach ($serviceName in $serviceConfigs.Keys) {
                $serviceConfig = $serviceConfigs[$serviceName]
                $appName = $serviceConfig.AppName
                $dumpFolder = $serviceConfig.DumpPath
                $friendlyName = $serviceConfig.FriendlyName
                
                Write-Log "Test mode: Simulating crash event for $friendlyName ($appName)" -Level 'INFO'
                
                # Create a simulated event details object
                $simulatedDetails = [PSCustomObject]@{
                    EventID = 1000
                    Source = "Application Error"
                    Time = (Get-Date).AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss")
                    Computer = $env:COMPUTERNAME
                    FaultingApp = $appName
                    FaultingModule = "unknown.dll"
                    ExceptionCode = "0xc0000005"
                    ProcessIdDecimal = 4660  # 0x1234 in decimal
                }
                
                # Process the simulated event
                try {
                    $hostname = $env:COMPUTERNAME
                    $time = $simulatedDetails.Time
                    $faultingModule = $simulatedDetails.FaultingModule
                    $exceptionCode = $simulatedDetails.ExceptionCode
                    
                    # Create dump file name
                    $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $simulatedDetails.ProcessIdDecimal
                    $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile
                    
                    # In test mode, we'll say the dump doesn't exist initially
                    $dumpExistsInitially = $false
                    
                    # Send initial notification if Slack is enabled
                    if (-not $NoSlack) {
                        Write-Log "Sending initial Slack notification for simulated crash of $friendlyName" -Level 'INFO'
                        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                        Write-Log "Sent initial Slack notification for simulated crash at $time" -Level 'INFO'
                    }
                    else {
                        Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
                    }
                    
                    # Simulate waiting for dump file
                    Write-Log "Simulating wait for dump file..." -Level 'INFO'
                    Start-Sleep -Seconds 2
                    
                    # In test mode, we'll say the dump becomes available
                    $dumpAvailable = $true
                    
                    # Send follow-up notification if Slack is enabled
                    if (-not $NoSlack) {
                        Write-Log "Sending follow-up Slack notification for simulated crash" -Level 'INFO'
                        Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false
                        Write-Log "Sent second Slack notification for simulated dump availability at $time" -Level 'INFO'
                    }
                    else {
                        Write-Log "Dump file is now available at: $fullDumpPath (simulated)" -Level 'INFO'
                    }
                }
                catch {
                    Write-Log "Error processing simulated event for ${friendlyName}: $($_.Exception.Message)" -Level 'ERROR'
                }
            }
            
            # Skip the normal event processing
            $events = $null
        }
        else {
            # Get real events
            $appNames = $serviceConfigs.Values | ForEach-Object { $_.AppName }
            $events = Get-CrashEvents -StartTime $lastRunTime -AppNames $appNames
        }
        
        if ($null -eq $events -or $events.Count -eq 0) {
            Write-Log "No events found in the specified time range" -Level 'INFO'
        }
        else {
            Write-Log "Found $($events.Count) event(s) to process" -Level 'INFO'
            
            foreach ($eventItem in $events) {
                try {
                    $details = Format-EventDetails -LogEvent $eventItem
                    
                    # Skip if we couldn't extract the faulting app
                    if (-not $details.FaultingApp) {
                        Write-Log "Skipping event - could not determine faulting application" -Level 'WARNING'
                        continue
                    }
                    
                    # Check if the faulting app matches any of our monitored services
                    $matchedService = $null
                    
                    foreach ($serviceName in $serviceConfigs.Keys) {
                        $serviceConfig = $serviceConfigs[$serviceName]
                        if ($details.FaultingApp -eq $serviceConfig.AppName) {
                            $matchedService = $serviceConfig
                            break
                        }
                    }
                    
                    # If we found a matching service, process the event
                    if ($matchedService) {
                        $appName = $matchedService.AppName
                        $friendlyName = $matchedService.FriendlyName
                        $dumpFolder = $matchedService.DumpPath
                        
                        Write-Log "Processing crash event for $friendlyName ($appName)" -Level 'INFO'
                        
                        $hostname = $env:COMPUTERNAME
                        $time = $details.Time
                        $faultingModule = if ($null -eq $details.FaultingModule) { "Unknown" } else { $details.FaultingModule }
                        $exceptionCode = if ($null -eq $details.ExceptionCode) { "Unknown" } else { $details.ExceptionCode }
                        
                        # Ensure dump directory exists
                        if (-not (Test-Path -Path $dumpFolder -ErrorAction SilentlyContinue)) {
                            try {
                                New-Item -Path $dumpFolder -ItemType Directory -Force -ErrorAction Stop | Out-Null
                                Write-Log "Created dump directory for $friendlyName - $dumpFolder" -Level 'INFO'
                            }
                            catch {
                                Write-Log "Failed to create dump directory for ${friendlyName}: $($_.Exception.Message)" -Level 'ERROR'
                            }
                        }
                        
                        # Ensure we have a valid process ID
                        if (-not $details.ProcessIdDecimal) {
                            Write-Log "Could not determine process ID from event, using timestamp instead" -Level 'WARNING'
                            $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), (Get-Date).ToString("yyyyMMddHHmmss")
                        }
                        else {
                            $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $details.ProcessIdDecimal
                        }
                        
                        $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile
                        
                        # Check if dump file exists
                        $dumpExistsInitially = Test-Path -Path $fullDumpPath
                        
                        # Send initial notification if Slack is enabled
                        if (-not $NoSlack) {
                            try {
                                Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                                Write-Log "Sent initial Slack notification for $friendlyName crash at $time" -Level 'INFO'
                            }
                            catch {
                                Write-Log "Failed to send initial Slack notification for $friendlyName after retries: $_" -Level 'ERROR'
                                # Continue execution - we'll try again with the follow-up notification
                            }
                        }
                        else {
                            Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
                        }
                        
                        # If dump doesn't exist initially, wait for it
                        if (-not $dumpExistsInitially) {
                            $dumpAvailable = Wait-ForDumpFile -DumpPath $fullDumpPath -TimeoutMinutes $DumpWaitTimeoutMinutes
                            
                            # Send follow-up notification if Slack is enabled
                            if (-not $NoSlack) {
                                try {
                                    Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false
                                    
                                    if ($dumpAvailable) {
                                        Write-Log "Sent second Slack notification for $friendlyName dump availability at $time" -Level 'INFO'
                                    }
                                    else {
                                        Write-Log "Sent notification that $friendlyName dump did not appear after timeout at $time" -Level 'WARNING'
                                    }
                                }
                                catch {
                                    Write-Log "Failed to send follow-up Slack notification for $friendlyName after retries: $_" -Level 'ERROR'
                                }
                            }
                            else {
                                if ($dumpAvailable) {
                                    Write-Log "Dump file for $friendlyName is now available at: $fullDumpPath" -Level 'INFO'
                                }
                                else {
                                    Write-Log "Dump file for $friendlyName did not appear after waiting $DumpWaitTimeoutMinutes minutes" -Level 'WARNING'
                                }
                            }
                        }
                    }
                    else {
                        # This crash is not for one of our monitored services
                        Write-Log "Skipping event - faulting application '$($details.FaultingApp)' is not in the monitored services list" -Level 'DEBUG'
                    }
                }
                catch {
                    Write-Log "Error processing event: $_" -Level 'ERROR'
                    # Continue with next event even if this one fails
                    continue
                }
            }
        }
        
        # Save the last run time (unless in test mode)
        if (-not $TestMode) {
            try {
                Save-LastRunTime -StateFilePath $StateFilePath -LastRunTime (Get-Date)
            }
            catch {
                Write-Log "Failed to save last run time: $_" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Test mode: Skipping save of last run time" -Level 'INFO'
        }
        
        # Calculate and log execution time
        $executionTime = (Get-Date) - $scriptStartTime
        Write-Log "Crash monitoring completed in $($executionTime.TotalSeconds.ToString('0.00')) seconds" -Level 'INFO'
    }
    catch {
        # Global error handler
        Write-Log "Critical error in script execution: $_" -Level 'ERROR'
        
        # Try to send an alert about the script failure if possible
        if (-not $NoSlack -and -not [string]::IsNullOrEmpty($SlackWebhookUrl)) {
            try {
                $errorText = ":x: *CVDemoMonitor Script Error*
The monitoring script encountered a critical error on $env:COMPUTERNAME at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
                
                # Use our enhanced Slack message function with queuing capability
                Send-SlackMessage -WebhookUrl $SlackWebhookUrl -MessageText $errorText -QueueOnFailure -ErrorAction SilentlyContinue
                Write-Host "Sent error notification to Slack" -ForegroundColor Yellow
            }
            catch {
                # Try to queue the message directly if Send-SlackMessage fails completely
                try {
                    Add-SlackMessageToQueue -WebhookUrl $SlackWebhookUrl -MessageText $errorText
                    Write-Host "Queued error notification for later delivery" -ForegroundColor Yellow
                }
                catch {
                    Write-Host "Could not send or queue error notification to Slack: $_" -ForegroundColor Red
                }
            }
        }
        
        # Re-throw the error
        throw $_
    }
    finally {
        # This block always executes, even if there are errors
        $executionTime = (Get-Date) - $scriptStartTime
        Write-Host "Script execution finished in $($executionTime.TotalSeconds.ToString('0.00')) seconds"
    }
}
