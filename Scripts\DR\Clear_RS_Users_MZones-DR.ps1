﻿# Set target backup folder path
$backupFolderPath = [System.IO.Path]::Co<PERSON><PERSON>([Environment]::GetFolderPath("Desktop"), "JSON Backups")

# Make the folder
function New-BackupFolder {
    try {
        # If the folder doesn't exist, let's create it
        if (-not (Test-Path -Path $backupFolderPath)) {
            New-Item -ItemType Directory -Path $backupFolderPath | Out-Null
        }
    } catch {
        # Couldn't make the folder
        Write-Error "Failed to create the backup folder: $_"
        exit
    }
}

# Validate IP
function Test-IPAddress($ipAddress) {
    # Regex validation
    $ipPattern = '^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    return $ipAddress -match $ipPattern
}

function Get-ServerData($apiUrl, $headers) {
    try {
        Write-Host "Fetching data from: $apiUrl"
        $response = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headers -Verbose
        return $response
    } catch {
        Write-Error "Error encountered in Get-ServerData: $_"
        if ($_.Exception -and $_.Exception.Response) {
            $responseStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($responseStream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        }
        return $null
    }
}

# Function to update both user and camera settings
function Update-Settings($apiUrl, $headers) {
    try {
        # Fetch current configuration
        $originalConfig = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headers

        # Update User Settings
        $initialUserCount = $originalConfig.'server-config'.users.Count
        $adminUser = $originalConfig.'server-config'.users | Where-Object { $_.name -eq "admin" }
        $originalConfig.'server-config'.users = @($adminUser)

        # Update Camera Settings
        $initialEnabledCount = 0
        foreach ($camera in $originalConfig.'server-config'.cameras) {
            foreach ($zone in $camera.motionSettings.motionzones) {
                if ($zone.enabled -eq $true) {
                    $initialEnabledCount++
                    $zone.enabled = $false
                }
            }
        }

        # Apply the updates
        $jsonData = $originalConfig | ConvertTo-Json -Depth 10
        Invoke-RestMethod -Uri $apiUrl -Method Put -Headers $headers -Body $jsonData -ContentType "application/json"

        # Wait for changes to take effect
        Start-Sleep -Seconds 1

        # Fetch updated configuration for verification
        $updatedConfig = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headers

        # Count updated users and camera zones
        $finalUserCount = $updatedConfig.'server-config'.users.Count
        $finalEnabledCount = ($updatedConfig.'server-config'.cameras | ForEach-Object { $_.motionSettings.motionzones } | Where-Object { $_.enabled -eq $true }).Count

        # Construct and return results
        return @{
            InitialUserCount = $initialUserCount
            FinalUserCount = $finalUserCount
            InitialEnabledCount = $initialEnabledCount
            FinalEnabledCount = $finalEnabledCount
            UserUpdateMessage = "Users updated. Initial: $initialUserCount, Final: $finalUserCount"
            CameraUpdateMessage = if ($finalEnabledCount -eq 0) {"All motion zones disabled successfully."} else {"Some motion zones are still enabled."}
        }
    } catch {
        Write-Error "Error encountered during API call: $($_.Exception.Message)"
        return $null
    }
}

# Define default admin username
$username = "admin"

# Check if it's the first run
$firstRun = $true

# The main stuff starts here...
New-BackupFolder
$storedPassword = $null

# Loop through the servers
do {
    # Display message on first run
    if ($firstRun) {
        Write-Host "This script will remove all users (except admin) and disable camera motion zones."
        Write-Host "To begin, enter a password that will work for all IPs in this session."
        Write-Host "To enter a new password, kill the script and start over."
        Write-Host "Backups will be placed on the desktop in a folder called JSON Backups."
        Write-Host
        # Set $firstRun to $false so the message doesn't repeat
        $firstRun = $false
    }

    if (-not $storedPassword) {
        $password = Read-Host -Prompt "Enter the password" -AsSecureString
        $passwordBSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($password)
        $storedPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($passwordBSTR)
    }

    $serverIp = Read-Host -Prompt "Enter the IP"
    if (-not (Test-IPAddress -ipAddress $serverIp)) {
        Write-Error "Please enter a valid IP (e.g., ***********)."
        continue
    }

    $backupFilePath = [System.IO.Path]::Combine($backupFolderPath, "$serverIp.json")
    if (Test-Path -Path $backupFilePath) {
        $userChoice = Read-Host "Backup for this IP already exists. Enter Y to overwrite or N to skip"
        if ($userChoice -ne 'Y') { continue }
    }

    # Construct the base URL with the provided IP
    $apiUrl = "http://" + $serverIp + ":4502/v2.0/config"
    $encodedCreds = [System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes("${username}:$storedPassword"))
    $headers = @{ Authorization = "Basic $encodedCreds" }

    # Get current configuration
    $currentConfig = Get-ServerData -apiUrl $apiUrl -headers $headers
    if ($null -eq $currentConfig) { continue }

    # Backup current configuration
    $currentConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $backupFilePath

    # Update settings
    $updateResults = Update-Settings -apiUrl $apiUrl -headers $headers
    if ($null -ne $updateResults) {
        Write-Host "Update results: "
        Write-Host "Users - Initial: $($updateResults.InitialUserCount), Final: $($updateResults.FinalUserCount)"
        Write-Host "Camera Zones - Initial: $($updateResults.InitialEnabledCount), Final: $($updateResults.FinalEnabledCount)"
    } else {
        Write-Host "No update results to display."
    }
    $continue = Read-Host "Press Enter to process another server or type 'no' to exit"
} while ($continue -ne "no")

# Finally done!
Write-Host "Processing complete."