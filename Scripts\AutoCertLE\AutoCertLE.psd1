@{
    # Script module or binary module file associated with this manifest.
    RootModule = 'AutoCertLE.psm1'

    # Version number of this module.
    ModuleVersion = '1.0.0'

    # ID used to uniquely identify this module
    GUID = 'f8dbc215-7e9d-4ac0-a7ad-389a407f9e2a'

    # Author of this module
    Author = 'Salient Security'

    # Company or vendor of this module
    CompanyName = 'Salient Security'

    # Copyright statement for this module
    Copyright = '(c) 2023 Salient Security. All rights reserved.'

    # Description of the functionality provided by this module
    Description = 'Automates the management of Let''s Encrypt TLS certificates using Posh-ACME for CompleteView servers.'

    # Minimum version of the PowerShell engine required by this module
    PowerShellVersion = '5.1'

    # Modules that must be imported into the global environment prior to importing this module
    RequiredModules = @('Posh-ACME')

    # Script files (.ps1) that are run in the caller's environment prior to importing this module
    ScriptsToProcess = @('Start-AutoCertLE.ps1')

    # Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
    FunctionsToExport = @(
        'Register-Certificate',
        'Install-Certificate',
        'Set-AutomaticRenewal',
        'Get-ExistingCertificates',
        'Revoke-Certificate',
        'Remove-Certificate',
        'Set-ACMEServer',
        'Show-Menu',
        'Show-AdvancedOptions',
        'Show-SettingsMenu',
        'Show-SecurityOptions',
        'Show-MonitoringDashboard',
        'Invoke-AutoCertRenewal',
        'Export-Logs',
        'Backup-ACMEAccountKey',
        'Restore-ACMEAccountKey',
        'Test-CertificateKeySecurity',
        'Set-EmailNotificationSettings',
        'Set-RenewalHooks',
        'Show-CertificateMonitoringReport',
        'Get-CertificateMonitoringReport',
        'Set-CertificateMonitoringSchedule',
        # Salient CompleteView specific functions
        'Install-SalientCertificate',
        'Send-SalientCertificate',
        'Start-AutoCertLE'
    )

    # Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
    CmdletsToExport = @()

    # Variables to export from this module
    VariablesToExport = '*'

    # Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
    AliasesToExport = @()

    # Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
    PrivateData = @{
        PSData = @{
            # Tags applied to this module. These help with module discovery in online galleries.
            Tags = @('Certificate', 'LetsEncrypt', 'ACME', 'TLS', 'SSL', 'CompleteView')

            # A URL to the license for this module.
            # LicenseUri = ''

            # A URL to the main website for this project.
            # ProjectUri = ''

            # A URL to an icon representing this module.
            # IconUri = ''

            # ReleaseNotes of this module
            ReleaseNotes = 'Initial release of the AutoCertLE module.'
        }
    }
}
