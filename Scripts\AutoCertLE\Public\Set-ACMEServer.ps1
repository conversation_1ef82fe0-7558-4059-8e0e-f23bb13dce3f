<#
.SYNOPSIS
    Sets the ACME server to use for certificate operations.

.DESCRIPTION
    Allows switching between Let's Encrypt production and staging servers,
    or other ACME-compatible servers.

.EXAMPLE
    Set-ACMEServer
#>
function Set-ACMEServer {
    [CmdletBinding()]
    param ()

    # Get current server
    $currentServer = Get-PAServer
    Write-Host "`nCurrent ACME Server: $($currentServer.Name) - $($currentServer.BaseUri)" -ForegroundColor Cyan

    # Display server options
    Write-Host "`nAvailable ACME Servers:" -ForegroundColor Cyan
    Write-Host "1) Let's Encrypt Production (LE_PROD)"
    Write-Host "2) Let's Encrypt Staging (LE_STAGE)"
    Write-Host "3) Custom ACME Server"
    Write-Host "0) Back"

    $choice = Get-ValidatedInput -Prompt "`nSelect an ACME server (0-3)" -ValidOptions 1,2,3
    switch ($choice) {
        0 { return }
        1 {
            # Set to Let's Encrypt Production
            Set-PAServer LE_PROD
            Write-Host "`nACME server set to Let's Encrypt Production." -ForegroundColor Green
            Write-Log "ACME server set to Let's Encrypt Production." -Level 'Info'
            
            # Clear the certificate cache
            Clear-CertificateCache
        }
        2 {
            # Set to Let's Encrypt Staging
            Set-PAServer LE_STAGE
            Write-Host "`nACME server set to Let's Encrypt Staging." -ForegroundColor Green
            Write-Log "ACME server set to Let's Encrypt Staging." -Level 'Info'
            
            # Clear the certificate cache
            Clear-CertificateCache
            
            # Warning about staging certificates
            Write-Host "`nWARNING: Staging certificates are not trusted by browsers and should only be used for testing." -ForegroundColor Yellow
        }
        3 {
            # Set to custom ACME server
            $serverUri = Read-Host "`nEnter the ACME server URI (e.g., https://acme.example.com/directory)"
            if ([string]::IsNullOrWhiteSpace($serverUri)) {
                Write-Warning "Invalid server URI. Operation cancelled."
                return
            }
            
            $serverName = Read-Host "Enter a name for this server"
            if ([string]::IsNullOrWhiteSpace($serverName)) {
                $serverName = "Custom_ACME_Server"
            }
            
            try {
                Set-PAServer $serverUri -Name $serverName
                Write-Host "`nACME server set to $serverName ($serverUri)." -ForegroundColor Green
                Write-Log "ACME server set to $serverName ($serverUri)." -Level 'Info'
                
                # Clear the certificate cache
                Clear-CertificateCache
            } catch {
                Write-Error "Failed to set ACME server: $($_)"
                Write-Log "Failed to set ACME server to ${serverName}: $($_)" -Level 'Error'
            }
        }
    }
    
    Read-Host "`nPress Enter to return to the advanced options"
}
