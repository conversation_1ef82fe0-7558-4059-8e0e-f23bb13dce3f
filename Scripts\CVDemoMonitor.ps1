<#
.SYNOPSIS
    Comprehensive monitoring solution for CompleteView services with Slack alerting capabilities and resilient operation.

.DESCRIPTION
    CVDemoMonitor is a robust monitoring solution for Salient CompleteView services that provides:

    1. Crash Detection and Alerting:
       - Monitors Windows Event Log for crashes (Event IDs 1000, 1001) and service failures (7031, 7034)
       - Monitors system-level events like low memory (2004) and unexpected shutdowns (6008)
       - Tracks the following CompleteView services:
         * RecordingServer64.exe (CompleteView Recording Server)
         * AdminService64.exe (CompleteView Administrative Service)
         * ManagementServer.exe (CompleteView Management Server)
       - Sends immediate Slack alerts when issues are detected
       - Tracks crash dump files and sends follow-up notifications when they become available

    2. Proactive System Monitoring:
       - Monitors service status (running/stopped)
       - Tracks resource usage (CPU, memory) with configurable thresholds
       - Checks disk space on critical paths
       - Verifies database connectivity for Management Server with multiple fallback methods
       - Analyzes log files for critical errors

    3. Resilient Operation:
       - Maintains state between runs to prevent duplicate notifications
       - Implements log rotation to manage disk usage
       - Handles network interruptions with retry logic and message queuing
       - Provides comprehensive error handling and fallback mechanisms
       - Supports test mode for validating configuration

    4. Enhanced Slack Notification System:
       - Performs network reachability pre-checks before attempting to send messages
       - Queues messages to disk when Slack is unreachable for later delivery
       - Automatically retries sending queued messages when connectivity is restored
       - Implements graceful degradation after repeated failures

    5. Robust SQL Connectivity:
       - Multiple database connection methods with graceful fallbacks:
         * SqlServer PowerShell module (if available)
         * .NET SqlClient (built-in)
         * ODBC connection (additional fallback)
         * Basic TCP connectivity test (minimal fallback)
       - Detailed error reporting for each connection method
       - Configurable options to control fallback behavior

    6. Flexible Installation Detection:
       - Automatically detects CompleteView installation paths
       - Supports configuration via config files (JSON, XML, INI)
       - Supports environment variable overrides
       - Registry-based detection with fallback options
       - Manual path specification for non-standard installations

.PARAMETER SlackWebhookUrl
    The Slack webhook URL for sending notifications.

.PARAMETER LogPath
    Custom path for the script's log file. If not specified, logs are stored in the CompleteView crash dumps directory.

.PARAMETER StateFilePath
    Custom path for the script's state file. If not specified, state is stored in the CompleteView crash dumps directory.

.PARAMETER LookbackMinutes
    Number of minutes to look back for events when the script runs. Default is 60 minutes.

.PARAMETER DumpWaitTimeoutMinutes
    Maximum time to wait for crash dump files to appear. Default is 5 minutes.

.PARAMETER NoSlack
    If specified, disables Slack notifications.

.PARAMETER TestMode
    Runs the script in test mode, simulating crashes without saving state.

.PARAMETER MonitorRecordingServer
    Enables monitoring of the CompleteView Recording Server.

.PARAMETER MonitorAdminService
    Enables monitoring of the CompleteView Administrative Service.

.PARAMETER MonitorManagementServer
    Enables monitoring of the CompleteView Management Server.

.PARAMETER LogLevel
    Sets the logging verbosity. Valid values: ERROR, WARNING, INFO, DEBUG. Default is INFO.

.PARAMETER ConfigFilePath
    Path to a configuration file for customizing script behavior.

.PARAMETER UseEnvironmentVariables
    Enables reading configuration from environment variables.

.PARAMETER ForceRegistryLookup
    Forces registry lookup for CompleteView installation paths.

.PARAMETER InstallBasePath
    Explicitly sets the base installation path for CompleteView.

.PARAMETER SkipSqlModuleCheck
    Skips checking for the SqlServer PowerShell module when testing database connectivity.

.PARAMETER SkipOdbcFallback
    Skips the ODBC fallback method when testing database connectivity.

.PARAMETER SkipTcpFallback
    Skips the TCP connectivity fallback when testing database connectivity.

.PARAMETER SqlConnectionString
    Provides a custom SQL connection string for database connectivity testing.

.EXAMPLE
    .\CVDemoMonitor.ps1 -MonitorRecordingServer -MonitorManagementServer
    Monitors the Recording Server and Management Server with default settings.

.EXAMPLE
    .\CVDemoMonitor.ps1 -MonitorRecordingServer -MonitorAdminService -MonitorManagementServer -LogLevel DEBUG
    Monitors all services with detailed logging.

.EXAMPLE
    .\CVDemoMonitor.ps1 -TestMode -MonitorRecordingServer
    Tests the script's functionality for the Recording Server without saving state.

.EXAMPLE
    .\CVDemoMonitor.ps1 -MonitorManagementServer -SkipOdbcFallback -SqlConnectionString "Server=myserver;Database=mydb;Integrated Security=True"
    Monitors the Management Server with a custom SQL connection string and skips ODBC fallback.

.NOTES
    Author: <EMAIL>
    Version: 4.1
    Updated: 2023-07-25

    Requirements:
    - Windows PowerShell 5.1 or later
    - Optional: SqlServer PowerShell module for enhanced database connectivity
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidatePattern('^https://hooks\.slack\.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$')]
    [string]$SlackWebhookUrl = "*******************************************************************************",

    # Original webhook URL (commented out for testing)
    # [string]$SlackWebhookUrl = "*******************************************************************************",

    [Parameter(Mandatory = $false)]
    [string]$LogPath = "",

    [Parameter(Mandatory = $false)]
    [string]$StateFilePath = "",

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 10080)] # Max 1 week (10080 minutes)
    [int]$LookbackMinutes = 60,

    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 60)]
    [int]$DumpWaitTimeoutMinutes = 5,

    [Parameter(Mandatory = $false)]
    [switch]$NoSlack,

    [Parameter(Mandatory = $false)]
    [switch]$TestMode,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorRecordingServer,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorAdminService,

    [Parameter(Mandatory = $false)]
    [switch]$MonitorManagementServer,

    [Parameter(Mandatory = $false)]
    [ValidateSet('ERROR', 'WARNING', 'INFO', 'DEBUG')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [string]$ConfigFilePath = "",

    [Parameter(Mandatory = $false)]
    [switch]$UseEnvironmentVariables,

    [Parameter(Mandatory = $false)]
    [switch]$ForceRegistryLookup,

    [Parameter(Mandatory = $false)]
    [string]$InstallBasePath = "",

    [Parameter(Mandatory = $false)]
    [switch]$SkipSqlModuleCheck,

    [Parameter(Mandatory = $false)]
    [switch]$SkipOdbcFallback,

    [Parameter(Mandatory = $false)]
    [switch]$SkipTcpFallback,

    [Parameter(Mandatory = $false)]
    [string]$SqlConnectionString = ""
)

#region Functions

function Find-InstallPath {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConfigFilePath = "",

        [Parameter(Mandatory = $false)]
        [switch]$UseEnvironmentVariables,

        [Parameter(Mandatory = $false)]
        [switch]$ForceRegistryLookup
    )

    try {
        # Initialize paths with defaults
        $paths = @{
            ProgramFiles = "C:\Program Files\Salient Security Platform"
            ProgramData = "C:\ProgramData\Salient Security Platform"
            RecordingServer = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            AdminService = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            ManagementServer = "C:\Program Files\Salient Security Platform\CompleteView\Management Server"
            LogsFolder = "C:\ProgramData\Salient Security Platform\Logs"
            CrashDumps = "C:\ProgramData\Salient Security Platform\CrashDumps"
        }

        # Step 1: Check for config file overrides
        if (-not [string]::IsNullOrEmpty($ConfigFilePath) -and (Test-Path $ConfigFilePath)) {
            try {
                Write-Log "Reading path configuration from $ConfigFilePath" -Level 'INFO'
                $configFileContent = Get-Content -Path $ConfigFilePath -Raw -ErrorAction Stop

                # Determine file type by extension
                $extension = [System.IO.Path]::GetExtension($ConfigFilePath).ToLower()

                switch ($extension) {
                    ".json" {
                        $config = $configFileContent | ConvertFrom-Json -ErrorAction Stop

                        # Update paths from JSON
                        if ($config.Paths) {
                            foreach ($key in $config.Paths.PSObject.Properties.Name) {
                                if ($paths.ContainsKey($key)) {
                                    $paths[$key] = $config.Paths.$key
                                    Write-Log "Config override for ${key}: $($paths[$key])" -Level 'DEBUG'
                                }
                            }
                        }
                    }
                    ".xml" {
                        [xml]$config = $configFileContent

                        # Update paths from XML
                        if ($config.Configuration.Paths) {
                            foreach ($path in $config.Configuration.Paths.Path) {
                                if ($paths.ContainsKey($path.Name)) {
                                    $paths[$path.Name] = $path.Value
                                    Write-Log "Config override for $($path.Name): $($paths[$path.Name])" -Level 'DEBUG'
                                }
                            }
                        }
                    }
                    ".ini" {
                        # Simple INI parser
                        $inPaths = $false
                        foreach ($line in $configFileContent -split "`r`n|`n") {
                            $line = $line.Trim()

                            # Skip comments and empty lines
                            if ($line -match "^[;#]" -or [string]::IsNullOrWhiteSpace($line)) {
                                continue
                            }

                            # Check for section
                            if ($line -match "^\[(.+)\]$") {
                                $section = $Matches[1].Trim()
                                $inPaths = ($section -eq "Paths")
                                continue
                            }

                            # Process key-value pairs in Paths section
                            if ($inPaths -and $line -match "^([^=]+)=(.*)$") {
                                $key = $Matches[1].Trim()
                                $value = $Matches[2].Trim()

                                if ($paths.ContainsKey($key)) {
                                    $paths[$key] = $value
                                    Write-Log "Config override for ${key}: $($paths[$key])" -Level 'DEBUG'
                                }
                            }
                        }
                    }
                    default {
                        Write-Log "Unsupported config file format: $extension" -Level 'WARNING'
                    }
                }
            }
            catch {
                Write-Log "Error reading config file: $_" -Level 'ERROR'
                # Continue with defaults or other methods
            }
        }

        # Step 2: Check for environment variable overrides
        if ($UseEnvironmentVariables) {
            Write-Log "Checking environment variables for path overrides" -Level 'DEBUG'

            $envVarPrefix = "COMPLETEVIEW_"

            $envMappings = @{
                "PROGRAM_FILES" = "ProgramFiles"
                "PROGRAM_DATA" = "ProgramData"
                "RECORDING_SERVER" = "RecordingServer"
                "ADMIN_SERVICE" = "AdminService"
                "MANAGEMENT_SERVER" = "ManagementServer"
                "LOGS_FOLDER" = "LogsFolder"
                "CRASH_DUMPS" = "CrashDumps"
            }

            foreach ($envKey in $envMappings.Keys) {
                $fullEnvVar = $envVarPrefix + $envKey
                $value = [Environment]::GetEnvironmentVariable($fullEnvVar)

                if (-not [string]::IsNullOrEmpty($value)) {
                    $pathKey = $envMappings[$envKey]
                    $paths[$pathKey] = $value
                    Write-Log "Environment variable override for ${pathKey}: $($paths[$pathKey])" -Level 'DEBUG'
                }
            }
        }

        # Step 3: Registry lookup for installation paths
        if ($ForceRegistryLookup -or (-not (Test-Path $paths.ProgramFiles)) -or (-not (Test-Path $paths.ProgramData))) {
            Write-Log "Performing registry lookup for CompleteView installation paths" -Level 'INFO'

            # Common registry paths for installed software
            $registryPaths = @(
                "HKLM:\SOFTWARE\Salient\CompleteView",
                "HKLM:\SOFTWARE\WOW6432Node\Salient\CompleteView",
                "HKLM:\SOFTWARE\Salient Systems\CompleteView",
                "HKLM:\SOFTWARE\WOW6432Node\Salient Systems\CompleteView"
            )

            $installPathFound = $false

            foreach ($regPath in $registryPaths) {
                if (Test-Path $regPath) {
                    try {
                        $installDir = Get-ItemProperty -Path $regPath -Name "InstallDir" -ErrorAction SilentlyContinue

                        if ($installDir -and $installDir.InstallDir) {
                            $basePath = $installDir.InstallDir

                            # Verify the path exists
                            if (Test-Path $basePath) {
                                Write-Log "Found CompleteView installation at: $basePath" -Level 'INFO'

                                # Update program files path
                                $paths.ProgramFiles = Split-Path -Path $basePath -Parent

                                # Update component paths based on the discovered installation
                                $paths.RecordingServer = Join-Path -Path $basePath -ChildPath "Recording Server"
                                $paths.ManagementServer = Join-Path -Path $basePath -ChildPath "Management Server"

                                # AdminService is typically in the Recording Server directory
                                $paths.AdminService = $paths.RecordingServer

                                $installPathFound = $true
                                break
                            }
                        }
                    }
                    catch {
                        Write-Log "Error reading registry key ${regPath}: $($_.Exception.Message)" -Level 'WARNING'
                    }
                }
            }

            # If we found the program files path but not program data, try to infer it
            if ($installPathFound) {
                # Program data is typically under C:\ProgramData with a similar structure
                $inferredProgramData = "C:\ProgramData\Salient Security Platform"

                if (Test-Path $inferredProgramData) {
                    $paths.ProgramData = $inferredProgramData
                    $paths.LogsFolder = Join-Path -Path $inferredProgramData -ChildPath "Logs"
                    $paths.CrashDumps = Join-Path -Path $inferredProgramData -ChildPath "CrashDumps"
                }
            }
        }

        # Step 4: Verify paths and search for alternatives if needed
        foreach ($key in @("ProgramFiles", "ProgramData")) {
            if (-not (Test-Path $paths[$key])) {
                Write-Log "Path not found: $($paths[$key])" -Level 'WARNING'

                # Search for alternative paths
                $searchPaths = @()

                if ($key -eq "ProgramFiles") {
                    $searchPaths = @(
                        "C:\Program Files\Salient Security Platform",
                        "C:\Program Files\Salient Systems",
                        "C:\Program Files\CompleteView",
                        "C:\Program Files (x86)\Salient Security Platform",
                        "C:\Program Files (x86)\Salient Systems",
                        "C:\Program Files (x86)\CompleteView"
                    )
                }
                elseif ($key -eq "ProgramData") {
                    $searchPaths = @(
                        "C:\ProgramData\Salient Security Platform",
                        "C:\ProgramData\Salient Systems",
                        "C:\ProgramData\CompleteView"
                    )
                }

                foreach ($searchPath in $searchPaths) {
                    if (Test-Path $searchPath) {
                        Write-Log "Found alternative path for ${key}: $searchPath" -Level 'INFO'
                        $paths[$key] = $searchPath
                        break
                    }
                }
            }
        }

        # Step 5: Ensure all derived paths are updated based on the base paths
        if (Test-Path $paths.ProgramFiles) {
            $cvBasePath = Join-Path -Path $paths.ProgramFiles -ChildPath "CompleteView"

            if (Test-Path $cvBasePath) {
                # Update component paths if they don't exist
                $rsPath = Join-Path -Path $cvBasePath -ChildPath "Recording Server"
                if (Test-Path $rsPath) {
                    $paths.RecordingServer = $rsPath
                    $paths.AdminService = $rsPath  # AdminService is in the same directory
                }

                $msPath = Join-Path -Path $cvBasePath -ChildPath "Management Server"
                if (Test-Path $msPath) {
                    $paths.ManagementServer = $msPath
                }
            }
        }

        if (Test-Path $paths.ProgramData) {
            # Update logs and crash dumps paths if they don't exist
            $logsPath = Join-Path -Path $paths.ProgramData -ChildPath "Logs"
            if (Test-Path $logsPath) {
                $paths.LogsFolder = $logsPath
            }

            $dumpsPath = Join-Path -Path $paths.ProgramData -ChildPath "CrashDumps"
            if (-not (Test-Path $dumpsPath)) {
                # Try to create the dumps directory if it doesn't exist
                try {
                    New-Item -Path $dumpsPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created crash dumps directory: $dumpsPath" -Level 'INFO'
                }
                catch {
                    Write-Log "Failed to create crash dumps directory: $_" -Level 'WARNING'
                    # Use a fallback location
                    $dumpsPath = Join-Path -Path $env:TEMP -ChildPath "CVCrashDumps"
                    try {
                        if (-not (Test-Path $dumpsPath)) {
                            New-Item -Path $dumpsPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
                        }
                        Write-Log "Using fallback crash dumps directory: $dumpsPath" -Level 'WARNING'
                    }
                    catch {
                        Write-Log "Failed to create fallback crash dumps directory: $_" -Level 'ERROR'
                        $dumpsPath = $env:TEMP
                    }
                }
            }
            $paths.CrashDumps = $dumpsPath
        }

        # Step 6: Final validation and logging
        $validPaths = $true
        foreach ($key in $paths.Keys) {
            if (-not [string]::IsNullOrEmpty($paths[$key]) -and -not (Test-Path $paths[$key])) {
                Write-Log "Warning: Path for $key does not exist: $($paths[$key])" -Level 'WARNING'
                $validPaths = $false
            }
        }

        if (-not $validPaths) {
            Write-Log "Some paths could not be validated. The script may not function correctly." -Level 'WARNING'
        }

        # Return the resolved paths
        return $paths
    }
    catch {
        Write-Log "Error in Find-InstallPath: $_" -Level 'ERROR'

        # Return default paths as fallback
        return @{
            ProgramFiles = "C:\Program Files\Salient Security Platform"
            ProgramData = "C:\ProgramData\Salient Security Platform"
            RecordingServer = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            AdminService = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            ManagementServer = "C:\Program Files\Salient Security Platform\CompleteView\Management Server"
            LogsFolder = "C:\ProgramData\Salient Security Platform\Logs"
            CrashDumps = "C:\ProgramData\Salient Security Platform\CrashDumps"
        }
    }
}

function Test-ServiceStatus {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking service status for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        # Map the executable name to the actual Windows service name
        $windowsServiceName = switch ($config.AppName) {
            "RecordingServer64.exe" { "CVRecordingServer" }
            "AdminService64.exe" { "CVAdminService" }
            "ManagementServer.exe" { "CVManagementServer" }
            default { $null }
        }

        if ($windowsServiceName) {
            try {
                $service = Get-Service -Name $windowsServiceName -ErrorAction Stop

                if ($service.Status -ne 'Running') {
                    Write-Log "$friendlyName service is not running (Status: $($service.Status))" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Service Not Running*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Status:* $($service.Status)
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent service status notification for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send service status notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
                else {
                    Write-Log "$friendlyName service is running" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error checking service status for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Could not determine Windows service name for $friendlyName" -Level 'WARNING'
        }
    }
}

function Measure-ResourceUsage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [int]$MemoryThresholdMB = 2048,  # 2GB

        [Parameter(Mandatory = $false)]
        [int]$CpuThresholdPercent = 80
    )

    Write-Log "Checking resource usage for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $appName = $config.AppName
        $friendlyName = $config.FriendlyName
        $processName = $appName -replace '\.exe$', ''

        try {
            $process = Get-Process -Name $processName -ErrorAction SilentlyContinue

            if ($process) {
                # Check memory usage
                $memoryUsageMB = [Math]::Round($process.WorkingSet64 / 1MB, 2)
                Write-Log "$friendlyName memory usage: $memoryUsageMB MB" -Level 'INFO'

                # Check CPU usage (requires multiple samples)
                $cpuSamples = @()
                for ($i = 0; $i -lt 3; $i++) {
                    $startCPU = $process.TotalProcessorTime
                    $startTime = Get-Date
                    Start-Sleep -Seconds 2
                    $process.Refresh()
                    $endCPU = $process.TotalProcessorTime
                    $endTime = Get-Date
                    $cpuUsage = [Math]::Round(($endCPU - $startCPU).TotalSeconds / ($endTime - $startTime).TotalSeconds * 100 / [Environment]::ProcessorCount, 2)
                    $cpuSamples += $cpuUsage
                }

                $avgCpuUsage = ($cpuSamples | Measure-Object -Average).Average
                Write-Log "$friendlyName CPU usage: $avgCpuUsage%" -Level 'INFO'

                # Check if thresholds are exceeded
                $memoryExceeded = $memoryUsageMB -gt $MemoryThresholdMB
                $cpuExceeded = $avgCpuUsage -gt $CpuThresholdPercent

                if ($memoryExceeded -or $cpuExceeded) {
                    $alertMessage = "$friendlyName resource usage alert:"
                    if ($memoryExceeded) { $alertMessage += " Memory: $memoryUsageMB MB (threshold: $MemoryThresholdMB MB)" }
                    if ($cpuExceeded) { $alertMessage += " CPU: $avgCpuUsage% (threshold: $CpuThresholdPercent%)" }

                    Write-Log $alertMessage -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *High Resource Usage*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
*Memory:* $memoryUsageMB MB (threshold: $MemoryThresholdMB MB)
*CPU:* $avgCpuUsage% (threshold: $CpuThresholdPercent%)"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent resource usage alert for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send resource usage alert for ${friendlyName}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
            }
            else {
                Write-Log "Process $processName not found - service may not be running" -Level 'WARNING'
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Error checking resource usage for ${friendlyName}: $errorMsg" -Level 'ERROR'
        }
    }
}

function Test-TcpConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Server,

        [Parameter(Mandatory = $false)]
        [int]$Port = 1433,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMilliseconds = 3000
    )

    try {
        # Extract server name and port if provided in format "server,port"
        if ($Server -match '^([^,]+),(\d+)$') {
            $Server = $matches[1]
            $Port = [int]$matches[2]
        }

        Write-Log "Testing TCP connection to $Server on port $Port" -Level 'DEBUG'

        # Create TCP client
        $tcpClient = New-Object System.Net.Sockets.TcpClient

        # Begin async connection attempt
        $connectResult = $tcpClient.BeginConnect($Server, $Port, $null, $null)

        # Wait for connection with timeout
        $waitResult = $connectResult.AsyncWaitHandle.WaitOne($TimeoutMilliseconds, $false)

        # Check if connection succeeded
        if ($waitResult) {
            try {
                # Complete the connection
                $tcpClient.EndConnect($connectResult)
                $connected = $tcpClient.Connected
            }
            catch {
                $connected = $false
                Write-Log "TCP connection error: $($_.Exception.Message)" -Level 'DEBUG'
            }
        }
        else {
            $connected = $false
            Write-Log "TCP connection timed out after $TimeoutMilliseconds ms" -Level 'DEBUG'
        }

        # Close the connection
        if ($tcpClient.Connected) {
            $tcpClient.Close()
        }

        # Dispose of the client
        $tcpClient.Dispose()

        return $connected
    }
    catch {
        Write-Log "Error in Test-TcpConnection: $($_.Exception.Message)" -Level 'DEBUG'
        return $false
    }
}

function Test-DatabaseConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConnectionString = "",

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [switch]$SkipModuleCheck,

        [Parameter(Mandatory = $false)]
        [switch]$SkipOdbcFallback,

        [Parameter(Mandatory = $false)]
        [switch]$SkipTcpFallback
    )

    # If no connection string is provided, try to get it from CompleteView config
    if ([string]::IsNullOrEmpty($ConnectionString)) {
        try {
            # Get the Management Server path from the global config if available
            $managementServerPath = $null

            if ($script:config -and $script:config.InstallPaths -and $script:config.InstallPaths.ManagementServer) {
                $managementServerPath = $script:config.InstallPaths.ManagementServer
                Write-Log "Using Management Server path from config: $managementServerPath" -Level 'DEBUG'
            }
            else {
                # Try to find the path using our path resolution function
                $installPaths = Find-InstallPath
                if ($installPaths -and $installPaths.ManagementServer) {
                    $managementServerPath = $installPaths.ManagementServer
                    Write-Log "Found Management Server path: $managementServerPath" -Level 'DEBUG'
                }
                else {
                    # Fall back to default path
                    $managementServerPath = "C:\Program Files\Salient Security Platform\CompleteView\Management Server"
                    Write-Log "Using default Management Server path: $managementServerPath" -Level 'WARNING'
                }
            }

            # Path to CompleteView config file that contains DB connection info
            $configPath = Join-Path -Path $managementServerPath -ChildPath "appsettings.json"

            if (Test-Path $configPath) {
                $config = Get-Content $configPath -Raw | ConvertFrom-Json
                $ConnectionString = $config.ConnectionString

                Write-Log "Successfully retrieved database connection string from $configPath" -Level 'INFO'
            }
            else {
                Write-Log "Management Server config file not found at: $configPath" -Level 'WARNING'

                # Try alternative locations
                $altPaths = @(
                    "C:\Program Files\Salient Security Platform\CompleteView\Management Server\appsettings.json",
                    "C:\Program Files (x86)\Salient Security Platform\CompleteView\Management Server\appsettings.json",
                    "C:\Program Files\Salient Systems\CompleteView\Management Server\appsettings.json",
                    "C:\Program Files (x86)\Salient Systems\CompleteView\Management Server\appsettings.json"
                )

                foreach ($altPath in $altPaths) {
                    if (Test-Path $altPath) {
                        Write-Log "Found alternative config file at: $altPath" -Level 'INFO'
                        try {
                            $config = Get-Content $altPath -Raw | ConvertFrom-Json
                            $ConnectionString = $config.ConnectionString
                            break
                        }
                        catch {
                            Write-Log "Error reading alternative config file: $($_.Exception.Message)" -Level 'WARNING'
                        }
                    }
                }
            }

            if ([string]::IsNullOrEmpty($ConnectionString)) {
                Write-Log "Could not find database connection string in appsettings.json" -Level 'WARNING'
                return $false
            }
        }
        catch {
            Write-Log "Error retrieving database connection string: $($_.Exception.Message)" -Level 'ERROR'
            return $false
        }
    }

    try {
        # Extract server and database name from connection string
        $serverPattern = "Server=([^;]+)"
        $dbPattern = "Database=([^;]+)"
        $userPattern = "User ID=([^;]+)"
        $passwordPattern = "Password=([^;]+)"

        $server = if ($ConnectionString -match $serverPattern) { $matches[1] } else { $null }
        $database = if ($ConnectionString -match $dbPattern) { $matches[1] } else { $null }
        $userId = if ($ConnectionString -match $userPattern) { $matches[1] } else { $null }
        $password = if ($ConnectionString -match $passwordPattern) { $matches[1] } else { $null }

        if (-not $server -or -not $database) {
            Write-Log "Could not parse server or database from connection string" -Level 'WARNING'
            return $false
        }

        $connected = $false
        $errorMessages = @()

        # Method 1: Try using SQL Server PowerShell module if available and not skipped
        if (-not $SkipModuleCheck) {
            $sqlServerModuleAvailable = Get-Module -ListAvailable -Name SqlServer
            if ($sqlServerModuleAvailable) {
                try {
                    Write-Log "Testing database connection using SqlServer PowerShell module" -Level 'INFO'
                    Import-Module SqlServer -ErrorAction Stop

                    # Create parameter hashtable
                    $sqlParams = @{
                        ServerInstance = $server
                        Database = $database
                        Query = "SELECT 1 AS IsConnected"
                        ConnectionTimeout = 10
                        ErrorAction = 'Stop'
                    }

                    # Add credentials if using SQL authentication
                    if ($userId -and $password) {
                        $sqlCredential = New-Object System.Management.Automation.PSCredential($userId, (ConvertTo-SecureString $password -AsPlainText -Force))
                        $sqlParams.Credential = $sqlCredential
                    }

                    $result = Invoke-Sqlcmd @sqlParams
                    $connected = $result.IsConnected -eq 1

                    if ($connected) {
                        Write-Log "Successfully connected to database using SqlServer module" -Level 'INFO'
                        return $true
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    $errorMessages += "SqlServer module error: $errorMsg"
                    Write-Log "SqlServer module connection failed: $errorMsg" -Level 'WARNING'
                }
            }
            else {
                Write-Log "SqlServer PowerShell module not available" -Level 'INFO'
            }
        }

        # Method 2: Try using .NET SqlClient
        try {
            Write-Log "Testing database connection using .NET SqlClient" -Level 'INFO'
            $conn = New-Object System.Data.SqlClient.SqlConnection
            $conn.ConnectionString = $ConnectionString
            $conn.Open()
            $connected = $conn.State -eq [System.Data.ConnectionState]::Open

            if ($connected) {
                Write-Log "Successfully connected to database using .NET SqlClient" -Level 'INFO'
                $conn.Close()
                return $true
            }
            $conn.Close()
        }
        catch {
            $errorMsg = $_.Exception.Message
            $errorMessages += ".NET SqlClient error: $errorMsg"
            Write-Log ".NET SqlClient connection failed: $errorMsg" -Level 'WARNING'
        }

        # Method 3: Try using ODBC if not skipped
        if (-not $SkipOdbcFallback) {
            try {
                Write-Log "Testing database connection using ODBC" -Level 'INFO'

                # Build ODBC connection string
                $odbcConnString = "Driver={SQL Server};"
                $odbcConnString += "Server=$server;"
                $odbcConnString += "Database=$database;"

                if ($userId -and $password) {
                    $odbcConnString += "UID=$userId;PWD=$password;"
                }
                else {
                    $odbcConnString += "Trusted_Connection=Yes;"
                }

                # Create and open connection
                $odbcConn = New-Object System.Data.Odbc.OdbcConnection
                $odbcConn.ConnectionString = $odbcConnString
                $odbcConn.Open()
                $connected = $odbcConn.State -eq [System.Data.ConnectionState]::Open

                if ($connected) {
                    Write-Log "Successfully connected to database using ODBC" -Level 'INFO'
                    $odbcConn.Close()
                    return $true
                }
                $odbcConn.Close()
            }
            catch {
                $errorMsg = $_.Exception.Message
                $errorMessages += "ODBC error: $errorMsg"
                Write-Log "ODBC connection failed: $errorMsg" -Level 'WARNING'
            }
        }

        # Method 4: Last resort - just check TCP connectivity if not skipped
        if (-not $SkipTcpFallback) {
            try {
                Write-Log "Testing basic TCP connectivity to SQL Server" -Level 'INFO'
                $tcpConnected = Test-TcpConnection -Server $server -TimeoutMilliseconds 5000

                if ($tcpConnected) {
                    Write-Log "TCP connection to SQL Server successful, but database connectivity failed" -Level 'WARNING'
                    # We don't return true here because we only verified network connectivity, not database access
                }
                else {
                    $errorMessages += "TCP connection failed: Cannot establish network connection to SQL Server"
                    Write-Log "TCP connection to SQL Server failed - server may be unreachable" -Level 'WARNING'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                $errorMessages += "TCP test error: $errorMsg"
                Write-Log "TCP connection test failed: $errorMsg" -Level 'WARNING'
            }
        }

        # If we get here, all connection methods failed
        Write-Log "All database connection methods failed" -Level 'WARNING'

        # Combine error messages for notification
        $combinedErrorMsg = $errorMessages -join "`n"

        # Send notification if Slack is enabled
        if (-not $NoSlack -and $WebhookUrl) {
            $messageText = ":warning: *Database Connection Failure*

*Host:* $env:COMPUTERNAME
*Server:* $server
*Database:* $database
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            if ($combinedErrorMsg) {
                $messageText += "`n*Errors:*`n$combinedErrorMsg"
            }

            try {
                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText -QueueOnFailure
                Write-Log "Sent database connection failure notification" -Level 'INFO'
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Failed to send database connection failure notification: $errorMsg" -Level 'ERROR'
            }
        }

        return $false
    }
    catch {
        $mainErrorMsg = $_.Exception.Message
        Write-Log "Error testing database connection: $mainErrorMsg" -Level 'ERROR'

        # Send notification if Slack is enabled
        if (-not $NoSlack -and $WebhookUrl) {
            $messageText = ":warning: *Database Connection Error*

*Host:* $env:COMPUTERNAME
*Server:* $server
*Database:* $database
*Error:* $mainErrorMsg
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            try {
                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText -QueueOnFailure
                Write-Log "Sent database connection error notification" -Level 'INFO'
            }
            catch {
                $slackErrorMsg = $_.Exception.Message
                Write-Log "Failed to send database connection error notification: $slackErrorMsg" -Level 'ERROR'
            }
        }

        return $false
    }
}

function Test-DiskSpace {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string[]]$PathsToCheck = @(
            "C:\Program Files\Salient Security Platform",
            "C:\ProgramData\Salient Security Platform"
        ),

        [Parameter(Mandatory = $false)]
        [int]$ThresholdPercent = 10,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking disk space for critical paths" -Level 'INFO'

    $checkedDrives = @{}

    foreach ($path in $PathsToCheck) {
        if (Test-Path $path) {
            $drive = (Get-Item $path).PSDrive.Name

            # Skip if we've already checked this drive
            if ($checkedDrives.ContainsKey($drive)) {
                continue
            }

            $checkedDrives[$drive] = $true

            try {
                $driveInfo = Get-PSDrive -Name $drive -PSProvider FileSystem
                $freeSpaceGB = [Math]::Round($driveInfo.Free / 1GB, 2)
                $totalSpaceGB = [Math]::Round(($driveInfo.Free + $driveInfo.Used) / 1GB, 2)
                $freePercent = [Math]::Round(($driveInfo.Free / ($driveInfo.Free + $driveInfo.Used)) * 100, 2)

                Write-Log "Drive ${drive}: has $freeSpaceGB GB free ($freePercent%)" -Level 'INFO'

                if ($freePercent -lt $ThresholdPercent) {
                    Write-Log "Low disk space on drive ${drive}: ($freePercent% free, threshold: $ThresholdPercent%)" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Low Disk Space*

*Host:* $env:COMPUTERNAME
*Drive:* ${drive}:
*Free Space:* $freeSpaceGB GB of $totalSpaceGB GB ($freePercent%)
*Threshold:* $ThresholdPercent%
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent low disk space notification for drive ${drive}:" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send low disk space notification for drive ${drive}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
            }
            catch {
                Write-Log "Error checking disk space for drive ${drive}: $_" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Path not found: $path" -Level 'WARNING'
        }
    }
}

function Search-LogFiles {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [int]$LookbackMinutes = 60
    )

    Write-Log "Analyzing log files for critical errors" -Level 'INFO'

    # Get the logs folder path from the global config if available
    $logsFolder = $null

    if ($script:config -and $script:config.InstallPaths -and $script:config.InstallPaths.LogsFolder) {
        $logsFolder = $script:config.InstallPaths.LogsFolder
        Write-Log "Using logs folder from config: $logsFolder" -Level 'DEBUG'
    }
    else {
        # Try to find the path using our path resolution function
        $installPaths = Find-InstallPath
        if ($installPaths -and $installPaths.LogsFolder) {
            $logsFolder = $installPaths.LogsFolder
            Write-Log "Found logs folder: $logsFolder" -Level 'DEBUG'
        }
        else {
            # Fall back to default path
            $logsFolder = "C:\ProgramData\Salient Security Platform\Logs"
            Write-Log "Using default logs folder: $logsFolder" -Level 'WARNING'
        }
    }

    $logPaths = @{
        "RecordingServer" = Join-Path -Path $logsFolder -ChildPath "RecordingServer*.log"
        "AdminService" = Join-Path -Path $logsFolder -ChildPath "AdminService*.log"
        "ManagementServer" = Join-Path -Path $logsFolder -ChildPath "ManagementServer*.log"
    }

    $errorPatterns = @(
        "CRITICAL",
        "FATAL",
        "Exception",
        "Error",
        "Failed to",
        "Failure",
        "Unable to"
    )

    $startTime = (Get-Date).AddMinutes(-$LookbackMinutes)

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        if ($logPaths.ContainsKey($serviceName)) {
            $logPath = $logPaths[$serviceName]

            try {
                $logFiles = Get-ChildItem -Path $logPath -ErrorAction SilentlyContinue

                if ($logFiles.Count -eq 0) {
                    Write-Log "No log files found for $friendlyName at path: $logPath" -Level 'WARNING'
                    continue
                }

                $criticalErrors = @()

                foreach ($logFile in $logFiles) {
                    # Only check files modified since our lookback time
                    if ($logFile.LastWriteTime -ge $startTime) {
                        $content = Get-Content -Path $logFile.FullName -ErrorAction SilentlyContinue

                        foreach ($line in $content) {
                            # Try to extract timestamp from the line
                            $hasTimestamp = $line -match '\[([\d\-]+\s[\d:]+)'
                            $timestamp = if ($hasTimestamp) { [datetime]::Parse($matches[1]) } else { $null }

                            # Skip if we have a timestamp and it's before our lookback period
                            if ($timestamp -and $timestamp -lt $startTime) {
                                continue
                            }

                            # Check for error patterns
                            foreach ($pattern in $errorPatterns) {
                                if ($line -match $pattern) {
                                    $criticalErrors += [PSCustomObject]@{
                                        Service = $friendlyName
                                        LogFile = $logFile.Name
                                        Timestamp = if ($timestamp) { $timestamp.ToString("yyyy-MM-dd HH:mm:ss") } else { "Unknown" }
                                        Message = $line.Trim()
                                    }
                                    break  # Once we've matched a pattern, no need to check others
                                }
                            }
                        }
                    }
                }

                # Report critical errors
                if ($criticalErrors.Count -gt 0) {
                    Write-Log "Found $($criticalErrors.Count) critical errors in $friendlyName logs" -Level 'WARNING'

                    # Group by log file to avoid too many notifications
                    $groupedErrors = $criticalErrors | Group-Object -Property LogFile

                    foreach ($group in $groupedErrors) {
                        $logFileName = $group.Name
                        $errorCount = $group.Count
                        $sampleErrors = $group.Group | Select-Object -First 3

                        $errorSamples = ($sampleErrors | ForEach-Object { "- [$($_.Timestamp)] $($_.Message)" }) -join "n"

                        # Send notification if Slack is enabled
                        if (-not $NoSlack -and $WebhookUrl) {
                            $messageText = ":warning: *Critical Errors in Logs*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Log File:* $logFileName
*Error Count:* $errorCount
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

*Sample Errors:*
$errorSamples"

                            if ($errorCount -gt 3) {
                                $messageText += "n_...and $($errorCount - 3) more errors_"
                            }

                            try {
                                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                                Write-Log "Sent log error notification for $friendlyName ($logFileName)" -Level 'INFO'
                            }
                            catch {
                                $errorMsg = $_.Exception.Message
                                Write-Log "Failed to send log error notification for ${friendlyName} ($logFileName): $errorMsg" -Level 'ERROR'
                            }
                        }
                    }
                }
                else {
                    Write-Log "No critical errors found in $friendlyName logs" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error analyzing log files for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "No log path defined for $friendlyName" -Level 'DEBUG'
        }
    }
}

function Initialize-Environment {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [hashtable]$InstallPaths = $null
    )

    try {
        # If no install paths were provided, find them
        if ($null -eq $InstallPaths) {
            Write-Log "Finding CompleteView installation paths" -Level 'INFO'

            # Use the Find-InstallPath function with the provided parameters
            $pathParams = @{}

            if (-not [string]::IsNullOrEmpty($ConfigFilePath)) {
                $pathParams.ConfigFilePath = $ConfigFilePath
            }

            if ($UseEnvironmentVariables) {
                $pathParams.UseEnvironmentVariables = $true
            }

            if ($ForceRegistryLookup) {
                $pathParams.ForceRegistryLookup = $true
            }

            $InstallPaths = Find-InstallPath @pathParams
        }

        # Get the crash dumps path (the most important one for our purposes)
        $crashDumpsPath = $InstallPaths.CrashDumps

        # Override with explicit install base path if provided
        if (-not [string]::IsNullOrEmpty($InstallBasePath)) {
            Write-Log "Using explicitly provided install base path: $InstallBasePath" -Level 'INFO'

            if (Test-Path $InstallBasePath) {
                # Update the install paths with the provided base path
                $InstallPaths.ProgramFiles = $InstallBasePath

                # Try to infer ProgramData path
                $inferredProgramData = "C:\ProgramData\Salient Security Platform"
                if (Test-Path $inferredProgramData) {
                    $InstallPaths.ProgramData = $inferredProgramData
                    $crashDumpsPath = Join-Path -Path $inferredProgramData -ChildPath "CrashDumps"
                    $InstallPaths.CrashDumps = $crashDumpsPath
                }

                # Update component paths
                $cvBasePath = Join-Path -Path $InstallBasePath -ChildPath "CompleteView"
                if (Test-Path $cvBasePath) {
                    $rsPath = Join-Path -Path $cvBasePath -ChildPath "Recording Server"
                    if (Test-Path $rsPath) {
                        $InstallPaths.RecordingServer = $rsPath
                        $InstallPaths.AdminService = $rsPath
                    }

                    $msPath = Join-Path -Path $cvBasePath -ChildPath "Management Server"
                    if (Test-Path $msPath) {
                        $InstallPaths.ManagementServer = $msPath
                    }
                }
            }
            else {
                Write-Log "Provided install base path does not exist: $InstallBasePath" -Level 'WARNING'
            }
        }

        # Validate and set state file path
        if (-not $StateFilePath) {
            $StateFilePath = Join-Path -Path $crashDumpsPath -ChildPath "CVCrashMonitor.state"
            Write-Log "Using default state file path: $StateFilePath" -Level 'INFO'
        }
        else {
            Write-Log "Using provided state file path: $StateFilePath" -Level 'INFO'
        }

        # Validate and set log path
        if (-not $LogPath) {
            $LogPath = Join-Path -Path $crashDumpsPath -ChildPath "CVCrashMonitor.log"
            Write-Log "Using default log path: $LogPath" -Level 'INFO'
        }
        else {
            Write-Log "Using provided log path: $LogPath" -Level 'INFO'
        }

        # Ensure log directory exists
        $logDir = Split-Path -Path $LogPath -Parent
        if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
            try {
                New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                Write-Log "Created log directory: $logDir" -Level 'INFO'
            }
            catch {
                # If we can't create the directory, try to use a fallback location
                Write-Log "Failed to create log directory: $($_.Exception.Message)" -Level 'ERROR'
                $tempPath = [System.IO.Path]::GetTempPath()
                $LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
                Write-Log "Using fallback log path: $LogPath" -Level 'WARNING'
            }
        }

        # Check if we can write to the log file
        try {
            $testContent = "Test log write"
            $testContent | Out-File -FilePath $LogPath -Append -ErrorAction Stop
            Write-Log "Verified log file is writable" -Level 'INFO'
        }
        catch {
            Write-Log "Cannot write to log file: $($_.Exception.Message)" -Level 'ERROR'
            $tempPath = [System.IO.Path]::GetTempPath()
            $LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
            Write-Log "Using fallback log path: $LogPath" -Level 'WARNING'

            # Test the fallback path
            try {
                $testContent | Out-File -FilePath $LogPath -Append -ErrorAction Stop
            }
            catch {
                Write-Log "Cannot write to fallback log file either: $($_.Exception.Message)" -Level 'ERROR'
                # At this point, we'll just use console logging
            }
        }

        # Check if we can write to the state file directory
        $stateDir = Split-Path -Path $StateFilePath -Parent
        if (-not (Test-Path -Path $stateDir -ErrorAction SilentlyContinue)) {
            try {
                New-Item -Path $stateDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                Write-Log "Created state file directory: $stateDir" -Level 'INFO'
            }
            catch {
                Write-Log "Failed to create state file directory: $($_.Exception.Message)" -Level 'ERROR'
                $tempPath = [System.IO.Path]::GetTempPath()
                $StateFilePath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.state"
                Write-Log "Using fallback state file path: $StateFilePath" -Level 'WARNING'
            }
        }

        # Define service configurations
        $serviceConfigs = @{}

        # Recording Server configuration
        if ($MonitorRecordingServer) {
            $recordingServerPath = $InstallPaths.RecordingServer
            $recordingServerExe = Join-Path -Path $recordingServerPath -ChildPath "RecordingServer64.exe"

            $recordingServerConfig = @{
                AppName = "RecordingServer64.exe"
                DumpPath = Join-Path -Path $crashDumpsPath -ChildPath "RecordingServer"
                InstallPath = $recordingServerExe
                FriendlyName = "CompleteView Recording Server"
            }
            $serviceConfigs["RecordingServer"] = $recordingServerConfig
        }

        # Administrative Service configuration
        if ($MonitorAdminService) {
            $adminServicePath = $InstallPaths.AdminService
            $adminServiceExe = Join-Path -Path $adminServicePath -ChildPath "AdminService64.exe"

            $adminServiceConfig = @{
                AppName = "AdminService64.exe"
                DumpPath = Join-Path -Path $crashDumpsPath -ChildPath "AdminService"
                InstallPath = $adminServiceExe
                FriendlyName = "CompleteView Administrative Service"
            }
            $serviceConfigs["AdminService"] = $adminServiceConfig
        }

        # Management Server configuration
        if ($MonitorManagementServer) {
            $managementServerPath = $InstallPaths.ManagementServer
            $managementServerExe = Join-Path -Path $managementServerPath -ChildPath "ManagementServer.exe"

            $managementServerConfig = @{
                AppName = "ManagementServer.exe"
                DumpPath = Join-Path -Path $crashDumpsPath -ChildPath "ManagementServer"
                InstallPath = $managementServerExe
                FriendlyName = "CompleteView Management Server"
            }
            $serviceConfigs["ManagementServer"] = $managementServerConfig
        }

        # Ensure dump directories exist for each service
        foreach ($serviceName in $serviceConfigs.Keys) {
            $config = $serviceConfigs[$serviceName]
            $dumpDir = $config.DumpPath

            if (-not (Test-Path -Path $dumpDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $dumpDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created dump directory for $($config.FriendlyName): $dumpDir" -Level 'INFO'
                }
                catch {
                    Write-Log "Failed to create dump directory for $($config.FriendlyName): $($_.Exception.Message)" -Level 'ERROR'
                }
            }

            # Verify executable exists
            if (-not (Test-Path -Path $config.InstallPath -ErrorAction SilentlyContinue)) {
                Write-Log "Warning: Executable for $($config.FriendlyName) not found at $($config.InstallPath)" -Level 'WARNING'
            }
        }

        # Return the configuration
        return @{
            LogPath = $LogPath
            StateFilePath = $StateFilePath
            BaseDirectory = $crashDumpsPath
            ServiceConfigs = $serviceConfigs
            InstallPaths = $InstallPaths
        }
    }
    catch {
        Write-Log "Critical error in Initialize-Environment: $($_.Exception.Message)" -Level 'ERROR'

        # Use temp directory as last resort
        $tempPath = [System.IO.Path]::GetTempPath()
        return @{
            LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
            StateFilePath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.state"
            BaseDirectory = $tempPath
            ServiceConfigs = @{}
            InstallPaths = $InstallPaths
        }
    }
}

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('INFO', 'WARNING', 'ERROR', 'DEBUG')]
        [string]$Level = 'INFO',

        [Parameter(Mandatory = $false)]
        [int]$MaxLogSizeMB = 10,

        [Parameter(Mandatory = $false)]
        [switch]$NoConsole
    )

    try {
        # Check if we should log this message based on the global LogLevel
        $logLevelPriority = @{
            'ERROR' = 0
            'WARNING' = 1
            'INFO' = 2
            'DEBUG' = 3
        }

        # Skip logging if the message level is less important than the global level
        if ($logLevelPriority[$Level] -gt $logLevelPriority[$script:LogLevel]) {
            return
        }

        # Format timestamp and message
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logMessage = "[$timestamp] [$Level] $Message"

        # Write to console with appropriate color
        if (-not $NoConsole) {
            switch ($Level) {
                'ERROR' {
                    Write-Host $logMessage -ForegroundColor Red
                }
                'WARNING' {
                    Write-Host $logMessage -ForegroundColor Yellow
                }
                'INFO' {
                    Write-Host $logMessage -ForegroundColor White
                }
                'DEBUG' {
                    if ($VerbosePreference -eq 'Continue' -or $script:LogLevel -eq 'DEBUG') {
                        Write-Host $logMessage -ForegroundColor Gray
                    }
                }
                default {
                    Write-Host $logMessage
                }
            }
        }

        # Write to log file if path is defined
        if ($LogPath) {
            # Check if log rotation is needed
            if ((Test-Path $LogPath -ErrorAction SilentlyContinue) -and
                ((Get-Item $LogPath -ErrorAction SilentlyContinue).Length -gt ($MaxLogSizeMB * 1MB))) {

                # Rotate logs - keep up to 3 backups
                try {
                    if (Test-Path "$LogPath.2.bak" -ErrorAction SilentlyContinue) {
                        Remove-Item "$LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$LogPath.1.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$LogPath.1.bak" "$LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$LogPath.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$LogPath.bak" "$LogPath.1.bak" -Force -ErrorAction SilentlyContinue
                    }

                    Rename-Item $LogPath "$LogPath.bak" -Force -ErrorAction SilentlyContinue
                }
                catch {
                    # If rotation fails, just append to the existing log
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to rotate log files: $errorMsg" -ForegroundColor Yellow
                }
            }

            # Ensure log directory exists
            $logDir = Split-Path -Path $LogPath -Parent
            if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                }
                catch {
                    # If we can't create the directory, just write to console
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to create log directory: $errorMsg" -ForegroundColor Red
                    return
                }
            }

            # Write to log file with error handling
            try {
                Add-Content -Path $LogPath -Value $logMessage -ErrorAction Stop
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Host "Failed to write to log file: $errorMsg" -ForegroundColor Red
            }
        }
    }
    catch {
        # Last resort error handling - just try to output something
        $errorMsg = $_.Exception.Message
        Write-Host "Error in Write-Log function: $errorMsg" -ForegroundColor Red
        Write-Host $Message -ForegroundColor Cyan
    }
}

function Add-SlackMessageToQueue {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $true)]
        [string]$MessageText,

        [Parameter(Mandatory = $false)]
        [string]$QueuePath = ""
    )

    try {
        # If no queue path is provided, use the default location
        if ([string]::IsNullOrEmpty($QueuePath)) {
            # Use the script's base directory or temp folder as fallback
            if ($script:config -and $script:config.BaseDirectory) {
                $queueDir = Join-Path -Path $script:config.BaseDirectory -ChildPath "SlackQueue"
            }
            else {
                $queueDir = Join-Path -Path $env:TEMP -ChildPath "CVMonitorSlackQueue"
            }

            # Ensure queue directory exists
            if (-not (Test-Path -Path $queueDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $queueDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created Slack message queue directory: $queueDir" -Level 'INFO'
                }
                catch {
                    # If we can't create the directory, use temp directly
                    $queueDir = $env:TEMP
                    Write-Log "Failed to create queue directory, using $queueDir instead: $($_.Exception.Message)" -Level 'WARNING'
                }
            }

            $QueuePath = Join-Path -Path $queueDir -ChildPath "slack_queue.json"
        }

        # Create message object with timestamp and unique ID
        $messageObj = @{
            Id = [guid]::NewGuid().ToString()
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            WebhookUrl = $WebhookUrl
            MessageText = $MessageText
            RetryCount = 0
        }

        # Load existing queue if it exists
        $queue = @()
        if (Test-Path -Path $QueuePath -ErrorAction SilentlyContinue) {
            try {
                $queueContent = Get-Content -Path $QueuePath -Raw -ErrorAction Stop
                if (-not [string]::IsNullOrEmpty($queueContent)) {
                    $queue = $queueContent | ConvertFrom-Json -ErrorAction Stop
                    # Ensure it's an array even if there's only one item
                    if ($queue -isnot [array]) {
                        $queue = @($queue)
                    }
                }
            }
            catch {
                Write-Log "Error reading queue file, creating new queue: $($_.Exception.Message)" -Level 'WARNING'
                $queue = @()
            }
        }

        # Add new message to queue
        $queue += $messageObj

        # Save updated queue
        $queue | ConvertTo-Json -Depth 5 | Out-File -FilePath $QueuePath -Force -ErrorAction Stop

        Write-Log "Message queued successfully. Queue now contains $($queue.Count) messages." -Level 'INFO'
        return $QueuePath
    }
    catch {
        Write-Log "Error queuing Slack message: $($_.Exception.Message)" -Level 'ERROR'
        return $null
    }
}

function Send-QueuedSlackMessages {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$QueuePath = "",

        [Parameter(Mandatory = $false)]
        [int]$MaxMessagesToProcess = 10,

        [Parameter(Mandatory = $false)]
        [int]$MaxRetries = 3
    )

    try {
        # If no queue path is provided, use the default location
        if ([string]::IsNullOrEmpty($QueuePath)) {
            # Use the script's base directory or temp folder as fallback
            if ($script:config -and $script:config.BaseDirectory) {
                $queueDir = Join-Path -Path $script:config.BaseDirectory -ChildPath "SlackQueue"
            }
            else {
                $queueDir = Join-Path -Path $env:TEMP -ChildPath "CVMonitorSlackQueue"
            }

            $QueuePath = Join-Path -Path $queueDir -ChildPath "slack_queue.json"
        }

        # Check if queue file exists
        if (-not (Test-Path -Path $QueuePath -ErrorAction SilentlyContinue)) {
            Write-Log "No queued messages found at $QueuePath" -Level 'INFO'
            return 0
        }

        # Load queue
        try {
            $queueContent = Get-Content -Path $QueuePath -Raw -ErrorAction Stop
            if ([string]::IsNullOrEmpty($queueContent)) {
                Write-Log "Queue file is empty" -Level 'INFO'
                return 0
            }

            $queue = $queueContent | ConvertFrom-Json -ErrorAction Stop
            # Ensure it's an array even if there's only one item
            if ($queue -isnot [array]) {
                $queue = @($queue)
            }
        }
        catch {
            Write-Log "Error reading queue file: $($_.Exception.Message)" -Level 'ERROR'
            return 0
        }

        if ($queue.Count -eq 0) {
            Write-Log "No messages in queue" -Level 'INFO'
            return 0
        }

        Write-Log "Found $($queue.Count) queued messages" -Level 'INFO'

        # Check internet connectivity before attempting to send
        $canConnect = $false
        try {
            $testConnection = Test-NetConnection -ComputerName "hooks.slack.com" -Port 443 -InformationLevel Quiet -ErrorAction SilentlyContinue -WarningAction SilentlyContinue
            $canConnect = $testConnection
        }
        catch {
            Write-Log "Error testing connection to Slack: $($_.Exception.Message)" -Level 'WARNING'
        }

        if (-not $canConnect) {
            Write-Log "Cannot connect to Slack API, will try again later" -Level 'WARNING'
            return 0
        }

        # Process messages (limited by MaxMessagesToProcess)
        $successCount = 0
        $remainingMessages = @()

        for ($i = 0; $i -lt $queue.Count; $i++) {
            $message = $queue[$i]

            # Skip messages that have exceeded max retries
            if ($message.RetryCount -ge $MaxRetries) {
                Write-Log "Skipping message ID $($message.Id) - exceeded maximum retry count" -Level 'WARNING'
                continue
            }

            # Only process up to the max number of messages
            if ($i -ge $MaxMessagesToProcess) {
                $remainingMessages += $message
                continue
            }

            # Try to send the message
            try {
                # Use the core send logic without queuing
                $params = @{
                    Uri = $message.WebhookUrl
                    Method = 'Post'
                    Body = @{ text = $message.MessageText } | ConvertTo-Json -Depth 3
                    ContentType = 'application/json'
                    TimeoutSec = 30
                    ErrorAction = 'Stop'
                }

                $response = Invoke-RestMethod @params

                if ($response -eq "ok") {
                    Write-Log "Successfully sent queued message ID $($message.Id) from $(Get-Date $message.Timestamp -Format 'yyyy-MM-dd HH:mm:ss')" -Level 'INFO'
                    $successCount++
                }
                else {
                    # Unexpected response, increment retry count and keep in queue
                    $message.RetryCount++
                    Write-Log "Unexpected response for message ID $($message.Id): $response" -Level 'WARNING'
                    $remainingMessages += $message
                }
            }
            catch {
                # Failed to send, increment retry count and keep in queue
                $message.RetryCount++
                Write-Log "Failed to send queued message ID $($message.Id): $($_.Exception.Message)" -Level 'WARNING'
                $remainingMessages += $message
            }
        }

        # Add any remaining messages (beyond MaxMessagesToProcess) to the queue
        for ($i = $MaxMessagesToProcess; $i -lt $queue.Count; $i++) {
            if ($i -lt $queue.Count) {
                $remainingMessages += $queue[$i]
            }
        }

        # Update queue file
        if ($remainingMessages.Count -gt 0) {
            $remainingMessages | ConvertTo-Json -Depth 5 | Out-File -FilePath $QueuePath -Force -ErrorAction Stop
            Write-Log "Updated queue with $($remainingMessages.Count) remaining messages" -Level 'INFO'
        }
        else {
            # Queue is empty, delete the file
            Remove-Item -Path $QueuePath -Force -ErrorAction SilentlyContinue
            Write-Log "All queued messages processed, queue file removed" -Level 'INFO'
        }

        return $successCount
    }
    catch {
        Write-Log "Error processing queued messages: $($_.Exception.Message)" -Level 'ERROR'
        return 0
    }
}

function Send-SlackMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $true)]
        [string]$MessageText,

        [Parameter(Mandatory = $false)]
        [int]$MaxRetries = 2,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutSec = 30,

        [Parameter(Mandatory = $false)]
        [switch]$QueueOnFailure,

        [Parameter(Mandatory = $false)]
        [switch]$SkipConnectivityCheck
    )

    # Validate webhook URL
    if (-not $WebhookUrl -or -not $WebhookUrl.StartsWith("https://hooks.slack.com/")) {
        throw "Invalid Slack webhook URL format"
    }

    try {
        # Check if we should process any queued messages first
        if (-not $SkipConnectivityCheck) {
            try {
                $queuedSent = Send-QueuedSlackMessages -MaxMessagesToProcess 5
                if ($queuedSent -gt 0) {
                    Write-Log "Sent $queuedSent previously queued messages" -Level 'INFO'
                }
            }
            catch {
                Write-Log "Error processing queued messages: $($_.Exception.Message)" -Level 'WARNING'
            }
        }

        # Check internet connectivity before attempting to send (unless skipped)
        if (-not $SkipConnectivityCheck) {
            $canConnect = $false
            try {
                $testConnection = Test-NetConnection -ComputerName "hooks.slack.com" -Port 443 -InformationLevel Quiet -ErrorAction SilentlyContinue -WarningAction SilentlyContinue
                $canConnect = $testConnection
            }
            catch {
                Write-Log "Error testing connection to Slack: $($_.Exception.Message)" -Level 'WARNING'
            }

            if (-not $canConnect) {
                Write-Log "Cannot connect to Slack API (hooks.slack.com:443)" -Level 'WARNING'

                # Queue the message if requested
                if ($QueueOnFailure) {
                    Write-Log "Queuing message for later delivery" -Level 'INFO'
                    $queuePath = Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                    if ($queuePath) {
                        Write-Log "Message queued successfully at $queuePath" -Level 'INFO'
                        return $true # Return success since we've handled the message
                    }
                    else {
                        Write-Log "Failed to queue message" -Level 'ERROR'
                        throw "Failed to send or queue Slack notification: Cannot connect to Slack API"
                    }
                }
                else {
                    throw "Failed to send Slack notification: Cannot connect to Slack API"
                }
            }
        }

        # Convert to JSON with proper escaping
        $payload = @{
            text = $MessageText
        } | ConvertTo-Json -ErrorAction Stop -Depth 3 -Compress:$false

        # Add timeout and error handling for network issues
        $params = @{
            Uri = $WebhookUrl
            Method = 'Post'
            Body = $payload
            ContentType = 'application/json'
            TimeoutSec = $TimeoutSec
            ErrorAction = 'Stop'
        }

        # Add proxy support if system is configured to use a proxy
        try {
            $systemProxy = [System.Net.WebRequest]::GetSystemWebProxy()
            if ($systemProxy -and $systemProxy.GetProxy([uri]$WebhookUrl) -ne $WebhookUrl) {
                $proxyUrl = $systemProxy.GetProxy([uri]$WebhookUrl)
                Write-Log "Using system proxy for Slack API request: $proxyUrl" -Level 'DEBUG'
                $params.UseDefaultCredentials = $true
                # Only set the proxy if we have a valid URI
                if ($proxyUrl -and $proxyUrl.AbsoluteUri) {
                    $params.Proxy = $proxyUrl.AbsoluteUri
                    $params.ProxyUseDefaultCredentials = $true
                }
            }
        }
        catch {
            Write-Log "Error detecting proxy settings: $($_.Exception.Message)" -Level 'DEBUG'
            # Continue without proxy settings
        }

        # Send the notification with retry logic for transient errors
        $retryCount = 0
        $success = $false

        do {
            try {
                $response = Invoke-RestMethod @params

                # Slack webhooks return "ok" when successful
                if ($response -ne "ok") {
                    Write-Log "Slack API returned unexpected response: $response" -Level 'WARNING'
                }

                Write-Log "Slack notification sent successfully" -Level 'DEBUG'
                $success = $true
                return $true
            }
            catch [System.Net.WebException] {
                $statusCode = 0

                # Try to get status code if available
                if ($null -ne $_.Exception.Response) {
                    $statusCode = [int]$_.Exception.Response.StatusCode
                }

                # Handle specific HTTP status codes
                switch ($statusCode) {
                    429 {
                        # Rate limiting - wait and retry
                        $retryCount++
                        $waitTime = [Math]::Min(10 * $retryCount, 30)  # Exponential backoff with max 30 seconds
                        Write-Log "Slack API rate limit exceeded. Retrying in $waitTime seconds..." -Level 'WARNING'
                        Start-Sleep -Seconds $waitTime
                    }
                    500 {
                        # Server error - wait and retry
                        $retryCount++
                        $waitTime = [Math]::Min(5 * $retryCount, 15)  # Exponential backoff with max 15 seconds
                        Write-Log "Slack API server error. Retrying in $waitTime seconds..." -Level 'WARNING'
                        Start-Sleep -Seconds $waitTime
                    }
                    0 {
                        # Network connectivity issue
                        $retryCount++
                        if ($retryCount -lt $MaxRetries) {
                            $waitTime = [Math]::Min(5 * $retryCount, 15)
                            Write-Log "Network connectivity issue. Retrying in $waitTime seconds..." -Level 'WARNING'
                            Start-Sleep -Seconds $waitTime
                        }
                        elseif ($QueueOnFailure) {
                            Write-Log "Network connectivity issue after $MaxRetries retries. Queuing message." -Level 'WARNING'
                            Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                            return $true # Return success since we've handled the message
                        }
                        else {
                            Write-Log "Network connectivity issue after $MaxRetries retries" -Level 'ERROR'
                            throw "Failed to send Slack notification: Network connectivity issue"
                        }
                    }
                    default {
                        # Other errors - log and throw or queue
                        Write-Log "Slack API error (HTTP $statusCode): $($_.Exception.Message)" -Level 'ERROR'

                        if ($QueueOnFailure) {
                            Write-Log "Queuing message after HTTP error $statusCode" -Level 'INFO'
                            Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                            return $true # Return success since we've handled the message
                        }
                        else {
                            throw "Failed to send Slack notification: HTTP $statusCode - $($_.Exception.Message)"
                        }
                    }
                }
            }
            catch {
                # General error - log and throw or queue
                Write-Log "Error sending Slack notification: $($_.Exception.Message)" -Level 'ERROR'

                if ($QueueOnFailure) {
                    Write-Log "Queuing message after general error" -Level 'INFO'
                    Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                    return $true # Return success since we've handled the message
                }
                else {
                    throw "Failed to send Slack notification: $($_.Exception.Message)"
                }
            }
        } while (-not $success -and $retryCount -lt $MaxRetries)

        # If we get here, we've exhausted retries
        if (-not $success) {
            Write-Log "Failed to send Slack notification after $MaxRetries retries" -Level 'ERROR'

            if ($QueueOnFailure) {
                Write-Log "Queuing message after exhausting retries" -Level 'INFO'
                Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                return $true # Return success since we've handled the message
            }
            else {
                throw "Failed to send Slack notification after $MaxRetries retries"
            }
        }

        return $true
    }
    catch {
        Write-Log "Error in Send-SlackMessage: $($_.Exception.Message)" -Level 'ERROR'

        if ($QueueOnFailure) {
            try {
                Write-Log "Attempting to queue message after error" -Level 'INFO'
                Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                return $true # Return success since we've handled the message
            }
            catch {
                Write-Log "Failed to queue message: $($_.Exception.Message)" -Level 'ERROR'
                throw "Failed to send or queue Slack notification: $($_.Exception.Message)"
            }
        }
        else {
            throw "Failed to send Slack notification: $($_.Exception.Message)"
        }
    }
}

function Send-SlackNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$AppName,

        [Parameter(Mandatory = $true)]
        [string]$Hostname,

        [Parameter(Mandatory = $true)]
        [string]$CrashTime,

        [Parameter(Mandatory = $false)]
        [string]$FaultingModule = "Unknown",

        [Parameter(Mandatory = $false)]
        [string]$ExceptionCode = "Unknown",

        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$DumpAvailable = $false,

        [Parameter(Mandatory = $false)]
        [bool]$InitialAlert = $true
    )

    try {
        # Check if URL is accessible (optional validation)
        try {
            $testConnection = Test-NetConnection -ComputerName "hooks.slack.com" -Port 443 -InformationLevel Quiet -ErrorAction SilentlyContinue -WarningAction SilentlyContinue
            if (-not $testConnection) {
                Write-Log "Warning: Cannot connect to Slack API (hooks.slack.com:443)" -Level 'WARNING'
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Could not test connection to Slack: $errorMsg" -Level 'WARNING'
        }

        # Sanitize inputs to prevent JSON formatting issues
        $AppName = $AppName -replace '["\r\n]', ' '
        $Hostname = $Hostname -replace '["\r\n]', ' '
        $CrashTime = $CrashTime -replace '["\r\n]', ' '
        $FaultingModule = $FaultingModule -replace '["\r\n]', ' '
        $ExceptionCode = $ExceptionCode -replace '["\r\n]', ' '
        # For the dump path, only remove quotes and newlines, but keep backslashes
        $DumpPath = $DumpPath -replace '["\r\n]', ' '

        # Prepare message text
        # For Slack, we don't need to escape backslashes when using code formatting with backticks
        # Just use the path as-is
        $formattedPath = $DumpPath

        # Use a single backtick for Slack code formatting
        $backtick = [char]96

        if ($DumpAvailable) {
            $dumpText = "*Dump File:* " + $backtick + $formattedPath + $backtick
        } else {
            $dumpText = "*Dump File:* _Not available_" + "n" + "Expected path: " + $backtick + $formattedPath + $backtick
        }

        if ($InitialAlert) {
            $title = "$AppName Crash Detected"
            $titleEmoji = ":rotating_light:"
        } else {
            $title = "Dump File Status Update"
            $titleEmoji = ":information_source:"
        }

        # Set emoji based on dump availability
        $statusEmoji = if ($DumpAvailable) { ":white_check_mark:" } else { ":x:" }

        # Create the message with proper line breaks for Slack
        $messageText = "$titleEmoji *$title*

*Host:* $Hostname
*Time:* $CrashTime
*Event Type:* $($details.EventType)
*Description:* $($details.Description)
*Faulting Module:* $FaultingModule
*Exception Code:* $ExceptionCode

$statusEmoji $dumpText"

        # Use the helper function to send the message with queuing on failure
        return Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText -QueueOnFailure
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error in Send-SlackNotification: $errorMsg" -Level 'ERROR'
        throw "Failed to send Slack notification: $errorMsg"
    }
}

function Wait-ForDumpFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$DumpPath,

        [Parameter(Mandatory = $false)]
        [int]$DelaySeconds = 3,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMinutes = 5,

        [Parameter(Mandatory = $false)]
        [int]$MinimumSizeKB = 1
    )

    try {
        # Validate parameters
        if ($DelaySeconds -lt 1) {
            Write-Log "DelaySeconds must be at least 1, setting to default of 3" -Level 'WARNING'
            $DelaySeconds = 3
        }

        if ($TimeoutMinutes -lt 1) {
            Write-Log "TimeoutMinutes must be at least 1, setting to default of 5" -Level 'WARNING'
            $TimeoutMinutes = 5
        }

        # Calculate timeout
        $startTime = Get-Date
        $endTime = $startTime.AddMinutes($TimeoutMinutes)
        $minimumSizeBytes = $MinimumSizeKB * 1KB

        Write-Log "Waiting for dump file: $DumpPath (timeout: $TimeoutMinutes minutes)" -Level 'INFO'

        # Wait loop with timeout
        while ((Get-Date) -lt $endTime) {
            # Check if file exists
            if (Test-Path -Path $DumpPath -ErrorAction SilentlyContinue) {
                try {
                    # Check if file is accessible and has content
                    $fileInfo = Get-Item -Path $DumpPath -ErrorAction Stop

                    # Check if file is still being written to
                    $initialSize = $fileInfo.Length
                    Start-Sleep -Seconds 1
                    $fileInfo.Refresh()

                    if ($fileInfo.Length -ge $minimumSizeBytes) {
                        # If file size hasn't changed, it's likely complete
                        if ($fileInfo.Length -eq $initialSize) {
                            $fileSizeKB = [Math]::Round($fileInfo.Length / 1KB, 2)
                            Write-Log "Dump file found and ready: $DumpPath ($fileSizeKB KB)" -Level 'INFO'
                            return $true
                        }
                        else {
                            Write-Log "Dump file found but still being written to: $DumpPath" -Level 'INFO'
                        }
                    }
                    else {
                        Write-Log "Dump file found but size is too small: $($fileInfo.Length) bytes" -Level 'INFO'
                    }
                }
                catch {
                    Write-Log "Error accessing dump file: $_" -Level 'WARNING'
                }
            }

            # Calculate and display remaining time periodically
            $elapsed = (Get-Date) - $startTime
            $remaining = $TimeoutMinutes * 60 - $elapsed.TotalSeconds

            if ($remaining -le 0) {
                break
            }

            if ($elapsed.TotalSeconds % 30 -lt $DelaySeconds) {
                Write-Log "Still waiting for dump file: $DumpPath ($('{0:N0}' -f $remaining) seconds remaining)" -Level 'INFO'
            }

            Start-Sleep -Seconds $DelaySeconds
        }

        Write-Log "Timeout waiting for dump file after $TimeoutMinutes minutes." -Level 'WARNING'
        return $false
    }
    catch {
        Write-Log "Error in Wait-ForDumpFile: $_" -Level 'ERROR'
        return $false
    }
}

function Get-CrashEvents {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [DateTime]$StartTime,

        [Parameter(Mandatory = $false)]
        [string]$LogName = 'Application',

        [Parameter(Mandatory = $false)]
        [int[]]$EventIDs = @(1000, 1001, 7031, 7034, 2004, 6008),

        [Parameter(Mandatory = $false)]
        [string[]]$AppNames = @(),

        [Parameter(Mandatory = $false)]
        [string]$ProviderName = '*'
    )

    try {
        Write-Log "Searching for crash events since $($StartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        Write-Log "Monitoring event IDs: $($EventIDs -join ', ')" -Level 'DEBUG'

        if ($AppNames.Count -gt 0) {
            Write-Log "Filtering for applications: $($AppNames -join ', ')" -Level 'DEBUG'
        }

        # Build filter hashtable
        $filterHash = @{
            LogName = $LogName
            ID = $EventIDs
            StartTime = $StartTime
        }

        # Add provider name if specified
        if ($ProviderName -ne '*') {
            $filterHash.ProviderName = $ProviderName
        }

        # Get events with error handling
        $events = Get-WinEvent -FilterHashtable $filterHash -ErrorAction SilentlyContinue -Verbose

        if ($null -eq $events -or $events.Count -eq 0) {
            Write-Log "No crash events found" -Level 'INFO'
            return $null
        }

        # Filter by application names if specified
        if ($AppNames.Count -gt 0) {
            $filteredEvents = @()

            foreach ($eventItem in $events) {
                $matchFound = $false

                # First try to match using XML data
                try {
                    $eventXml = [xml]$eventItem.ToXml()
                    $dataNodes = $eventXml.Event.EventData.Data

                    if ($dataNodes -and $dataNodes.Count -gt 0) {
                        # Look for application name in event data
                        foreach ($node in $dataNodes) {
                            if (($node.Name -eq "FaultingApplicationName" -or
                                 $node.Name -eq "Application" -or
                                 $node.Name -eq "ServiceName" -or
                                 $node.Name -eq "ProcessName") -and
                                $node.'#text') {

                                $appNameFromXml = $node.'#text'

                                foreach ($appName in $AppNames) {
                                    if ($appNameFromXml -like "*$appName*") {
                                        $matchFound = $true
                                        break
                                    }
                                }

                                if ($matchFound) { break }
                            }
                        }
                    }

                    # If we couldn't find a match in XML, try the Properties collection
                    if (-not $matchFound -and $eventItem.Properties -and $eventItem.Properties.Count -gt 0) {
                        # For event ID 1000, application name is typically the first property
                        if ($eventItem.Id -eq 1000 -and $eventItem.Properties.Count -gt 0) {
                            $appNameFromProps = $eventItem.Properties[0].Value.ToString()

                            foreach ($appName in $AppNames) {
                                if ($appNameFromProps -like "*$appName*") {
                                    $matchFound = $true
                                    break
                                }
                            }
                        }

                        # For service events (7031, 7034), service name is typically the first property
                        if (($eventItem.Id -eq 7031 -or $eventItem.Id -eq 7034) -and $eventItem.Properties.Count -gt 0) {
                            $serviceNameFromProps = $eventItem.Properties[0].Value.ToString()

                            foreach ($appName in $AppNames) {
                                if ($serviceNameFromProps -like "*$appName*") {
                                    $matchFound = $true
                                    break
                                }
                            }
                        }
                    }
                }
                catch {
                    Write-Log "Error parsing event XML for filtering: $_" -Level 'DEBUG'
                    # Continue to message-based filtering as fallback
                }

                # If we still don't have a match, fall back to message-based filtering
                if (-not $matchFound) {
                    $message = $eventItem.Message
                    if (-not [string]::IsNullOrEmpty($message)) {
                        foreach ($appName in $AppNames) {
                            if ($message -match [regex]::Escape($appName)) {
                                $matchFound = $true
                                break
                            }
                        }
                    }
                }

                if ($matchFound) {
                    $filteredEvents += $eventItem
                }
            }

            $events = $filteredEvents

            if ($events.Count -eq 0) {
                Write-Log "No events found matching the specified applications" -Level 'INFO'
                return $null
            }
        }

        # Log event ID distribution
        $eventCounts = $events | Group-Object -Property Id | Select-Object Name, Count
        foreach ($eventType in $eventCounts) {
            Write-Log "Found $($eventType.Count) events with ID $($eventType.Name)" -Level 'INFO'
        }

        Write-Log "Found $($events.Count) total potential crash/error events" -Level 'INFO'
        return $events
    }
    catch [System.InvalidOperationException] {
        # This exception is thrown when no events match the filter
        Write-Log "No events found matching the criteria" -Level 'INFO'
        return $null
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error retrieving crash events: $errorMsg" -Level 'ERROR'

        # Try an alternative approach if the primary method fails
        try {
            Write-Log "Attempting alternative event retrieval method" -Level 'INFO'

            # Use Get-WinEvent with XPath query as an alternative approach
            try {
                $xpathQueries = @()
                foreach ($eventId in $EventIDs) {
                    $xpathQueries += "*[System[EventID=$eventId]]"
                }

                $xpathFilter = "*[System[TimeCreated[@SystemTime>='$($StartTime.ToUniversalTime().ToString("o"))']]] and (*[System[(" + ($xpathQueries -join ") or (") + ")]])"

                $events = Get-WinEvent -LogName $LogName -FilterXPath $xpathFilter -ErrorAction SilentlyContinue

                if ($events -and $events.Count -gt 0) {
                    Write-Log "Found $($events.Count) events using XPath query" -Level 'INFO'

                    # Apply application filtering if needed
                    if ($AppNames.Count -gt 0) {
                        $filteredEvents = @()
                        foreach ($eventItem in $events) {
                            $message = $eventItem.Message
                            $matchFound = $false

                            foreach ($appName in $AppNames) {
                                if ($message -match [regex]::Escape($appName)) {
                                    $matchFound = $true
                                    break
                                }
                            }

                            if ($matchFound) {
                                $filteredEvents += $eventItem
                            }
                        }

                        $events = $filteredEvents
                    }

                    return $events
                }
            }
            catch {
                Write-Log "XPath query approach failed: $_" -Level 'WARNING'
                # Continue to next fallback method
            }

            # Fall back to Get-EventLog as a last resort
            $allEvents = @()
            foreach ($eventId in $EventIDs) {
                $events = Get-EventLog -LogName $LogName -After $StartTime -EntryType Error,Warning -ErrorAction SilentlyContinue |
                          Where-Object { $_.EventID -eq $eventId }

                if ($null -ne $events -and $events.Count -gt 0) {
                    $allEvents += $events
                }
            }

            # Filter by application names if specified
            if ($AppNames.Count -gt 0 -and $allEvents.Count -gt 0) {
                $filteredEvents = @()

                foreach ($eventItem in $allEvents) {
                    $message = $eventItem.Message
                    $matchFound = $false

                    foreach ($appName in $AppNames) {
                        if ($message -match [regex]::Escape($appName)) {
                            $matchFound = $true
                            break
                        }
                    }

                    if ($matchFound) {
                        $filteredEvents += $eventItem
                    }
                }

                $allEvents = $filteredEvents
            }

            if ($allEvents.Count -gt 0) {
                Write-Log "Found $($allEvents.Count) events using Get-EventLog method" -Level 'INFO'
                return $allEvents
            }
            else {
                Write-Log "No events found using alternative methods" -Level 'INFO'
                return $null
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "All alternative event retrieval methods failed: $errorMsg" -Level 'ERROR'
            return $null
        }
    }
}

function Format-EventDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [System.Diagnostics.Eventing.Reader.EventLogRecord]$LogEvent
    )

    try {
        # Initialize properties
        $props = @{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = $LogEvent.TimeCreated.ToString("yyyy-MM-dd HH:mm:ss")
            Computer = $env:COMPUTERNAME
            FaultingApp = $null
            FaultingModule = "Unknown"
            ExceptionCode = "Unknown"
            ProcessIdDecimal = $null
            EventType = "Unknown"
            Description = ""
            OSVersion = [Environment]::OSVersion.Version.ToString()
        }

        # Get the event XML for structured parsing
        $eventXml = [xml]$LogEvent.ToXml()

        # Get event data as a structured object
        $eventData = @{}

        # Extract event data from XML with error handling
        try {
            # Different events may store data in different locations
            $dataNodes = $eventXml.Event.EventData.Data

            if ($dataNodes -and $dataNodes.Count -gt 0) {
                foreach ($node in $dataNodes) {
                    if ($node.Name -and $node.'#text') {
                        $eventData[$node.Name] = $node.'#text'
                    }
                }
            }

            # If we couldn't get named data, try to use the Properties collection
            if ($eventData.Count -eq 0 -and $LogEvent.Properties -and $LogEvent.Properties.Count -gt 0) {
                Write-Log "Using Properties collection for event $($LogEvent.Id)" -Level 'DEBUG'

                # Map known property indexes to names based on event ID
                switch ($LogEvent.Id) {
                    1000 {
                        # Application Error event properties mapping
                        if ($LogEvent.Properties.Count -ge 7) {
                            $eventData["FaultingApplicationName"] = $LogEvent.Properties[0].Value.ToString()
                            $eventData["FaultingApplicationVersion"] = $LogEvent.Properties[1].Value.ToString()
                            $eventData["FaultingModuleName"] = $LogEvent.Properties[2].Value.ToString()
                            $eventData["FaultingModuleVersion"] = $LogEvent.Properties[3].Value.ToString()
                            $eventData["ExceptionCode"] = $LogEvent.Properties[4].Value.ToString()
                            $eventData["ExceptionOffset"] = $LogEvent.Properties[5].Value.ToString()
                            $eventData["ProcessId"] = $LogEvent.Properties[6].Value.ToString()
                        }
                    }
                    1001 {
                        # Windows Error Reporting properties mapping
                        # Property indexes vary, so we'll use a fallback to message parsing
                    }
                    7031 {
                        # Service terminated unexpectedly and was restarted
                        if ($LogEvent.Properties.Count -ge 2) {
                            $eventData["ServiceName"] = $LogEvent.Properties[0].Value.ToString()
                            $eventData["RestartAttempts"] = $LogEvent.Properties[1].Value.ToString()
                        }
                    }
                    7034 {
                        # Service terminated unexpectedly
                        if ($LogEvent.Properties.Count -ge 1) {
                            $eventData["ServiceName"] = $LogEvent.Properties[0].Value.ToString()
                        }
                    }
                }
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Error extracting event data from XML: $errorMsg" -Level 'WARNING'
            # Continue execution - we'll fall back to message parsing
        }

        # Fallback to message parsing if we couldn't get structured data
        $message = $LogEvent.Message
        if ($eventData.Count -eq 0 -and -not [string]::IsNullOrEmpty($message)) {
            Write-Log "Falling back to message parsing for event $($LogEvent.Id)" -Level 'DEBUG'
        }
        elseif ([string]::IsNullOrEmpty($message)) {
            Write-Log "Event message is empty or null" -Level 'WARNING'
            $message = ""
        }

        # Process based on event ID
        switch ($LogEvent.Id) {
            1000 {
                # Application crash
                $props.EventType = "Application Crash"

                # Try to get data from structured properties first
                if ($eventData.ContainsKey("FaultingApplicationName")) {
                    $props.FaultingApp = $eventData["FaultingApplicationName"]
                    $props.FaultingModule = $eventData["FaultingModuleName"]
                    $props.ExceptionCode = $eventData["ExceptionCode"]

                    # Convert process ID if it's in hex format
                    if ($eventData["ProcessId"] -match "^0x") {
                        try {
                            $props.ProcessIdDecimal = [Convert]::ToInt32($eventData["ProcessId"], 16)
                        }
                        catch {
                            Write-Log "Failed to convert process ID '$($eventData["ProcessId"])' to decimal: $_" -Level 'WARNING'
                        }
                    }
                    else {
                        # Try to parse as decimal
                        try {
                            $props.ProcessIdDecimal = [int]$eventData["ProcessId"]
                        }
                        catch {
                            Write-Log "Failed to parse process ID '$($eventData["ProcessId"])' as integer: $_" -Level 'WARNING'
                        }
                    }
                }
                # Fall back to regex parsing if structured data isn't available
                elseif (-not [string]::IsNullOrEmpty($message)) {
                    try {
                        if ($message -match "Faulting application name: ([^,]+)") {
                            $props.FaultingApp = $Matches[1].Trim()
                        }

                        if ($message -match "Faulting module name: ([^,]+)") {
                            $props.FaultingModule = $Matches[1].Trim()
                        }

                        if ($message -match "Exception code: (0x[0-9a-fA-F]+)") {
                            $props.ExceptionCode = $Matches[1].Trim()
                        }

                        if ($message -match "Faulting process id: (0x[0-9a-fA-F]+)") {
                            $hexId = $Matches[1].Trim()
                            try {
                                $props.ProcessIdDecimal = [Convert]::ToInt32($hexId, 16)
                            }
                            catch {
                                $errorMsg = $_.Exception.Message
                                Write-Log "Failed to convert process ID '$hexId' to decimal: $errorMsg" -Level 'WARNING'
                            }
                        }
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-Log "Error parsing event 1000 message: $errorMsg" -Level 'ERROR'
                    }
                }

                $props.Description = "Application crash detected"
            }

            1001 {
                # Windows Error Reporting
                $props.EventType = "Error Report"

                # WER events often don't have consistent structured data
                # Fall back to message parsing
                try {
                    # Try to extract the application name from WER message
                    if ($message -match "Fault bucket [^,]+, type [^,]+, process name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }
                    elseif ($message -match "process: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }

                    # Extract any additional details
                    if ($message -match "Fault bucket ([^,]+)") {
                        $props.ExceptionCode = $Matches[1].Trim()
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-Log "Error parsing event 1001 message: $errorMsg" -Level 'ERROR'
                }

                $props.Description = "Windows Error Reporting details"
            }

            7031 {
                # Service crashed and was recovered
                $props.EventType = "Service Recovery"

                # Try to get data from structured properties first
                if ($eventData.ContainsKey("ServiceName")) {
                    $props.FaultingApp = $eventData["ServiceName"]
                    $props.Description = "Service terminated unexpectedly and was restarted"

                    if ($eventData.ContainsKey("RestartAttempts")) {
                        $props.Description += " (Restart attempt: $($eventData["RestartAttempts"]))"
                    }
                }
                # Fall back to regex parsing
                elseif (-not [string]::IsNullOrEmpty($message)) {
                    try {
                        if ($message -match "The ([^(]+) service terminated unexpectedly") {
                            $serviceName = $Matches[1].Trim()
                            $props.FaultingApp = $serviceName
                            $props.Description = "Service terminated unexpectedly and was restarted"
                        }
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-Log "Error parsing event 7031 message: $errorMsg" -Level 'ERROR'
                    }
                }
            }

            7034 {
                # Service terminated unexpectedly
                $props.EventType = "Service Termination"

                # Try to get data from structured properties first
                if ($eventData.ContainsKey("ServiceName")) {
                    $props.FaultingApp = $eventData["ServiceName"]
                    $props.Description = "Service terminated unexpectedly"
                }
                # Fall back to regex parsing
                elseif (-not [string]::IsNullOrEmpty($message)) {
                    try {
                        if ($message -match "The ([^(]+) service terminated unexpectedly") {
                            $serviceName = $Matches[1].Trim()
                            $props.FaultingApp = $serviceName
                            $props.Description = "Service terminated unexpectedly"
                        }
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-Log "Error parsing event 7034 message: $errorMsg" -Level 'ERROR'
                    }
                }
            }

            2004 {
                # Memory resource notification
                $props.EventType = "Low Memory"
                $props.Description = "System is running low on memory"
                $props.FaultingApp = "System"
            }

            6008 {
                # Unexpected shutdown
                $props.EventType = "System Shutdown"
                $props.Description = "The system was unexpectedly shut down"
                $props.FaultingApp = "System"
            }

            default {
                # Unknown event type
                $props.EventType = "Unknown Event"
                $props.Description = "Unrecognized event type"

                # Try to extract any app name from the message
                if (-not [string]::IsNullOrEmpty($message)) {
                    if ($message -match "application name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }
                    elseif ($message -match "process name: ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }
                    elseif ($message -match "service ([^,]+)") {
                        $props.FaultingApp = $Matches[1].Trim()
                    }
                }
            }
        }

        # Validate and sanitize extracted data
        if ($props.FaultingApp -and $props.FaultingApp.Length -gt 100) {
            $props.FaultingApp = $props.FaultingApp.Substring(0, 100) + "..."
            Write-Log "Truncated FaultingApp name (too long)" -Level 'WARNING'
        }

        if ($props.FaultingModule -and $props.FaultingModule.Length -gt 100) {
            $props.FaultingModule = $props.FaultingModule.Substring(0, 100) + "..."
            Write-Log "Truncated FaultingModule name (too long)" -Level 'WARNING'
        }

        # Log the extracted details
        $detailsLog = "Event details - ID: $($props.EventID), Type: $($props.EventType), App: $($props.FaultingApp), Module: $($props.FaultingModule), Exception: $($props.ExceptionCode), PID: $($props.ProcessIdDecimal)"
        Write-Log $detailsLog -Level 'INFO'

        return [PSCustomObject]$props
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error formatting event details: $errorMsg" -Level 'ERROR'

        # Return a minimal object to prevent null reference exceptions
        return [PSCustomObject]@{
            EventID = $LogEvent.Id
            Source = $LogEvent.ProviderName
            Time = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            Computer = $env:COMPUTERNAME
            FaultingApp = $null
            FaultingModule = "Unknown"
            ExceptionCode = "Unknown"
            ProcessIdDecimal = $null
            EventType = "Unknown"
            Description = "Error processing event details"
            OSVersion = [Environment]::OSVersion.Version.ToString()
        }
    }
}

function Get-LastRunTime {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath,

        [Parameter(Mandatory = $true)]
        [int]$DefaultLookbackMinutes
    )

    try {
        # Check if state file exists and is readable
        if (Test-Path -Path $StateFilePath -ErrorAction SilentlyContinue) {
            try {
                $content = Get-Content $StateFilePath -Raw -ErrorAction Stop

                if (-not [string]::IsNullOrWhiteSpace($content)) {
                    $lastRunTime = [DateTime]::Parse($content)

                    # Validate the date is reasonable (not in the future, not too far in the past)
                    $now = Get-Date

                    if ($lastRunTime -gt $now) {
                        Write-Log "Last run time from state file is in the future, using default lookback" -Level 'WARNING'
                        return $now.AddMinutes(-$DefaultLookbackMinutes)
                    }

                    # If the last run time is more than 7 days ago, use the default lookback
                    if ($now.Subtract($lastRunTime).TotalDays -gt 7) {
                        Write-Log "Last run time from state file is more than 7 days old, using default lookback" -Level 'WARNING'
                        return $now.AddMinutes(-$DefaultLookbackMinutes)
                    }

                    Write-Log "Using last run time from state file: $($lastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
                    return $lastRunTime
                }
            }
            catch {
                Write-Log "Error parsing last run time from state file: $_" -Level 'WARNING'
            }
        }

        # If we get here, either the file doesn't exist or we couldn't parse the date
        $defaultTime = (Get-Date).AddMinutes(-$DefaultLookbackMinutes)
        Write-Log "Using default lookback time: $($defaultTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        return $defaultTime
    }
    catch {
        Write-Log "Error in Get-LastRunTime: $_" -Level 'ERROR'
        return (Get-Date).AddMinutes(-$DefaultLookbackMinutes)
    }
}

function Save-LastRunTime {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath,

        [Parameter(Mandatory = $true)]
        [DateTime]$LastRunTime
    )

    try {
        # Ensure directory exists
        $stateDir = Split-Path -Path $StateFilePath -Parent
        if (-not (Test-Path -Path $stateDir -ErrorAction SilentlyContinue)) {
            New-Item -Path $stateDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
            Write-Log "Created state file directory: $stateDir" -Level 'INFO'
        }

        # Create a temporary file first, then move it to avoid partial writes
        $tempFile = [System.IO.Path]::GetTempFileName()
        $LastRunTime.ToString('o') | Out-File -FilePath $tempFile -Force -ErrorAction Stop

        # Move the temp file to the final location
        Move-Item -Path $tempFile -Destination $StateFilePath -Force -ErrorAction Stop

        Write-Log "Saved last run time: $($LastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'
        return $true
    }
    catch {
        Write-Log "Error saving last run time: $_" -Level 'ERROR'
        return $false
    }
}

#endregion

#region Main

try {
    # Script start time for execution tracking
    $scriptStartTime = Get-Date
    $script:scriptVersion = "4.0"
    $script:LogLevel = $LogLevel  # Store the log level as a script variable

    # Display script banner
    Write-Log "CVDemoCrashAlert v$script:scriptVersion - CompleteView Services Crash Monitor" -Level 'INFO'
    Write-Log "=======================================================================" -Level 'INFO'
    Write-Log "Log level set to: $script:LogLevel" -Level 'INFO'

    # Test mode banner
    if ($TestMode) {
        Write-Log "RUNNING IN TEST MODE - No state will be saved" -Level 'WARNING'
    }

    # Initialize environment and get configuration
    $script:config = Initialize-Environment
    $LogPath = $script:config.LogPath
    $StateFilePath = $script:config.StateFilePath
    $serviceConfigs = $script:config.ServiceConfigs

    # Log the detected installation paths
    if ($script:config.InstallPaths) {
        Write-Log "CompleteView installation paths:" -Level 'INFO'
        foreach ($key in $script:config.InstallPaths.Keys | Sort-Object) {
            Write-Log "  ${key}: $($script:config.InstallPaths[$key])" -Level 'INFO'
        }
    }

    # Check if any services are being monitored
    if ($serviceConfigs.Count -eq 0) {
        Write-Log "No services selected for monitoring. Please enable at least one service." -Level 'WARNING'
        exit 0
    }

    # Log which services are being monitored
    $monitoredServices = $serviceConfigs.Keys -join ", "
    Write-Log "Monitoring the following services: $monitoredServices" -Level 'INFO'
    Write-Log "Log Path: $LogPath" -Level 'INFO'
    Write-Log "State File: $StateFilePath" -Level 'INFO'

    # Check if Slack notifications are disabled
    if ($NoSlack) {
        Write-Log "Slack notifications are disabled" -Level 'WARNING'
    }
    elseif ([string]::IsNullOrEmpty($SlackWebhookUrl)) {
        Write-Log "Slack webhook URL is not configured, notifications will be disabled" -Level 'WARNING'
        $NoSlack = $true
    }
    else {
        # Process any queued Slack messages from previous runs
        try {
            $queuedSent = Send-QueuedSlackMessages -MaxMessagesToProcess 10
            if ($queuedSent -gt 0) {
                Write-Log "Sent $queuedSent previously queued Slack messages at startup" -Level 'INFO'
            }
        }
        catch {
            Write-Log "Error processing queued Slack messages at startup: $($_.Exception.Message)" -Level 'WARNING'
        }
    }

    # Perform health monitoring checks
    Write-Log "Starting health monitoring checks" -Level 'INFO'

    # Check service status
    Test-ServiceStatus -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Check resource usage
    Measure-ResourceUsage -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Check disk space
    Test-DiskSpace -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack

    # Check database connection (only for Management Server)
    if ($MonitorManagementServer) {
        Write-Log "Testing database connection with enhanced fallback options" -Level 'INFO'

        # Build parameters for database connection test
        $dbParams = @{
            WebhookUrl = $SlackWebhookUrl
            NoSlack = $NoSlack
            QueueOnFailure = $true
        }

        # Add optional parameters if specified
        if ($SkipSqlModuleCheck) {
            $dbParams.SkipModuleCheck = $true
            Write-Log "SQL Server PowerShell module check will be skipped" -Level 'INFO'
        }

        if ($SkipOdbcFallback) {
            $dbParams.SkipOdbcFallback = $true
            Write-Log "ODBC fallback will be skipped" -Level 'INFO'
        }

        if ($SkipTcpFallback) {
            $dbParams.SkipTcpFallback = $true
            Write-Log "TCP connectivity fallback will be skipped" -Level 'INFO'
        }

        # Use custom connection string if provided
        if (-not [string]::IsNullOrEmpty($SqlConnectionString)) {
            $dbParams.ConnectionString = $SqlConnectionString
            Write-Log "Using custom SQL connection string" -Level 'INFO'
        }

        # Use enhanced database connection function with configured options
        Test-DatabaseConnection @dbParams
    }

    # Analyze log files
    Search-LogFiles -ServiceConfigs $serviceConfigs -WebhookUrl $SlackWebhookUrl -NoSlack:$NoSlack -LookbackMinutes $LookbackMinutes

    # Get last run time and crash events
    $lastRunTime = Get-LastRunTime -StateFilePath $StateFilePath -DefaultLookbackMinutes $LookbackMinutes
    Write-Log "Looking for events since $($lastRunTime.ToString('yyyy-MM-dd HH:mm:ss'))" -Level 'INFO'

    # If in test mode, we can simulate a crash event for each service
    if ($TestMode) {
        Write-Log "Test mode: Simulating crash events for monitored services" -Level 'INFO'

        # Process each service in test mode
        foreach ($serviceName in $serviceConfigs.Keys) {
            $serviceConfig = $serviceConfigs[$serviceName]
            $appName = $serviceConfig.AppName
            $dumpFolder = $serviceConfig.DumpPath
            $friendlyName = $serviceConfig.FriendlyName

            Write-Log "Test mode: Simulating crash event for $friendlyName ($appName)" -Level 'INFO'

            # Create a simulated event details object
            $simulatedDetails = [PSCustomObject]@{
                EventID = 1000
                Source = "Application Error"
                Time = (Get-Date).AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss")
                Computer = $env:COMPUTERNAME
                FaultingApp = $appName
                FaultingModule = "unknown.dll"
                ExceptionCode = "0xc0000005"
                ProcessIdDecimal = 4660  # 0x1234 in decimal
            }

            # Process the simulated event
            try {
                $hostname = $env:COMPUTERNAME
                $time = $simulatedDetails.Time
                $faultingModule = $simulatedDetails.FaultingModule
                $exceptionCode = $simulatedDetails.ExceptionCode

                # Create dump file name
                $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $simulatedDetails.ProcessIdDecimal
                $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile

                # In test mode, we'll say the dump doesn't exist initially
                $dumpExistsInitially = $false

                # Send initial notification if Slack is enabled
                if (-not $NoSlack) {
                    Write-Log "Sending initial Slack notification for simulated crash of $friendlyName" -Level 'INFO'
                    Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                    Write-Log "Sent initial Slack notification for simulated crash at $time" -Level 'INFO'
                }
                else {
                    Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
                }

                # Simulate waiting for dump file
                Write-Log "Simulating wait for dump file..." -Level 'INFO'
                Start-Sleep -Seconds 2

                # In test mode, we'll say the dump becomes available
                $dumpAvailable = $true

                # Send follow-up notification if Slack is enabled
                if (-not $NoSlack) {
                    Write-Log "Sending follow-up Slack notification for simulated crash" -Level 'INFO'
                    Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false
                    Write-Log "Sent second Slack notification for simulated dump availability at $time" -Level 'INFO'
                }
                else {
                    Write-Log "Dump file is now available at: $fullDumpPath (simulated)" -Level 'INFO'
                }
            }
            catch {
                Write-Log "Error processing simulated event for ${friendlyName}: $($_.Exception.Message)" -Level 'ERROR'
            }
        }

        # Skip the normal event processing
        $events = $null
    }
    else {
        # Get real events
        $appNames = $serviceConfigs.Values | ForEach-Object { $_.AppName }
        $events = Get-CrashEvents -StartTime $lastRunTime -AppNames $appNames
    }

    if ($null -eq $events -or $events.Count -eq 0) {
        Write-Log "No events found in the specified time range" -Level 'INFO'
    }
    else {
        Write-Log "Found $($events.Count) event(s) to process" -Level 'INFO'

        foreach ($eventItem in $events) {
            try {
                $details = Format-EventDetails -LogEvent $eventItem

                # Skip if we couldn't extract the faulting app
                if (-not $details.FaultingApp) {
                    Write-Log "Skipping event - could not determine faulting application" -Level 'WARNING'
                    continue
                }

                # Check if the faulting app matches any of our monitored services
                $matchedService = $null

                foreach ($serviceName in $serviceConfigs.Keys) {
                    $serviceConfig = $serviceConfigs[$serviceName]
                    if ($details.FaultingApp -eq $serviceConfig.AppName) {
                        $matchedService = $serviceConfig
                        break
                    }
                }

                # If we found a matching service, process the event
                if ($matchedService) {
                    $appName = $matchedService.AppName
                    $friendlyName = $matchedService.FriendlyName
                    $dumpFolder = $matchedService.DumpPath

                    Write-Log "Processing crash event for $friendlyName ($appName)" -Level 'INFO'

                    $hostname = $env:COMPUTERNAME
                    $time = $details.Time
                    $faultingModule = if ($null -eq $details.FaultingModule) { "Unknown" } else { $details.FaultingModule }
                    $exceptionCode = if ($null -eq $details.ExceptionCode) { "Unknown" } else { $details.ExceptionCode }

                    # Ensure dump directory exists
                    if (-not (Test-Path -Path $dumpFolder -ErrorAction SilentlyContinue)) {
                        try {
                            New-Item -Path $dumpFolder -ItemType Directory -Force -ErrorAction Stop | Out-Null
                            Write-Log "Created dump directory for $friendlyName - $dumpFolder" -Level 'INFO'
                        }
                        catch {
                            Write-Log "Failed to create dump directory for ${friendlyName}: $($_.Exception.Message)" -Level 'ERROR'
                        }
                    }

                    # Ensure we have a valid process ID
                    if (-not $details.ProcessIdDecimal) {
                        Write-Log "Could not determine process ID from event, using timestamp instead" -Level 'WARNING'
                        $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), (Get-Date).ToString("yyyyMMddHHmmss")
                    }
                    else {
                        $dumpFile = "{0}.{1}.dmp" -f ($appName -replace '\.exe$', ''), $details.ProcessIdDecimal
                    }

                    $fullDumpPath = Join-Path -Path $dumpFolder -ChildPath $dumpFile

                    # Check if dump file exists
                    $dumpExistsInitially = Test-Path -Path $fullDumpPath

                    # Send initial notification if Slack is enabled
                    if (-not $NoSlack) {
                        try {
                            Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpExistsInitially -InitialAlert:$true
                            Write-Log "Sent initial Slack notification for $friendlyName crash at $time" -Level 'INFO'
                        }
                        catch {
                            Write-Log "Failed to send initial Slack notification for $friendlyName after retries: $_" -Level 'ERROR'
                            # Continue execution - we'll try again with the follow-up notification
                        }
                    }
                    else {
                        Write-Log "Slack notifications disabled - skipping initial notification" -Level 'INFO'
                    }

                    # If dump doesn't exist initially, wait for it
                    if (-not $dumpExistsInitially) {
                        $dumpAvailable = Wait-ForDumpFile -DumpPath $fullDumpPath -TimeoutMinutes $DumpWaitTimeoutMinutes

                        # Send follow-up notification if Slack is enabled
                        if (-not $NoSlack) {
                            try {
                                Send-SlackNotification -AppName $appName -Hostname $hostname -CrashTime $time -FaultingModule $faultingModule -ExceptionCode $exceptionCode -DumpPath $fullDumpPath -WebhookUrl $SlackWebhookUrl -DumpAvailable:$dumpAvailable -InitialAlert:$false

                                if ($dumpAvailable) {
                                    Write-Log "Sent second Slack notification for $friendlyName dump availability at $time" -Level 'INFO'
                                }
                                else {
                                    Write-Log "Sent notification that $friendlyName dump did not appear after timeout at $time" -Level 'WARNING'
                                }
                            }
                            catch {
                                Write-Log "Failed to send follow-up Slack notification for $friendlyName after retries: $_" -Level 'ERROR'
                            }
                        }
                        else {
                            if ($dumpAvailable) {
                                Write-Log "Dump file for $friendlyName is now available at: $fullDumpPath" -Level 'INFO'
                            }
                            else {
                                Write-Log "Dump file for $friendlyName did not appear after waiting $DumpWaitTimeoutMinutes minutes" -Level 'WARNING'
                            }
                        }
                    }
                }
                else {
                    # This crash is not for one of our monitored services
                    Write-Log "Skipping event - faulting application '$($details.FaultingApp)' is not in the monitored services list" -Level 'DEBUG'
                }
            }
            catch {
                Write-Log "Error processing event: $_" -Level 'ERROR'
                # Continue with next event even if this one fails
                continue
            }
        }
    }

    # Save the last run time (unless in test mode)
    if (-not $TestMode) {
        try {
            Save-LastRunTime -StateFilePath $StateFilePath -LastRunTime (Get-Date)
        }
        catch {
            Write-Log "Failed to save last run time: $_" -Level 'ERROR'
        }
    }
    else {
        Write-Log "Test mode: Skipping save of last run time" -Level 'INFO'
    }

    # Calculate and log execution time
    $executionTime = (Get-Date) - $scriptStartTime
    Write-Log "Crash monitoring completed in $($executionTime.TotalSeconds.ToString('0.00')) seconds" -Level 'INFO'
}
catch {
    # Global error handler
    Write-Log "Critical error in script execution: $_" -Level 'ERROR'

    # Try to send an alert about the script failure if possible
    if (-not $NoSlack -and -not [string]::IsNullOrEmpty($SlackWebhookUrl)) {
        try {
            $errorText = ":x: *CVDemoCrashAlert Script Error*
The crash monitoring script encountered a critical error on $env:COMPUTERNAME at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            # Use our enhanced Slack message function with queuing capability
            Send-SlackMessage -WebhookUrl $SlackWebhookUrl -MessageText $errorText -QueueOnFailure -ErrorAction SilentlyContinue
            Write-Host "Sent error notification to Slack" -ForegroundColor Yellow
        }
        catch {
            # Try to queue the message directly if Send-SlackMessage fails completely
            try {
                Add-SlackMessageToQueue -WebhookUrl $SlackWebhookUrl -MessageText $errorText
                Write-Host "Queued error notification for later delivery" -ForegroundColor Yellow
            }
            catch {
                Write-Host "Could not send or queue error notification to Slack: $_" -ForegroundColor Red
            }
        }
    }

    # Exit with non-zero code to indicate failure
    if (-not $TestMode) {
        exit 1
    }
}
finally {
    # This block always executes, even if there are errors
    $executionTime = (Get-Date) - $scriptStartTime
    Write-Host "Script execution finished in $($executionTime.TotalSeconds.ToString('0.00')) seconds"
}

#endregion