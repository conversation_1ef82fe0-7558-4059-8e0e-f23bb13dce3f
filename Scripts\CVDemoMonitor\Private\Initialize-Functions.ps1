# Initialize-Functions.ps1
# Contains functions related to initialization and environment setup

function Initialize-Environment {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [hashtable]$InstallPaths = $null,

        [Parameter(Mandatory = $false)]
        [string]$ConfigFilePath = "",

        [Parameter(Mandatory = $false)]
        [switch]$UseEnvironmentVariables,

        [Parameter(Mandatory = $false)]
        [switch]$ForceRegistryLookup,

        [Parameter(Mandatory = $false)]
        [string]$InstallBasePath = "",

        [Parameter(Mandatory = $false)]
        [string]$LogPath = "",

        [Parameter(Mandatory = $false)]
        [string]$StateFilePath = "",

        [Parameter(Mandatory = $false)]
        [switch]$MonitorRecordingServer,

        [Parameter(Mandatory = $false)]
        [switch]$MonitorAdminService,

        [Parameter(Mandatory = $false)]
        [switch]$MonitorManagementServer
    )

    try {
        # If no install paths were provided, find them
        if ($null -eq $InstallPaths) {
            Write-Log "Finding CompleteView installation paths" -Level 'INFO'

            # Use the Find-InstallPath function with the provided parameters
            $pathParams = @{}

            if (-not [string]::IsNullOrEmpty($ConfigFilePath)) {
                $pathParams.ConfigFilePath = $ConfigFilePath
            }

            if ($UseEnvironmentVariables) {
                $pathParams.UseEnvironmentVariables = $true
            }

            if ($ForceRegistryLookup) {
                $pathParams.ForceRegistryLookup = $true
            }

            $InstallPaths = Find-InstallPath @pathParams
        }

        # Get the crash dumps path (the most important one for our purposes)
        $crashDumpsPath = $InstallPaths.CrashDumps

        # Override with explicit install base path if provided
        if (-not [string]::IsNullOrEmpty($InstallBasePath)) {
            Write-Log "Using explicitly provided install base path: $InstallBasePath" -Level 'INFO'

            if (Test-Path $InstallBasePath) {
                # Update the install paths with the provided base path
                $InstallPaths.ProgramFiles = $InstallBasePath

                # Try to infer ProgramData path
                $inferredProgramData = "C:\ProgramData\Salient Security Platform"
                if (Test-Path $inferredProgramData) {
                    $InstallPaths.ProgramData = $inferredProgramData
                    $crashDumpsPath = Join-Path -Path $inferredProgramData -ChildPath "CrashDumps"
                    $InstallPaths.CrashDumps = $crashDumpsPath
                }

                # Update component paths
                $cvBasePath = Join-Path -Path $InstallBasePath -ChildPath "CompleteView"
                if (Test-Path $cvBasePath) {
                    $rsPath = Join-Path -Path $cvBasePath -ChildPath "Recording Server"
                    if (Test-Path $rsPath) {
                        $InstallPaths.RecordingServer = $rsPath
                        $InstallPaths.AdminService = $rsPath
                    }

                    $msPath = Join-Path -Path $cvBasePath -ChildPath "Management Server"
                    if (Test-Path $msPath) {
                        $InstallPaths.ManagementServer = $msPath
                    }
                }
            }
            else {
                Write-Log "Provided install base path does not exist: $InstallBasePath" -Level 'WARNING'
            }
        }

        # Validate and set state file path
        if (-not $StateFilePath) {
            $StateFilePath = Join-Path -Path $crashDumpsPath -ChildPath "CVCrashMonitor.state"
            Write-Log "Using default state file path: $StateFilePath" -Level 'INFO'
        }
        else {
            Write-Log "Using provided state file path: $StateFilePath" -Level 'INFO'
        }

        # Validate and set log path
        if (-not $LogPath) {
            $LogPath = Join-Path -Path $crashDumpsPath -ChildPath "CVCrashMonitor.log"
            Write-Log "Using default log path: $LogPath" -Level 'INFO'
        }
        else {
            Write-Log "Using provided log path: $LogPath" -Level 'INFO'
        }

        # Ensure log directory exists
        $logDir = Split-Path -Path $LogPath -Parent
        if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
            try {
                New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                Write-Log "Created log directory: $logDir" -Level 'INFO'
            }
            catch {
                # If we can't create the directory, try to use a fallback location
                Write-Log "Failed to create log directory: $($_.Exception.Message)" -Level 'ERROR'
                $tempPath = [System.IO.Path]::GetTempPath()
                $LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
                Write-Log "Using fallback log path: $LogPath" -Level 'WARNING'
            }
        }

        # Check if we can write to the log file
        try {
            $testContent = "Test log write"
            $testContent | Out-File -FilePath $LogPath -Append -ErrorAction Stop
            Write-Log "Verified log file is writable" -Level 'INFO'
        }
        catch {
            Write-Log "Cannot write to log file: $($_.Exception.Message)" -Level 'ERROR'
            $tempPath = [System.IO.Path]::GetTempPath()
            $LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
            Write-Log "Using fallback log path: $LogPath" -Level 'WARNING'

            # Test the fallback path
            try {
                $testContent | Out-File -FilePath $LogPath -Append -ErrorAction Stop
            }
            catch {
                Write-Log "Cannot write to fallback log file either: $($_.Exception.Message)" -Level 'ERROR'
                # At this point, we'll just use console logging
            }
        }

        # Check if we can write to the state file directory
        $stateDir = Split-Path -Path $StateFilePath -Parent
        if (-not (Test-Path -Path $stateDir -ErrorAction SilentlyContinue)) {
            try {
                New-Item -Path $stateDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                Write-Log "Created state file directory: $stateDir" -Level 'INFO'
            }
            catch {
                Write-Log "Failed to create state file directory: $($_.Exception.Message)" -Level 'ERROR'
                $tempPath = [System.IO.Path]::GetTempPath()
                $StateFilePath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.state"
                Write-Log "Using fallback state file path: $StateFilePath" -Level 'WARNING'
            }
        }

        # Define service configurations
        $serviceConfigs = @{}

        # Recording Server configuration
        if ($MonitorRecordingServer) {
            $recordingServerPath = $InstallPaths.RecordingServer
            $recordingServerExe = Join-Path -Path $recordingServerPath -ChildPath "RecordingServer64.exe"

            $recordingServerConfig = @{
                AppName = "RecordingServer64.exe"
                DumpPath = Join-Path -Path $crashDumpsPath -ChildPath "RecordingServer"
                InstallPath = $recordingServerExe
                FriendlyName = "CompleteView Recording Server"
            }
            $serviceConfigs["RecordingServer"] = $recordingServerConfig
        }

        # Administrative Service configuration
        if ($MonitorAdminService) {
            $adminServicePath = $InstallPaths.AdminService
            $adminServiceExe = Join-Path -Path $adminServicePath -ChildPath "AdminService64.exe"

            $adminServiceConfig = @{
                AppName = "AdminService64.exe"
                DumpPath = Join-Path -Path $crashDumpsPath -ChildPath "AdminService"
                InstallPath = $adminServiceExe
                FriendlyName = "CompleteView Administrative Service"
            }
            $serviceConfigs["AdminService"] = $adminServiceConfig
        }

        # Management Server configuration
        if ($MonitorManagementServer) {
            $managementServerPath = $InstallPaths.ManagementServer
            $managementServerExe = Join-Path -Path $managementServerPath -ChildPath "ManagementServer.exe"

            $managementServerConfig = @{
                AppName = "ManagementServer.exe"
                DumpPath = Join-Path -Path $crashDumpsPath -ChildPath "ManagementServer"
                InstallPath = $managementServerExe
                FriendlyName = "CompleteView Management Server"
            }
            $serviceConfigs["ManagementServer"] = $managementServerConfig
        }

        # Ensure dump directories exist for each service
        foreach ($serviceName in $serviceConfigs.Keys) {
            $config = $serviceConfigs[$serviceName]
            $dumpDir = $config.DumpPath

            if (-not (Test-Path -Path $dumpDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $dumpDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created dump directory for $($config.FriendlyName): $dumpDir" -Level 'INFO'
                }
                catch {
                    Write-Log "Failed to create dump directory for $($config.FriendlyName): $($_.Exception.Message)" -Level 'ERROR'
                }
            }

            # Verify executable exists
            if (-not (Test-Path -Path $config.InstallPath -ErrorAction SilentlyContinue)) {
                Write-Log "Warning: Executable for $($config.FriendlyName) not found at $($config.InstallPath)" -Level 'WARNING'
            }
        }

        # Return the configuration
        return @{
            LogPath = $LogPath
            StateFilePath = $StateFilePath
            BaseDirectory = $crashDumpsPath
            ServiceConfigs = $serviceConfigs
            InstallPaths = $InstallPaths
        }
    }
    catch {
        Write-Log "Critical error in Initialize-Environment: $($_.Exception.Message)" -Level 'ERROR'

        # Use temp directory as last resort
        $tempPath = [System.IO.Path]::GetTempPath()
        return @{
            LogPath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.log"
            StateFilePath = Join-Path -Path $tempPath -ChildPath "CVCrashMonitor.state"
            BaseDirectory = $tempPath
            ServiceConfigs = @{}
            InstallPaths = $InstallPaths
        }
    }
}
