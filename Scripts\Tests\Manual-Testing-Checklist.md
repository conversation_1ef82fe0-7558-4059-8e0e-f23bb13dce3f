# AutoCertLE Manual Testing Checklist

## Prerequisites
- [ ] Ensure you have a domain you can use for testing
- [ ] Ensure you have access to modify DNS records for the domain
- [ ] Ensure you have a test environment with Salient CompleteView installed (if testing Salient-specific features)

## Basic Functionality

### Module Loading
- [ ] Import the module without errors
- [ ] Check that all expected functions are exported

### Certificate Registration
- [ ] Register a new certificate with manual DNS validation
- [ ] Register a new certificate with automatic DNS validation (if you have credentials for a supported provider)
- [ ] Register a wildcard certificate
- [ ] Verify that the certificate is properly stored

### Certificate Management
- [ ] List existing certificates
- [ ] View certificate details
- [ ] Revoke a certificate
- [ ] Delete a certificate

### DNS Provider Detection
- [ ] Test DNS provider detection with a Cloudflare-hosted domain
- [ ] Test DNS provider detection with an AWS Route53-hosted domain
- [ ] Test DNS provider detection with a domain hosted by another provider
- [ ] Verify that the correct plugin is suggested

## Salient CompleteView Specific Features

### Server Type Detection
- [ ] Test server type detection on a Management Server
- [ ] Test server type detection on a Recording Server
- [ ] Test server type detection on a server with both roles

### Certificate Installation
- [ ] Install a certificate to a Management Server
- [ ] Verify the certificate is properly installed in the Windows certificate store
- [ ] Install a certificate to a Recording Server
- [ ] Verify the certificate files are created with the correct naming convention
- [ ] Verify the certificate number increments correctly

### Certificate Distribution
- [ ] Distribute a certificate to multiple servers using file shares
- [ ] Distribute a certificate to multiple servers using WinRM
- [ ] Verify the certificate is properly installed on all target servers

## Advanced Features

### Automatic Renewal
- [ ] Configure automatic renewal for a certificate
- [ ] Verify that the renewal task is created
- [ ] Test the renewal process

### Certificate Monitoring
- [ ] Generate a certificate monitoring report
- [ ] Verify that expiring certificates are properly flagged
- [ ] Test the monitoring dashboard

### Security Features
- [ ] Test secure credential storage
- [ ] Test certificate key security validation
- [ ] Test ACME account key backup and recovery

## Error Handling and Edge Cases

### Network Issues
- [ ] Test behavior when DNS servers are unreachable
- [ ] Test behavior when Let's Encrypt servers are unreachable
- [ ] Test behavior when target servers are unreachable

### Permission Issues
- [ ] Test behavior with insufficient permissions to modify DNS records
- [ ] Test behavior with insufficient permissions to install certificates
- [ ] Test behavior with insufficient permissions to create scheduled tasks

### Invalid Inputs
- [ ] Test with invalid domain names
- [ ] Test with invalid email addresses
- [ ] Test with invalid plugin parameters

## Performance and Reliability

### Performance
- [ ] Measure the time taken to register a certificate
- [ ] Measure the time taken to install a certificate
- [ ] Measure the time taken to distribute a certificate to multiple servers

### Reliability
- [ ] Test the module with multiple consecutive operations
- [ ] Test the module with concurrent operations
- [ ] Test the module with large numbers of certificates

## User Experience

### Menu System
- [ ] Navigate through all menu options
- [ ] Verify that all menu options work as expected
- [ ] Verify that the menu system is intuitive and user-friendly

### Help and Documentation
- [ ] Check that all functions have proper help documentation
- [ ] Verify that error messages are clear and helpful
- [ ] Test the built-in guidance for DNS provider setup

## Notes
- Record any issues or unexpected behavior
- Note any suggestions for improvement
- Document any workarounds for known issues
