<#
.SYNOPSIS
    Displays the settings menu.

.DESCRIPTION
    Shows the settings menu for configuring module preferences.

.EXAMPLE
    Show-SettingsMenu
#>
function Show-SettingsMenu {
    [CmdletBinding()]
    param ()

    # Load settings
    $Settings = Get-ScriptSettings

    while ($true) {
        Clear-Host
        Write-Host "=== Settings Menu ===`n"

        Write-Host "Basic Settings:" -ForegroundColor Cyan
        Write-Host "1) Set default DNS plugin (current: $($Settings.DefaultDNSPlugin))"
        Write-Host "2) Set default certificate path (current: $($Settings.DefaultCertPath))"
        Write-Host "3) Set default PEM save location (current: $($Settings.DefaultPEMLocation))"
        Write-Host "4) Set default PFX save location (current: $($Settings.DefaultPFXLocation))"

        Write-Host "`nRenewal Settings:" -ForegroundColor Cyan
        Write-Host "5) Configure automatic renewal (threshold: $($Settings.RenewalThresholdDays) days)"
        Write-Host "6) Configure auto-installation (enabled: $($Settings.AutoInstallRenewedCertificates))"
        Write-Host "7) Configure email notifications"
        Write-Host "8) Configure renewal hooks"

        Write-Host "`nMonitoring Settings:" -ForegroundColor Cyan
        Write-Host "9) Configure certificate monitoring (enabled: $($Settings.EnableExpirationMonitoring))"

        Write-Host "`n0) Back"

        $settingsChoice = Read-Host "`nEnter your choice (0-9)"
        switch ($settingsChoice) {
            '0' { return }
            '1' {
                $plugin = Read-Host "Enter default DNS plugin (Enter to keep current)"
                if ($plugin) { $Settings.DefaultDNSPlugin = $plugin }
            }
            '2' {
                $path = Read-Host "Enter default certificate path (Enter to keep current)"
                if ($path) { $Settings.DefaultCertPath = $path }
            }
            '3' {
                $path = Read-Host "Enter default PEM save location (Enter to keep current)"
                if ($path) { $Settings.DefaultPEMLocation = $path }
            }
            '4' {
                $path = Read-Host "Enter default PFX save location (Enter to keep current)"
                if ($path) { $Settings.DefaultPFXLocation = $path }
            }
            '5' {
                # Configure automatic renewal
                $threshold = Read-Host "Enter renewal threshold in days (current: $($Settings.RenewalThresholdDays))"
                if ($threshold -match '^\d+$') {
                    $Settings.RenewalThresholdDays = [int]$threshold
                }
            }
            '6' {
                # Configure auto-installation
                Write-Host "`nAuto-Installation Settings:" -ForegroundColor Cyan
                Write-Host "1) Enable/disable auto-installation (current: $($Settings.AutoInstallRenewedCertificates))"
                Write-Host "2) Set preferred installation location (current: $($Settings.PreferredInstallLocation))"
                Write-Host "3) Enable/disable service restart (current: $($Settings.RestartServicesAfterRenewal))"
                Write-Host "0) Back"

                $autoInstallChoice = Read-Host "`nEnter your choice (0-3)"
                switch ($autoInstallChoice) {
                    '0' { }
                    '1' {
                        $enable = Read-Host "Enable auto-installation? (Y/N)"
                        $Settings.AutoInstallRenewedCertificates = $enable -match '^[Yy]$'
                    }
                    '2' {
                        Write-Host "`nAvailable installation locations:"
                        Write-Host "1) Local Certificate Store (LocalStore)"
                        Write-Host "2) Recording Server (RecordingServer)"
                        Write-Host "3) Management Server (ManagementServer)"
                        Write-Host "4) Nginx Web Server (Nginx)"
                        Write-Host "5) Apache Web Server (Apache)"

                        $locationChoice = Read-Host "`nEnter your choice (1-5)"
                        switch ($locationChoice) {
                            '1' { $Settings.PreferredInstallLocation = 'LocalStore' }
                            '2' { $Settings.PreferredInstallLocation = 'RecordingServer' }
                            '3' { $Settings.PreferredInstallLocation = 'ManagementServer' }
                            '4' { $Settings.PreferredInstallLocation = 'Nginx' }
                            '5' { $Settings.PreferredInstallLocation = 'Apache' }
                        }
                    }
                    '3' {
                        $restart = Read-Host "Enable service restart after renewal? (Y/N)"
                        $Settings.RestartServicesAfterRenewal = $restart -match '^[Yy]$'
                    }
                }
            }
            '7' {
                # Configure email notifications
                Set-EmailNotificationSettings
            }
            '8' {
                # Configure renewal hooks
                Set-RenewalHooks
            }
            '9' {
                # Configure certificate monitoring
                Write-Host "`nCertificate Monitoring Settings:" -ForegroundColor Cyan
                Write-Host "1) Enable/disable monitoring (current: $($Settings.EnableExpirationMonitoring))"
                Write-Host "2) Set warning threshold (current: $($Settings.ExpirationWarningThresholdDays) days)"
                Write-Host "3) Set critical threshold (current: $($Settings.ExpirationCriticalThresholdDays) days)"
                Write-Host "0) Back"

                $monitoringChoice = Read-Host "`nEnter your choice (0-3)"
                switch ($monitoringChoice) {
                    '0' { }
                    '1' {
                        $enable = Read-Host "Enable certificate monitoring? (Y/N)"
                        $Settings.EnableExpirationMonitoring = $enable -match '^[Yy]$'
                    }
                    '2' {
                        $threshold = Read-Host "Enter warning threshold in days (current: $($Settings.ExpirationWarningThresholdDays))"
                        if ($threshold -match '^\d+$') {
                            $Settings.ExpirationWarningThresholdDays = [int]$threshold
                        }
                    }
                    '3' {
                        $threshold = Read-Host "Enter critical threshold in days (current: $($Settings.ExpirationCriticalThresholdDays))"
                        if ($threshold -match '^\d+$') {
                            $Settings.ExpirationCriticalThresholdDays = [int]$threshold
                        }
                    }
                }
            }
        }

        # Save settings
        Save-ScriptSettings -Settings $Settings
        Write-Host "`nSettings saved." -ForegroundColor Green
        Read-Host "`nPress Enter to return to the settings menu"
    }
}
