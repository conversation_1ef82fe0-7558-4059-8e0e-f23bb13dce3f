# Install-PathFunctions.ps1
# Contains functions related to finding and validating installation paths

function Find-InstallPath {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConfigFilePath = "",

        [Parameter(Mandatory = $false)]
        [switch]$UseEnvironmentVariables,

        [Parameter(Mandatory = $false)]
        [switch]$ForceRegistryLookup
    )

    try {
        # Initialize paths with defaults
        $paths = @{
            ProgramFiles = "C:\Program Files\Salient Security Platform"
            ProgramData = "C:\ProgramData\Salient Security Platform"
            RecordingServer = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            AdminService = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            ManagementServer = "C:\Program Files\Salient Security Platform\CompleteView\Management Server"
            LogsFolder = "C:\ProgramData\Salient Security Platform\Logs"
            CrashDumps = "C:\ProgramData\Salient Security Platform\CrashDumps"
        }

        # Step 1: Check for config file overrides
        if (-not [string]::IsNullOrEmpty($ConfigFilePath) -and (Test-Path $ConfigFilePath)) {
            try {
                Write-Log "Reading path configuration from $ConfigFilePath" -Level 'INFO'
                $configFileContent = Get-Content -Path $ConfigFilePath -Raw -ErrorAction Stop

                # Determine file type by extension
                $extension = [System.IO.Path]::GetExtension($ConfigFilePath).ToLower()

                switch ($extension) {
                    ".json" {
                        $config = $configFileContent | ConvertFrom-Json -ErrorAction Stop

                        # Update paths from JSON
                        if ($config.Paths) {
                            foreach ($key in $config.Paths.PSObject.Properties.Name) {
                                if ($paths.ContainsKey($key)) {
                                    $paths[$key] = $config.Paths.$key
                                    Write-Log "Config override for ${key}: $($paths[$key])" -Level 'DEBUG'
                                }
                            }
                        }
                    }
                    ".xml" {
                        [xml]$config = $configFileContent

                        # Update paths from XML
                        if ($config.Configuration.Paths) {
                            foreach ($path in $config.Configuration.Paths.Path) {
                                if ($paths.ContainsKey($path.Name)) {
                                    $paths[$path.Name] = $path.Value
                                    Write-Log "Config override for $($path.Name): $($paths[$path.Name])" -Level 'DEBUG'
                                }
                            }
                        }
                    }
                    ".ini" {
                        # Simple INI parser
                        $inPaths = $false
                        foreach ($line in $configFileContent -split "`r`n|`n") {
                            $line = $line.Trim()

                            # Skip comments and empty lines
                            if ($line -match "^[;#]" -or [string]::IsNullOrWhiteSpace($line)) {
                                continue
                            }

                            # Check for section
                            if ($line -match "^\[(.+)\]$") {
                                $section = $Matches[1].Trim()
                                $inPaths = ($section -eq "Paths")
                                continue
                            }

                            # Process key-value pairs in Paths section
                            if ($inPaths -and $line -match "^([^=]+)=(.*)$") {
                                $key = $Matches[1].Trim()
                                $value = $Matches[2].Trim()

                                if ($paths.ContainsKey($key)) {
                                    $paths[$key] = $value
                                    Write-Log "Config override for ${key}: $($paths[$key])" -Level 'DEBUG'
                                }
                            }
                        }
                    }
                    default {
                        Write-Log "Unsupported config file format: $extension" -Level 'WARNING'
                    }
                }
            }
            catch {
                Write-Log "Error reading config file: $_" -Level 'ERROR'
                # Continue with defaults or other methods
            }
        }

        # Step 2: Check for environment variable overrides
        if ($UseEnvironmentVariables) {
            Write-Log "Checking environment variables for path overrides" -Level 'DEBUG'

            $envVarPrefix = "COMPLETEVIEW_"

            $envMappings = @{
                "PROGRAM_FILES" = "ProgramFiles"
                "PROGRAM_DATA" = "ProgramData"
                "RECORDING_SERVER" = "RecordingServer"
                "ADMIN_SERVICE" = "AdminService"
                "MANAGEMENT_SERVER" = "ManagementServer"
                "LOGS_FOLDER" = "LogsFolder"
                "CRASH_DUMPS" = "CrashDumps"
            }

            foreach ($envKey in $envMappings.Keys) {
                $fullEnvVar = $envVarPrefix + $envKey
                $value = [Environment]::GetEnvironmentVariable($fullEnvVar)

                if (-not [string]::IsNullOrEmpty($value)) {
                    $pathKey = $envMappings[$envKey]
                    $paths[$pathKey] = $value
                    Write-Log "Environment variable override for ${pathKey}: $($paths[$pathKey])" -Level 'DEBUG'
                }
            }
        }

        # Step 3: Registry lookup for installation paths
        if ($ForceRegistryLookup -or (-not (Test-Path $paths.ProgramFiles)) -or (-not (Test-Path $paths.ProgramData))) {
            Write-Log "Performing registry lookup for CompleteView installation paths" -Level 'INFO'

            # Common registry paths for installed software
            $registryPaths = @(
                "HKLM:\SOFTWARE\Salient\CompleteView",
                "HKLM:\SOFTWARE\WOW6432Node\Salient\CompleteView",
                "HKLM:\SOFTWARE\Salient Systems\CompleteView",
                "HKLM:\SOFTWARE\WOW6432Node\Salient Systems\CompleteView"
            )

            $installPathFound = $false

            foreach ($regPath in $registryPaths) {
                if (Test-Path $regPath) {
                    try {
                        $installDir = Get-ItemProperty -Path $regPath -Name "InstallDir" -ErrorAction SilentlyContinue

                        if ($installDir -and $installDir.InstallDir) {
                            $basePath = $installDir.InstallDir

                            # Verify the path exists
                            if (Test-Path $basePath) {
                                Write-Log "Found CompleteView installation at: $basePath" -Level 'INFO'

                                # Update program files path
                                $paths.ProgramFiles = Split-Path -Path $basePath -Parent

                                # Update component paths based on the discovered installation
                                $paths.RecordingServer = Join-Path -Path $basePath -ChildPath "Recording Server"
                                $paths.ManagementServer = Join-Path -Path $basePath -ChildPath "Management Server"

                                # AdminService is typically in the Recording Server directory
                                $paths.AdminService = $paths.RecordingServer

                                $installPathFound = $true
                                break
                            }
                        }
                    }
                    catch {
                        Write-Log "Error reading registry key ${regPath}: $($_.Exception.Message)" -Level 'WARNING'
                    }
                }
            }

            # If we found the program files path but not program data, try to infer it
            if ($installPathFound) {
                # Program data is typically under C:\ProgramData with a similar structure
                $inferredProgramData = "C:\ProgramData\Salient Security Platform"

                if (Test-Path $inferredProgramData) {
                    $paths.ProgramData = $inferredProgramData
                    $paths.LogsFolder = Join-Path -Path $inferredProgramData -ChildPath "Logs"
                    $paths.CrashDumps = Join-Path -Path $inferredProgramData -ChildPath "CrashDumps"
                }
            }
        }

        # Return the resolved paths
        return $paths
    }
    catch {
        Write-Log "Error in Find-InstallPath: $_" -Level 'ERROR'

        # Return default paths as fallback
        return @{
            ProgramFiles = "C:\Program Files\Salient Security Platform"
            ProgramData = "C:\ProgramData\Salient Security Platform"
            RecordingServer = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            AdminService = "C:\Program Files\Salient Security Platform\CompleteView\Recording Server"
            ManagementServer = "C:\Program Files\Salient Security Platform\CompleteView\Management Server"
            LogsFolder = "C:\ProgramData\Salient Security Platform\Logs"
            CrashDumps = "C:\ProgramData\Salient Security Platform\CrashDumps"
        }
    }
}
