

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Advanced Wireshark Filter Builder</title>
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      margin: 0;
      padding: 20px;
      color: #2c3e50;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 30px;
      text-align: center;
      position: relative;
    }
    
    .header h1 {
      margin: 0;
      font-size: 2.5em;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .header .subtitle {
      margin-top: 10px;
      opacity: 0.9;
      font-size: 1.1em;
    }
    
    .main-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      padding: 30px;
    }
    
    .left-panel, .right-panel {
      display: flex;
      flex-direction: column;
      gap: 25px;
    }
    
    .tabs {
      display: flex;
      border-bottom: 2px solid #e0e0e0;
      margin-bottom: 20px;
    }
    
    .tab {
      padding: 12px 24px;
      background: none;
      border: none;
      cursor: pointer;
      font-weight: 600;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
    }
    
    .tab.active {
      color: #4facfe;
      border-bottom-color: #4facfe;
    }
    
    .tab:hover {
      background: rgba(79, 172, 254, 0.1);
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .section {
      background: white;
      padding: 25px;
      border-radius: 15px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .section h3 {
      margin: 0 0 20px 0;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 1.3em;
    }
    
    .section.collapsible h3 {
      cursor: pointer;
      user-select: none;
    }
    
    .section.collapsible h3:after {
      content: '▼';
      margin-left: auto;
      transition: transform 0.3s ease;
    }
    
    .section.collapsible.collapsed h3:after {
      transform: rotate(-90deg);
    }
    
    .section.collapsible.collapsed .section-content {
      display: none;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
    }
    
    label {
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
      color: #34495e;
    }
    
    input[type="text"], input[type="number"], select, textarea {
      width: 100%;
      padding: 12px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: #4facfe;
      box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    }
    
    .input-with-validation {
      position: relative;
    }
    
    .validation-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 16px;
    }
    
    .validation-icon.valid {
      color: #27ae60;
    }
    
    .validation-icon.invalid {
      color: #e74c3c;
    }
    
    .error-message {
      color: #e74c3c;
      font-size: 12px;
      margin-top: 5px;
    }
    
    .radio-group, .checkbox-group {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      align-items: center;
    }
    
    .radio-group label, .checkbox-group label {
      font-weight: normal;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background 0.2s ease;
    }
    
    .radio-group label:hover, .checkbox-group label:hover {
      background: rgba(79, 172, 254, 0.1);
    }
    
    .preset-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin-top: 15px;
    }
    
    .preset-button {
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      background: white;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;
      font-size: 13px;
      position: relative;
    }
    
    .preset-button:hover {
      border-color: #4facfe;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .preset-button.active {
      border-color: #4facfe;
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
    }
    
    .preset-button .preset-title {
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .preset-button .preset-filter {
      font-family: monospace;
      font-size: 11px;
      opacity: 0.8;
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
    }
    
    .btn-warning {
      background: #ffc107;
      color: #212529;
    }
    
    .btn-danger {
      background: #dc3545;
      color: white;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    .btn-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      justify-content: center;
      margin-top: 20px;
    }
    
    .output-section {
      background: #1e1e1e;
      border-radius: 12px;
      overflow: hidden;
    }
    
    .output-header {
      background: #333;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .output-content {
      padding: 20px;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 14px;
      line-height: 1.6;
      color: #f8f8f2;
      min-height: 100px;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    
    .filter-explanation {
      background: #f8f9fa;
      border-left: 4px solid #4facfe;
      padding: 15px;
      margin-top: 15px;
      border-radius: 0 8px 8px 0;
    }
    
    .filter-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }
    
    .stat-card {
      background: white;
      padding: 15px;
      border-radius: 8px;
      text-align: center;
      border: 2px solid #e0e0e0;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #4facfe;
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    
    .history-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-bottom: 10px;
      cursor: pointer;
      transition: background 0.2s ease;
    }
    
    .history-item:hover {
      background: #e9ecef;
    }
    
    .history-filter {
      font-family: monospace;
      font-size: 12px;
      flex: 1;
      margin-right: 10px;
    }
    
    .history-actions {
      display: flex;
      gap: 5px;
    }
    
    .active-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 15px;
    }
    
    .filter-chip {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    
    .filter-chip .remove {
      cursor: pointer;
      font-weight: bold;
    }
    
    .complexity-indicator {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 10px;
    }
    
    .complexity-bar {
      flex: 1;
      height: 6px;
      background: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;
    }
    
    .complexity-fill {
      height: 100%;
      transition: width 0.3s ease;
    }
    
    .complexity-low { background: #27ae60; }
    .complexity-medium { background: #f39c12; }
    .complexity-high { background: #e74c3c; }
    
    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      z-index: 1000;
      transform: translateX(400px);
      transition: transform 0.3s ease;
    }
    
    .toast.show {
      transform: translateX(0);
    }
    
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      backdrop-filter: blur(5px);
    }
    
    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 30px;
      border-radius: 15px;
      max-width: 600px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .close {
      font-size: 24px;
      cursor: pointer;
      color: #999;
    }
    
    @media (max-width: 768px) {
      .main-content {
        grid-template-columns: 1fr;
        padding: 20px;
      }
      
      .form-row {
        grid-template-columns: 1fr;
      }
      
      .preset-grid {
        grid-template-columns: 1fr;
      }
      
      .btn-group {
        flex-direction: column;
      }
    }
    
    .syntax-highlight .keyword { color: #ff6b6b; }
    .syntax-highlight .operator { color: #4ecdc4; }
    .syntax-highlight .value { color: #ffe66d; }
    .syntax-highlight .field { color: #95e1d3; }
  </style>
</head>
<body>

<div class="container">
  <div class="header">
    <h1>🦈 Advanced Wireshark Filter Builder</h1>
    <div class="subtitle">Build complex network filters with ease</div>
  </div>

  <div class="main-content">
    <div class="left-panel">
      <!-- Tabs -->
      <div class="tabs">
        <button class="tab active" onclick="switchTab('basic')">Basic</button>
        <button class="tab" onclick="switchTab('advanced')">Advanced</button>
        <button class="tab" onclick="switchTab('presets')">Presets</button>
        <button class="tab" onclick="switchTab('templates')">Templates</button>
      </div>

      <!-- Basic Tab -->
      <div id="basic-tab" class="tab-content active">
        <div class="section">
          <h3>🔧 Basic Filters</h3>
          <div class="section-content">
            <div class="form-group">
              <label for="protocol">Protocol:</label>
              <select id="protocol" onchange="updateFilter()">
                <option value="">-- Choose --</option>
                <option value="ip">IP</option>
                <option value="tcp">TCP</option>
                <option value="udp">UDP</option>
                <option value="http">HTTP</option>
                <option value="https">HTTPS</option>
                <option value="dns">DNS</option>
                <option value="icmp">ICMP</option>
                <option value="arp">ARP</option>
                <option value="ftp">FTP</option>
                <option value="ssh">SSH</option>
                <option value="telnet">Telnet</option>
                <option value="smtp">SMTP</option>
              </select>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="src">Source IP:</label>
                <div class="input-with-validation">
                  <input type="text" id="src" placeholder="e.g., ************" onchange="validateIP(this)" oninput="updateFilter()"/>
                  <span class="validation-icon" id="src-icon"></span>
                </div>
                <div class="error-message" id="src-error"></div>
              </div>
              <div class="form-group">
                <label for="dst">Destination IP:</label>
                <div class="input-with-validation">
                  <input type="text" id="dst" placeholder="e.g., *******" onchange="validateIP(this)" oninput="updateFilter()"/>
                  <span class="validation-icon" id="dst-icon"></span>
                </div>
                <div class="error-message" id="dst-error"></div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="port">Port:</label>
                <input type="text" id="port" placeholder="e.g., 443 or 80,443" oninput="updateFilter()"/>
              </div>
              <div class="form-group">
                <label for="port-range-from">Port Range:</label>
                <div style="display: flex; gap: 10px; align-items: center;">
                  <input type="number" id="port-range-from" placeholder="From" oninput="updateFilter()"/>
                  <span>to</span>
                  <input type="number" id="port-range-to" placeholder="To" oninput="updateFilter()"/>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>Port Protocol:</label>
              <div class="radio-group">
                <label><input type="radio" name="portproto" value="tcp" checked onchange="updateFilter()"> TCP</label>
                <label><input type="radio" name="portproto" value="udp" onchange="updateFilter()"> UDP</label>
                <label><input type="radio" name="portproto" value="both" onchange="updateFilter()"> Both</label>
              </div>
            </div>

            <div class="form-group">
              <label>Logic Operator:</label>
              <div class="radio-group">
                <label><input type="radio" name="logic" value="&&" checked onchange="updateFilter()"> AND</label>
                <label><input type="radio" name="logic" value="||" onchange="updateFilter()"> OR</label>
                <label><input type="radio" name="logic" value="mixed" onchange="updateFilter()"> Mixed</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Tab -->
      <div id="advanced-tab" class="tab-content">
        <div class="section">
          <h3>⚡ Advanced Filters</h3>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group">
                <label for="mac-src">Source MAC:</label>
                <input type="text" id="mac-src" placeholder="e.g., aa:bb:cc:dd:ee:ff" oninput="updateFilter()"/>
              </div>
              <div class="form-group">
                <label for="mac-dst">Destination MAC:</label>
                <input type="text" id="mac-dst" placeholder="e.g., 11:22:33:44:55:66" oninput="updateFilter()"/>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="vlan-id">VLAN ID:</label>
                <input type="number" id="vlan-id" placeholder="e.g., 100" oninput="updateFilter()"/>
              </div>
              <div class="form-group">
                <label for="packet-size">Packet Size:</label>
                <select id="packet-size" onchange="updateFilter()">
                  <option value="">-- Choose --</option>
                  <option value="frame.len > 1500">Large (>1500)</option>
                  <option value="frame.len < 64">Small (<64)</option>
                  <option value="frame.len == 0">Empty</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label>TCP Flags:</label>
              <div class="checkbox-group">
                <label><input type="checkbox" name="tcp-flags" value="syn" onchange="updateFilter()"> SYN</label>
                <label><input type="checkbox" name="tcp-flags" value="ack" onchange="updateFilter()"> ACK</label>
                <label><input type="checkbox" name="tcp-flags" value="fin" onchange="updateFilter()"> FIN</label>
                <label><input type="checkbox" name="tcp-flags" value="rst" onchange="updateFilter()"> RST</label>
                <label><input type="checkbox" name="tcp-flags" value="psh" onchange="updateFilter()"> PSH</label>
                <label><input type="checkbox" name="tcp-flags" value="urg" onchange="updateFilter()"> URG</label>
              </div>
            </div>

            <div class="form-group">
              <label for="custom">Custom Expression:</label>
              <textarea id="custom" placeholder="Enter custom Wireshark filter expression..." rows="3" oninput="updateFilter()"></textarea>
            </div>

            <div class="form-group">
              <label>NOT Filters:</label>
              <div class="checkbox-group">
                <label><input type="checkbox" id="not-src" onchange="updateFilter()"> NOT Source</label>
                <label><input type="checkbox" id="not-dst" onchange="updateFilter()"> NOT Destination</label>
                <label><input type="checkbox" id="not-port" onchange="updateFilter()"> NOT Port</label>
                <label><input type="checkbox" id="not-protocol" onchange="updateFilter()"> NOT Protocol</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Presets Tab -->
      <div id="presets-tab" class="tab-content">
        <div class="section">
          <h3>📋 Protocol Presets</h3>
          <div class="section-content">
            <div class="preset-grid" id="protocol-presets"></div>
          </div>
        </div>

        <div class="section">
          <h3>🏢 CompleteView Presets</h3>
          <div class="section-content">
            <div class="preset-grid" id="completeview-presets"></div>
          </div>
        </div>

        <div class="section">
          <h3>🔒 Security Presets</h3>
          <div class="section-content">
            <div class="preset-grid" id="security-presets"></div>
          </div>
        </div>
      </div>

      <!-- Templates Tab -->
      <div id="templates-tab" class="tab-content">
        <div class="section">
          <h3>💾 Filter Templates</h3>
          <div class="section-content">
            <div class="btn-group">
              <button class="btn btn-primary" onclick="showSaveTemplateModal()">Save Current as Template</button>
              <button class="btn btn-secondary" onclick="importTemplate()">Import Template</button>
              <button class="btn btn-warning" onclick="exportTemplates()">Export All</button>
            </div>
            <div id="saved-templates" style="margin-top: 20px;"></div>
          </div>
        </div>

        <div class="section">
          <h3>📚 Recent Filters</h3>
          <div class="section-content">
            <div id="filter-history"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <!-- Active Filters -->
      <div class="section">
        <h3>🎯 Active Filters</h3>
        <div class="section-content">
          <div id="active-filters" class="active-filters"></div>
          <div class="complexity-indicator">
            <span>Complexity:</span>
            <div class="complexity-bar">
              <div id="complexity-fill" class="complexity-fill complexity-low" style="width: 0%"></div>
            </div>
            <span id="complexity-text">Low</span>
          </div>
        </div>
      </div>

      <!-- Output Section -->
      <div class="section">
        <div class="output-section">
          <div class="output-header">
            <h3 style="margin: 0; color: white;">🔍 Generated Filter</h3>
            <div>
              <button class="btn btn-primary" onclick="copyToClipboard()">📋 Copy</button>
            </div>
          </div>
          <div id="output" class="output-content">(Your filter will appear here)</div>
        </div>
        
        <div class="filter-explanation">
          <strong>Filter Explanation:</strong>
          <div id="filter-explanation">Build your filter using the controls above.</div>
        </div>
      </div>

      <!-- Filter Stats -->
      <div class="section">
        <h3>📊 Filter Statistics</h3>
        <div class="section-content">
          <div class="filter-stats">
            <div class="stat-card">
              <div class="stat-value" id="filter-count">0</div>
              <div class="stat-label">Active Filters</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="preset-count">0</div>
              <div class="stat-label">Presets Used</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="complexity-score">0</div>
              <div class="stat-label">Complexity</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="section">
        <div class="btn-group">
          <button class="btn btn-primary" onclick="buildFilter()">🔨 Build Filter</button>
          <button class="btn btn-secondary" onclick="clearAll()">🧹 Clear All</button>
          <button class="btn btn-success" onclick="validateFilter()">✅ Validate</button>
          <button class="btn btn-warning" onclick="showHelp()">❓ Help</button>
        </div>
        
        <div class="btn-group" style="margin-top: 10px;">
          <button class="btn btn-secondary" onclick="undoLastAction()">↶ Undo</button>
          <button class="btn btn-secondary" onclick="redoLastAction()">↷ Redo</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Save Template Modal -->
<div id="save-template-modal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Save Filter Template</h3>
      <span class="close" onclick="closeSaveTemplateModal()">&times;</span>
    </div>
    <div class="form-group">
      <label for="template-name">Template Name:</label>
      <input type="text" id="template-name" placeholder="Enter template name...">
    </div>
    <div class="form-group">
      <label for="template-description">Description (optional):</label>
      <textarea id="template-description" placeholder="Describe what this filter does..." rows="3"></textarea>
    </div>
    <div class="btn-group">
      <button class="btn btn-primary" onclick="saveTemplate()">Save Template</button>
      <button class="btn btn-secondary" onclick="closeSaveTemplateModal()">Cancel</button>
    </div>
  </div>
</div>

<!-- Help Modal -->
<div id="help-modal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Wireshark Filter Help</h3>
      <span class="close" onclick="closeHelpModal()">&times;</span>
    </div>
    <div id="help-content"></div>
  </div>
</div>

<script>
  // Global state management
  let filterState = {
    activeFilters: [],
    presetFilters: new Set(),
    history: [],
    historyIndex: -1,
    templates: JSON.parse(localStorage.getItem('wireshark-templates') || '[]')
  };

  // Preset definitions
  const presetGroups = {
    protocol: [
      { title: 'Web Traffic', filter: 'tcp.port == 80 || tcp.port == 443', description: 'HTTP and HTTPS traffic' },
      { title: 'DNS Queries', filter: 'udp.port == 53', description: 'Domain name resolution' },
      { title: 'Email SMTP', filter: 'tcp.port == 25', description: 'Email sending protocol' },
      { title: 'Email IMAP', filter: 'tcp.port == 143 || tcp.port == 993', description: 'Email retrieval protocol' },
      { title: 'FTP', filter: 'tcp.port == 21', description: 'File transfer protocol' },
      { title: 'SSH', filter: 'tcp.port == 22', description: 'Secure shell connections' },
      { title: 'Telnet', filter: 'tcp.port == 23', description: 'Unencrypted remote access' },
      { title: 'DHCP', filter: 'udp.port == 67 || udp.port == 68', description: 'Dynamic host configuration' },
      { title: 'NTP', filter: 'udp.port == 123', description: 'Network time protocol' },
      { title: 'SNMP', filter: 'udp.port == 161', description: 'Simple network management' },
      { title: 'LDAP', filter: 'tcp.port == 389', description: 'Lightweight directory access' }
    ],
    completeview: [
      { title: 'RTSP Streaming', filter: 'tcp.port == 554', description: 'Real-time streaming protocol' },
      { title: 'Management HTTP', filter: 'tcp.port == 4502', description: 'CompleteView management interface' },
      { title: 'Management HTTPS', filter: 'tcp.port == 4503', description: 'Secure management interface' },
      { title: 'Real-time Data', filter: 'tcp.port == 4242', description: 'Real-time data streaming' },
      { title: 'Client HTTP', filter: 'tcp.port == 8095', description: 'Client web interface' },
      { title: 'Client HTTPS', filter: 'tcp.port == 8096', description: 'Secure client interface' },
      { title: 'Video RTP Range', filter: 'udp.port >= 6970 && udp.port <= 7226', description: 'Video streaming ports' },
      { title: 'ONVIF Events', filter: 'tcp.port == 7775', description: 'ONVIF event notifications' },
      { title: 'Database Access', filter: 'tcp.port == 1433', description: 'SQL Server connections' },
      { title: 'Archive Server', filter: 'tcp.port == 7563', description: 'Archive server communication' }
    ],
    security: [
      { title: 'SYN Flood Detection', filter: 'tcp.flags.syn == 1 && tcp.flags.ack == 0', description: 'Potential SYN flood attacks' },
      { title: 'Port Scan Detection', filter: 'tcp.flags.syn == 1 && tcp.window_size <= 1024', description: 'Possible port scanning' },
      { title: 'Failed Connections', filter: 'tcp.flags.rst == 1', description: 'Reset/failed connections' },
      { title: 'Large Packets', filter: 'frame.len > 1500', description: 'Potentially fragmented packets' },
      { title: 'Broadcast Traffic', filter: 'eth.dst == ff:ff:ff:ff:ff:ff', description: 'Network broadcast packets' },
      { title: 'Non-Standard Ports', filter: '!tcp.port in {21 22 23 25 53 ************** 993 995}', description: 'Traffic on unusual ports' },
      { title: 'ICMP Errors', filter: 'icmp.type == 3', description: 'ICMP destination unreachable' },
      { title: 'SSL/TLS Errors', filter: 'ssl.alert_message', description: 'SSL/TLS alert messages' }
    ]
  };

  // Tab switching
  function switchTab(tabName) {
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
  }

  // IP validation
  function validateIP(input) {
    const value = input.value.trim();
    const iconId = input.id + '-icon';
    const errorId = input.id + '-error';
    const icon = document.getElementById(iconId);
    const errorMsg = document.getElementById(errorId);
    
    if (!value) {
      icon.textContent = '';
      errorMsg.textContent = '';
      icon.className = 'validation-icon';
      return true;
    }
    
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/([0-9]|[1-2][0-9]|3[0-2]))?$/;
    const rangeRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    if (ipRegex.test(value) || rangeRegex.test(value)) {
      icon.textContent = '✓';
      icon.className = 'validation-icon valid';
      errorMsg.textContent = '';
      return true;
    } else {
      icon.textContent = '✗';
      icon.className = 'validation-icon invalid';
      errorMsg.textContent = 'Invalid IP address format';
      return false;
    }
  }

  // Toggle preset filter
  function togglePreset(filter, button) {
    if (filterState.presetFilters.has(filter)) {
      filterState.presetFilters.delete(filter);
      button.classList.remove('active');
    } else {
      filterState.presetFilters.add(filter);
      button.classList.add('active');
    }
    updateFilter();
  }

  // Main filter building logic
  function buildFilter() {
    const protocol = document.getElementById('protocol').value;
    const src = document.getElementById('src').value.trim();
    const dst = document.getElementById('dst').value.trim();
    const port = document.getElementById('port').value.trim();
    const portRangeFrom = document.getElementById('port-range-from').value;
    const portRangeTo = document.getElementById('port-range-to').value;
    const portproto = document.querySelector('input[name="portproto"]:checked').value;
    const logic = document.querySelector('input[name="logic"]:checked').value;
    const custom = document.getElementById('custom').value.trim();
    
    // Advanced filters
    const macSrc = document.getElementById('mac-src').value.trim();
    const macDst = document.getElementById('mac-dst').value.trim();
    const vlanId = document.getElementById('vlan-id').value.trim();
    const packetSize = document.getElementById('packet-size').value;
    
    // TCP flags
    const tcpFlags = Array.from(document.querySelectorAll('input[name="tcp-flags"]:checked')).map(cb => cb.value);
    
    // NOT filters
    const notSrc = document.getElementById('not-src').checked;
    const notDst = document.getElementById('not-dst').checked;
    const notPort = document.getElementById('not-port').checked;
    const notProtocol = document.getElementById('not-protocol').checked;

    let filters = [];

    if (protocol) {
      const protocolFilter = notProtocol ? `!${protocol}` : protocol;
      filters.push(protocolFilter);
    }

    if (src) {
      const srcFilter = notSrc ? `!(ip.src == ${src})` : `ip.src == ${src}`;
      filters.push(srcFilter);
    }

    if (dst) {
      const dstFilter = notDst ? `!(ip.dst == ${dst})` : `ip.dst == ${dst}`;
      filters.push(dstFilter);
    }

    if (port) {
      let portFilter = '';
      const ports = port.split(',').map(p => p.trim());
      
      if (ports.length > 1) {
        const portConditions = ports.map(p => {
          if (portproto === "tcp") return `tcp.port == ${p}`;
          if (portproto === "udp") return `udp.port == ${p}`;
          return `tcp.port == ${p} || udp.port == ${p}`;
        });
        portFilter = `(${portConditions.join(' || ')})`;
      } else {
        if (portproto === "tcp") {
          portFilter = `tcp.port == ${port}`;
        } else if (portproto === "udp") {
          portFilter = `udp.port == ${port}`;
        } else {
          portFilter = `tcp.port == ${port} || udp.port == ${port}`;
        }
      }
      
      if (notPort) portFilter = `!(${portFilter})`;
      filters.push(portFilter);
    }

    if (portRangeFrom && portRangeTo) {
      let rangeFilter = '';
      if (portproto === "tcp") {
        rangeFilter = `tcp.port >= ${portRangeFrom} && tcp.port <= ${portRangeTo}`;
      } else if (portproto === "udp") {
        rangeFilter = `udp.port >= ${portRangeFrom} && udp.port <= ${portRangeTo}`;
      } else {
        rangeFilter = `(tcp.port >= ${portRangeFrom} && tcp.port <= ${portRangeTo}) || (udp.port >= ${portRangeFrom} && udp.port <= ${portRangeTo})`;
      }
      filters.push(rangeFilter);
    }

    if (macSrc) filters.push(`eth.src == ${macSrc}`);
    if (macDst) filters.push(`eth.dst == ${macDst}`);
    if (vlanId) filters.push(`vlan.id == ${vlanId}`);
    if (packetSize) filters.push(packetSize);

    if (tcpFlags.length > 0) {
      const flagFilters = tcpFlags.map(flag => `tcp.flags.${flag} == 1`);
      filters.push(`(${flagFilters.join(' && ')})`);
    }

    if (custom) filters.push(`(${custom})`);

    filters = filters.concat(Array.from(filterState.presetFilters));

    let filterString = '';
    if (logic === 'mixed') {
      filterString = filters.join(' && ');
    } else {
      filterString = filters.join(` ${logic} `);
    }

    return filterString || "(No filter components selected)";
  }

  function syntaxHighlight(filter) {
    if (!filter || filter === "(No filter components selected)") {
      return `<span style="color: #999; font-style: italic;">${filter}</span>`;
    }
    
    let highlighted = filter;
    highlighted = highlighted.replace(/\b(tcp|udp|ip|http|https|dns|icmp|arp|eth|frame|vlan)\b/gi, '<span class="keyword">$1</span>');
    highlighted = highlighted.replace(/\b(src|dst|port|len|id|flags|syn|ack|fin|rst|psh|urg)\b/g, '<span class="field">$1</span>');
    highlighted = highlighted.replace(/(&&|\|\||==|!=|>=|<=|>|<)/g, '<span class="operator">$1</span>');
    highlighted = highlighted.replace(/\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|\d+)\b/g, '<span class="value">$1</span>');
    return highlighted;
  }

  function updateFilter() {
    const filterString = buildFilter();
    const output = document.getElementById('output');
    output.innerHTML = syntaxHighlight(filterString);
    updateActiveFilters();
    updateComplexity(filterString);
    updateExplanation(filterString);
    updateStatistics();
  }

  function updateActiveFilters() {
    const container = document.getElementById('active-filters');
    const filters = [];
    
    const protocol = document.getElementById('protocol').value;
    const src = document.getElementById('src').value.trim();
    const dst = document.getElementById('dst').value.trim();
    const port = document.getElementById('port').value.trim();
    const custom = document.getElementById('custom').value.trim();
    
    if (protocol) filters.push({ type: 'Protocol', value: protocol });
    if (src) filters.push({ type: 'Source IP', value: src });
    if (dst) filters.push({ type: 'Dest IP', value: dst });
    if (port) filters.push({ type: 'Port', value: port });
    if (custom) filters.push({ type: 'Custom', value: custom });
    
    filterState.presetFilters.forEach(preset => {
      filters.push({ type: 'Preset', value: preset });
    });
    
    container.innerHTML = filters.map(filter => `
      <div class="filter-chip">
        <strong>${filter.type}:</strong> ${filter.value}
        <span class="remove" onclick="removeFilter('${filter.type}', '${filter.value}')">&times;</span>
      </div>
    `).join('');
  }

  function removeFilter(type, value) {
    switch(type) {
      case 'Protocol':
        document.getElementById('protocol').value = '';
        break;
      case 'Source IP':
        document.getElementById('src').value = '';
        break;
      case 'Dest IP':
        document.getElementById('dst').value = '';
        break;
      case 'Port':
        document.getElementById('port').value = '';
        break;
      case 'Custom':
        document.getElementById('custom').value = '';
        break;
      case 'Preset':
        filterState.presetFilters.delete(value);
        document.querySelectorAll('.preset-button').forEach(btn => {
          if (btn.querySelector('.preset-filter').textContent === value) {
            btn.classList.remove('active');
          }
        });
        break;
    }
    updateFilter();
  }

  function updateComplexity(filterString) {
    let complexity = 0;
    complexity += (filterString.match(/&&|\|\|/g) || []).length * 2;
    complexity += (filterString.match(/\(/g) || []).length;
    const fields = new Set();
    const fieldMatches = filterString.match(/\b\w+\.\w+/g) || [];
    fieldMatches.forEach(field => fields.add(field.split('.')[0]));
    complexity += fields.size;
    complexity += (filterString.match(/!/g) || []).length * 3;
    
    const complexityFill = document.getElementById('complexity-fill');
    const complexityText = document.getElementById('complexity-text');
    const complexityScore = document.getElementById('complexity-score');
    
    let level, percentage;
    if (complexity <= 5) {
      level = 'Low';
      complexityFill.className = 'complexity-fill complexity-low';
      percentage = Math.min((complexity / 5) * 100, 100);
    } else if (complexity <= 15) {
      level = 'Medium';
      complexityFill.className = 'complexity-fill complexity-medium';
      percentage = Math.min(((complexity - 5) / 10) * 100, 100);
    } else {
      level = 'High';
      complexityFill.className = 'complexity-fill complexity-high';
      percentage = 100;
    }
    
    complexityFill.style.width = percentage + '%';
    complexityText.textContent = level;
    complexityScore.textContent = complexity;
  }

  function updateExplanation(filterString) {
    const explanation = document.getElementById('filter-explanation');
    
    if (!filterString || filterString === "(No filter components selected)") {
      explanation.textContent = "Build your filter using the controls above.";
      return;
    }
    
    let parts = [];
    if (filterString.includes('tcp')) parts.push('TCP protocol traffic');
    if (filterString.includes('udp')) parts.push('UDP protocol traffic');
    if (filterString.includes('ip.src')) parts.push('from specific source IP');
    if (filterString.includes('ip.dst')) parts.push('to specific destination IP');
    if (filterString.includes('.port')) parts.push('on specified ports');
    if (filterString.includes('syn')) parts.push('with SYN flag set');
    if (filterString.includes('!')) parts.push('with exclusions applied');
    
    explanation.textContent = parts.length > 0 
      ? `This filter captures ${parts.join(', ')}.`
      : "Complex custom filter expression.";
  }

  function updateStatistics() {
    const filterCount = document.getElementById('filter-count');
    const presetCount = document.getElementById('preset-count');
    
    let activeCount = 0;
    if (document.getElementById('protocol').value) activeCount++;
    if (document.getElementById('src').value.trim()) activeCount++;
    if (document.getElementById('dst').value.trim()) activeCount++;
    if (document.getElementById('port').value.trim()) activeCount++;
    if (document.getElementById('custom').value.trim()) activeCount++;
    
    filterCount.textContent = activeCount;
    presetCount.textContent = filterState.presetFilters.size;
  }

  function clearAll() {
    document.getElementById('protocol').value = '';
    document.getElementById('src').value = '';
    document.getElementById('dst').value = '';
    document.getElementById('port').value = '';
    document.getElementById('port-range-from').value = '';
    document.getElementById('port-range-to').value = '';
    document.getElementById('custom').value = '';
    document.getElementById('mac-src').value = '';
    document.getElementById('mac-dst').value = '';
    document.getElementById('vlan-id').value = '';
    document.getElementById('packet-size').value = '';
    
    document.querySelector('input[name="logic"][value="&&"]').checked = true;
    document.querySelector('input[name="portproto"][value="tcp"]').checked = true;
    document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
    
    filterState.presetFilters.clear();
    document.querySelectorAll('.preset-button').forEach(btn => btn.classList.remove('active'));
    
    document.querySelectorAll('.validation-icon').forEach(icon => {
      icon.textContent = '';
      icon.className = 'validation-icon';
    });
    document.querySelectorAll('.error-message').forEach(msg => msg.textContent = '');
    
    updateFilter();
    showToast('All filters cleared');
  }

  function validateFilter() {
    const filter = buildFilter();
    if (!filter || filter === "(No filter components selected)") {
      showToast('No filter to validate', 'warning');
      return;
    }
    
    const errors = [];
    const openParens = (filter.match(/\(/g) || []).length;
    const closeParens = (filter.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push('Unmatched parentheses');
    }
    
    if (filter.match(/\s(&&|\|\|)\s*(&&|\|\|)/)) {
      errors.push('Consecutive operators');
    }
    
    if (filter.match(/(&&|\|\|)\s*$/)) {
      errors.push('Incomplete expression');
    }
    
    if (errors.length === 0) {
      showToast('Filter syntax appears valid ✓', 'success');
    } else {
      showToast(`Validation errors: ${errors.join(', ')}`, 'error');
    }
  }

  function copyToClipboard() {
    const output = document.getElementById('output').textContent;
    if (!output || output === '(Your filter will appear here)') {
      showToast("Nothing to copy!", 'warning');
      return;
    }
    
    navigator.clipboard.writeText(output)
      .then(() => {
        showToast("Filter copied to clipboard!");
        addToHistory(output);
      })
      .catch(() => showToast("Failed to copy filter.", 'error'));
  }

  function showHelp() {
    const helpContent = document.getElementById('help-content');
    helpContent.innerHTML = `
      <h4>🔍 Filter Syntax Guide</h4>
      <ul>
        <li><strong>Protocols:</strong> tcp, udp, ip, http, dns, icmp</li>
        <li><strong>Addresses:</strong> ip.src == ***********, ip.dst != ********</li>
        <li><strong>Ports:</strong> tcp.port == 80, udp.port in {53 5353}</li>
        <li><strong>Ranges:</strong> tcp.port >= 1024 && tcp.port <= 65535</li>
        <li><strong>Flags:</strong> tcp.flags.syn == 1, tcp.flags.ack == 1</li>
        <li><strong>MAC Addresses:</strong> eth.src == aa:bb:cc:dd:ee:ff</li>
        <li><strong>VLAN:</strong> vlan.id == 100</li>
        <li><strong>Packet Size:</strong> frame.len > 1500</li>
        <li><strong>Logical Operators:</strong> && (AND), || (OR), ! (NOT)</li>
        <li><strong>Grouping:</strong> Use parentheses for complex logic</li>
      </ul>
      
      <h4>💡 Common Examples</h4>
      <ul>
        <li><code>tcp.port == 80 || tcp.port == 443</code> - Web traffic</li>
        <li><code>ip.src == ***********/24 && tcp.flags.syn == 1</code> - SYN packets from local network</li>
        <li><code>!(tcp.port == 22 || tcp.port == 80)</code> - Exclude SSH and HTTP</li>
        <li><code>tcp.port in {21 22 23 25 53 **************}</code> - Common service ports</li>
        <li><code>frame.len > 1500 && ip.proto == 6</code> - Large TCP packets</li>
      </ul>
    `;
    document.getElementById('help-modal').style.display = 'block';
  }

  function closeHelpModal() {
    document.getElementById('help-modal').style.display = 'none';
  }

  function showSaveTemplateModal() {
    const filter = buildFilter();
    if (!filter || filter === "(No filter components selected)") {
      showToast('Build a filter first before saving as template', 'warning');
      return;
    }
    document.getElementById('save-template-modal').style.display = 'block';
  }

  function closeSaveTemplateModal() {
    document.getElementById('save-template-modal').style.display = 'none';
    document.getElementById('template-name').value = '';
    document.getElementById('template-description').value = '';
  }

  function saveTemplate() {
    const name = document.getElementById('template-name').value.trim();
    const description = document.getElementById('template-description').value.trim();
    const filter = buildFilter();
    
    if (!name) {
      showToast('Please enter a template name', 'warning');
      return;
    }
    
    const template = {
      id: Date.now(),
      name: name,
      description: description,
      filter: filter,
      created: new Date().toLocaleString()
    };
    
    filterState.templates.push(template);
    localStorage.setItem('wireshark-templates', JSON.stringify(filterState.templates));
    
    closeSaveTemplateModal();
    loadTemplates();
    showToast('Template saved successfully');
  }

  function loadTemplate(id) {
    const template = filterState.templates.find(t => t.id === id);
    if (template) {
      document.getElementById('custom').value = template.filter;
      updateFilter();
      showToast(`Template "${template.name}" loaded`);
    }
  }

  function deleteTemplate(id) {
    if (confirm('Are you sure you want to delete this template?')) {
      filterState.templates = filterState.templates.filter(t => t.id !== id);
      localStorage.setItem('wireshark-templates', JSON.stringify(filterState.templates));
      loadTemplates();
      showToast('Template deleted');
    }
  }

  function importTemplate() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const imported = JSON.parse(e.target.result);
            filterState.templates = filterState.templates.concat(imported);
            localStorage.setItem('wireshark-templates', JSON.stringify(filterState.templates));
            loadTemplates();
            showToast(`Imported ${imported.length} templates`);
          } catch (error) {
            showToast('Error importing templates', 'error');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  function exportTemplates() {
    const dataStr = JSON.stringify(filterState.templates, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'wireshark-templates.json';
    link.click();
    URL.revokeObjectURL(url);
    showToast('Templates exported successfully');
  }

  function loadFromHistory(index) {
    const item = filterState.history[index];
    if (item) {
      document.getElementById('custom').value = item.filter;
      updateFilter();
      showToast('Filter loaded from history');
    }
  }

  function undoLastAction() {
    if (filterState.historyIndex > 0) {
      filterState.historyIndex--;
      loadFromHistory(filterState.historyIndex);
      showToast('Action undone');
    } else {
      showToast('Nothing to undo', 'warning');
    }
  }

  function redoLastAction() {
    if (filterState.historyIndex < filterState.history.length - 1) {
      filterState.historyIndex++;
      loadFromHistory(filterState.historyIndex);
      showToast('Action redone');
    } else {
      showToast('Nothing to redo', 'warning');
    }
  }

  function toggleSection(element) {
    const section = element.closest('.section');
    section.classList.toggle('collapsed');
  }

  function applyPattern(filter) {
    document.getElementById('custom').value = filter;
    updateFilter();
    showToast('Pattern applied to custom filter');
  }

  function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => toast.classList.add('show'), 100);
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }

  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  function populatePresets() {
    Object.keys(presetGroups).forEach(groupName => {
      const container = document.getElementById(`${groupName}-presets`);
      if (!container) return;
      
      presetGroups[groupName].forEach(preset => {
        const button = document.createElement('div');
        button.className = 'preset-button';
        button.onclick = () => togglePreset(preset.filter, button);
        button.innerHTML = `
          <div class="preset-title">${preset.title}</div>
          <div class="preset-filter">${preset.filter}</div>
        `;
        button.title = preset.description;
        container.appendChild(button);
      });
    });
  }

  function loadTemplates() {
    const container = document.getElementById('saved-templates');
    container.innerHTML = filterState.templates.map(template => `
      <div class="history-item">
        <div>
          <div style="font-weight: bold;">${template.name}</div>
          <div class="history-filter">${template.filter}</div>
          ${template.description ? `<div style="font-size: 12px; color: #666;">${template.description}</div>` : ''}
          <small style="color: #999;">${template.created}</small>
        </div>
        <div class="history-actions">
          <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;" onclick="loadTemplate(${template.id})">Load</button>
          <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteTemplate(${template.id})">Delete</button>
        </div>
      </div>
    `).join('');
  }

  function addToHistory(filter) {
    if (filter && filter !== "(No filter components selected)") {
      filterState.history = filterState.history.slice(0, filterState.historyIndex + 1);
      filterState.history.push({
        filter: filter,
        timestamp: new Date().toLocaleString()
      });
      filterState.historyIndex = filterState.history.length - 1;
      
      if (filterState.history.length > 50) {
        filterState.history = filterState.history.slice(-50);
        filterState.historyIndex = 49;
      }
      
      saveHistory();
      loadHistory();
    }
  }

  function saveHistory() {
    localStorage.setItem('wireshark-filter-history', JSON.stringify(filterState.history));
  }

  function loadHistory() {
    filterState.history = JSON.parse(localStorage.getItem('wireshark-filter-history') || '[]');
    filterState.historyIndex = filterState.history.length - 1;
    
    const container = document.getElementById('filter-history');
    container.innerHTML = filterState.history.slice(-10).reverse().map((item, index) => `
      <div class="history-item" onclick="loadFromHistory(${filterState.history.length - 1 - index})">
        <div class="history-filter">${item.filter}</div>
        <div class="history-actions">
          <small>${item.timestamp}</small>
        </div>
      </div>
    `).join('');
  }

  function initializeApp() {
    populatePresets();
    loadTemplates();
    loadHistory();
    updateFilter();
    
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      input.addEventListener('input', debounce(updateFilter, 300));
    });
  }

  // Close modals when clicking outside
  window.onclick = function(event) {
    const saveModal = document.getElementById('save-template-modal');
    const helpModal = document.getElementById('help-modal');
    
    if (event.target == saveModal) {
      closeSaveTemplateModal();
    }
    if (event.target == helpModal) {
      closeHelpModal();
    }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
      if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
          case 's':
            e.preventDefault();
            showSaveTemplateModal();
            break;
          case 'Enter':
            e.preventDefault();
            buildFilter();
            break;
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              redoLastAction();
            } else {
              undoLastAction();
            }
            break;
        }
      }
    });
    
    // Auto-save state periodically
    setInterval(() => {
      const currentState = {
        protocol: document.getElementById('protocol').value,
        src: document.getElementById('src').value,
        dst: document.getElementById('dst').value,
        port: document.getElementById('port').value,
        custom: document.getElementById('custom').value,
        presets: Array.from(filterState.presetFilters)
      };
      localStorage.setItem('wireshark-current-state', JSON.stringify(currentState));
    }, 30000);
    
    // Restore state on load
    const savedState = localStorage.getItem('wireshark-current-state');
    if (savedState) {
      try {
        const state = JSON.parse(savedState);
        document.getElementById('protocol').value = state.protocol || '';
        document.getElementById('src').value = state.src || '';
        document.getElementById('dst').value = state.dst || '';
        document.getElementById('port').value = state.port || '';
        document.getElementById('custom').value = state.custom || '';
        
        if (state.presets) {
          state.presets.forEach(preset => filterState.presetFilters.add(preset));
          document.querySelectorAll('.preset-button').forEach(btn => {
            const presetFilter = btn.querySelector('.preset-filter').textContent;
            if (filterState.presetFilters.has(presetFilter)) {
              btn.classList.add('active');
            }
          });
        }
        
        updateFilter();
      } catch (e) {
        console.log('Could not restore previous state');
      }
    }
  });



  // Make functions globally available - move to top of script
  window.switchTab = switchTab;
  window.validateIP = validateIP;
  window.togglePreset = togglePreset;
  window.buildFilter = buildFilter;
  window.updateFilter = updateFilter;
  window.removeFilter = removeFilter;
  window.clearAll = clearAll;
  window.validateFilter = validateFilter;
  window.copyToClipboard = copyToClipboard;
  window.showHelp = showHelp;
  window.closeHelpModal = closeHelpModal;
  window.showSaveTemplateModal = showSaveTemplateModal;
  window.closeSaveTemplateModal = closeSaveTemplateModal;
  window.saveTemplate = saveTemplate;
  window.loadTemplate = loadTemplate;
  window.deleteTemplate = deleteTemplate;
  window.importTemplate = importTemplate;
  window.exportTemplates = exportTemplates;
  window.loadFromHistory = loadFromHistory;
  window.undoLastAction = undoLastAction;
  window.redoLastAction = redoLastAction;
  window.toggleSection = toggleSection;
  window.applyPattern = applyPattern;
  window.exportFilter = exportFilter;

  // Initialize the application
  function initializeApp() {
    populatePresets();
    loadTemplates();
    loadHistory();
    updateFilter();
    
    // Set up auto-save for form inputs
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      input.addEventListener('input', debounce(updateFilter, 300));
    });
  }

  // Utility functions
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => toast.classList.add('show'), 100);
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }

  // Tab switching
  function switchTab(tabName) {
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    document.querySelector(`button[onclick="switchTab('${tabName}')"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
  }

  // IP validation
  function validateIP(input) {
    const value = input.value.trim();
    const iconId = input.id + '-icon';
    const errorId = input.id + '-error';
    const icon = document.getElementById(iconId);
    const errorMsg = document.getElementById(errorId);
    
    if (!value) {
      icon.textContent = '';
      errorMsg.textContent = '';
      icon.className = 'validation-icon';
      return true;
    }
    
    // Support for CIDR notation and ranges
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/([0-9]|[1-2][0-9]|3[0-2]))?$/;
    const rangeRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    if (ipRegex.test(value) || rangeRegex.test(value)) {
      icon.textContent = '✓';
      icon.className = 'validation-icon valid';
      errorMsg.textContent = '';
      return true;
    } else {
      icon.textContent = '✗';
      icon.className = 'validation-icon invalid';
      errorMsg.textContent = 'Invalid IP address format';
      return false;
    }
  }

  // Populate preset buttons
  function populatePresets() {
    Object.keys(presetGroups).forEach(groupName => {
      const container = document.getElementById(`${groupName}-presets`);
      if (!container) return;
      
      presetGroups[groupName].forEach(preset => {
        const button = document.createElement('div');
        button.className = 'preset-button';
        button.onclick = () => togglePreset(preset.filter, button);
        button.innerHTML = `
          <div class="preset-title">${preset.title}</div>
          <div class="preset-filter">${preset.filter}</div>
        `;
        button.title = preset.description;
        container.appendChild(button);
      });
    });
  }

  // Toggle preset filter
  function togglePreset(filter, button) {
    if (filterState.presetFilters.has(filter)) {
      filterState.presetFilters.delete(filter);
      button.classList.remove('active');
    } else {
      filterState.presetFilters.add(filter);
      button.classList.add('active');
    }
    updateFilter();
  }

  // Main filter building logic
  function buildFilter() {
    const protocol = document.getElementById('protocol').value;
    const src = document.getElementById('src').value.trim();
    const dst = document.getElementById('dst').value.trim();
    const port = document.getElementById('port').value.trim();
    const portRangeFrom = document.getElementById('port-range-from').value;
    const portRangeTo = document.getElementById('port-range-to').value;
    const portproto = document.querySelector('input[name="portproto"]:checked').value;
    const logic = document.querySelector('input[name="logic"]:checked').value;
    const custom = document.getElementById('custom').value.trim();
    
    // Advanced filters
    const macSrc = document.getElementById('mac-src').value.trim();
    const macDst = document.getElementById('mac-dst').value.trim();
    const vlanId = document.getElementById('vlan-id').value.trim();
    const packetSize = document.getElementById('packet-size').value;
    
    // TCP flags
    const tcpFlags = Array.from(document.querySelectorAll('input[name="tcp-flags"]:checked'))
      .map(cb => cb.value);
    
    // NOT filters
    const notSrc = document.getElementById('not-src').checked;
    const notDst = document.getElementById('not-dst').checked;
    const notPort = document.getElementById('not-port').checked;
    const notProtocol = document.getElementById('not-protocol').checked;

    let filters = [];

    // Build basic filters
    if (protocol) {
      const protocolFilter = notProtocol ? `!${protocol}` : protocol;
      filters.push(protocolFilter);
    }

    if (src) {
      const srcFilter = notSrc ? `!(ip.src == ${src})` : `ip.src == ${src}`;
      filters.push(srcFilter);
    }

    if (dst) {
      const dstFilter = notDst ? `!(ip.dst == ${dst})` : `ip.dst == ${dst}`;
      filters.push(dstFilter);
    }

    // Handle ports
    if (port) {
      let portFilter = '';
      const ports = port.split(',').map(p => p.trim());
      
      if (ports.length > 1) {
        const portConditions = ports.map(p => {
          if (portproto === "tcp") return `tcp.port == ${p}`;
          if (portproto === "udp") return `udp.port == ${p}`;
          return `tcp.port == ${p} || udp.port == ${p}`;
        });
        portFilter = `(${portConditions.join(' || ')})`;
      } else {
        if (portproto === "tcp") {
          portFilter = `tcp.port == ${port}`;
        } else if (portproto === "udp") {
          portFilter = `udp.port == ${port}`;
        } else {
          portFilter = `tcp.port == ${port} || udp.port == ${port}`;
        }
      }
      
      if (notPort) portFilter = `!(${portFilter})`;
      filters.push(portFilter);
    }

    // Port range
    if (portRangeFrom && portRangeTo) {
      let rangeFilter = '';
      if (portproto === "tcp") {
        rangeFilter = `tcp.port >= ${portRangeFrom} && tcp.port <= ${portRangeTo}`;
      } else if (portproto === "udp") {
        rangeFilter = `udp.port >= ${portRangeFrom} && udp.port <= ${portRangeTo}`;
      } else {
        rangeFilter = `(tcp.port >= ${portRangeFrom} && tcp.port <= ${portRangeTo}) || (udp.port >= ${portRangeFrom} && udp.port <= ${portRangeTo})`;
      }
      filters.push(rangeFilter);
    }

    // Advanced filters
    if (macSrc) filters.push(`eth.src == ${macSrc}`);
    if (macDst) filters.push(`eth.dst == ${macDst}`);
    if (vlanId) filters.push(`vlan.id == ${vlanId}`);
    if (packetSize) filters.push(packetSize);

    // TCP flags
    if (tcpFlags.length > 0) {
      const flagFilters = tcpFlags.map(flag => `tcp.flags.${flag} == 1`);
      filters.push(`(${flagFilters.join(' && ')})`);
    }

    // Custom filter
    if (custom) filters.push(`(${custom})`);

    // Add preset filters
    filters = filters.concat(Array.from(filterState.presetFilters));

    // Combine filters
    let filterString = '';
    if (logic === 'mixed') {
      // For mixed logic, we'll use a more sophisticated approach
      filterString = filters.join(' && ');
    } else {
      filterString = filters.join(` ${logic} `);
    }

    return filterString || "(No filter components selected)";
  }

  // Update filter display
  function updateFilter() {
    const filterString = buildFilter();
    const output = document.getElementById('output');
    
    // Apply syntax highlighting
    output.innerHTML = syntaxHighlight(filterString);
    
    // Update active filters display
    updateActiveFilters();
    
    // Update complexity
    updateComplexity(filterString);
    
    // Update explanation
    updateExplanation(filterString);
    
    // Update statistics
    updateStatistics();
  }

  // Syntax highlighting
  function syntaxHighlight(filter) {
    if (!filter || filter === "(No filter components selected)") {
      return filter;
    }
    
    return filter
      .replace(/\b(tcp|udp|ip|http|https|dns|icmp|arp|ftp|ssh|smtp|eth|vlan|frame)\b/g, '<span class="keyword">$1</span>')
      .replace(/\b(==|!=|>=|<=|>|<|&&|\|\||!)\b/g, '<span class="operator">$1</span>')
      .replace(/\b(\d+\.\d+\.\d+\.\d+|\d+)\b/g, '<span class="value">$1</span>')
 .replace(/\b(src|dst|port|len|id|flags|syn|ack|fin|rst|psh|urg)\b/g, '<span class="field">$1</span>');
  }

  // Update active filters chips
  function updateActiveFilters() {
    const container = document.getElementById('active-filters');
    const filters = [];
    
    // Collect all active filter components
    const protocol = document.getElementById('protocol').value;
    const src = document.getElementById('src').value.trim();
    const dst = document.getElementById('dst').value.trim();
    const port = document.getElementById('port').value.trim();
    const custom = document.getElementById('custom').value.trim();
    
    if (protocol) filters.push({ type: 'Protocol', value: protocol });
    if (src) filters.push({ type: 'Source IP', value: src });
    if (dst) filters.push({ type: 'Dest IP', value: dst });
    if (port) filters.push({ type: 'Port', value: port });
    if (custom) filters.push({ type: 'Custom', value: custom });
    
    filterState.presetFilters.forEach(preset => {
      filters.push({ type: 'Preset', value: preset });
    });
    
    container.innerHTML = filters.map(filter => `
      <div class="filter-chip">
        <strong>${filter.type}:</strong> ${filter.value}
        <span class="remove" onclick="removeFilter('${filter.type}', '${filter.value}')">&times;</span>
      </div>
    `).join('');
  }

  // Remove individual filter
  function removeFilter(type, value) {
    switch(type) {
      case 'Protocol':
        document.getElementById('protocol').value = '';
        break;
      case 'Source IP':
        document.getElementById('src').value = '';
        break;
      case 'Dest IP':
        document.getElementById('dst').value = '';
        break;
      case 'Port':
        document.getElementById('port').value = '';
        break;
      case 'Custom':
        document.getElementById('custom').value = '';
        break;
      case 'Preset':
        filterState.presetFilters.delete(value);
        document.querySelectorAll('.preset-button').forEach(btn => {
          if (btn.querySelector('.preset-filter').textContent === value) {
            btn.classList.remove('active');
          }
        });
        break;
    }
    updateFilter();
  }

  // Update complexity indicator
  function updateComplexity(filterString) {
    let complexity = 0;
    
    // Count operators
    complexity += (filterString.match(/&&|\|\|/g) || []).length * 2;
    
    // Count parentheses groups
    complexity += (filterString.match(/\(/g) || []).length;
    
    // Count different field types
    const fields = new Set();
    const fieldMatches = filterString.match(/\b\w+\.\w+/g) || [];
    fieldMatches.forEach(field => fields.add(field.split('.')[0]));
    complexity += fields.size;
    
    // Count NOT operations
    complexity += (filterString.match(/!/g) || []).length * 3;
    
    const complexityFill = document.getElementById('complexity-fill');
    const complexityText = document.getElementById('complexity-text');
    const complexityScore = document.getElementById('complexity-score');
    
    let level, percentage;
    if (complexity <= 5) {
      level = 'Low';
      complexityFill.className = 'complexity-fill complexity-low';
      percentage = Math.min((complexity / 5) * 100, 100);
    } else if (complexity <= 15) {
      level = 'Medium';
      complexityFill.className = 'complexity-fill complexity-medium';
      percentage = Math.min(((complexity - 5) / 10) * 100, 100);
    } else {
      level = 'High';
      complexityFill.className = 'complexity-fill complexity-high';
      percentage = 100;
    }
    
    complexityFill.style.width = percentage + '%';
    complexityText.textContent = level;
    complexityScore.textContent = complexity;
  }

  // Update filter explanation
  function updateExplanation(filterString) {
    const explanation = document.getElementById('filter-explanation');
    
    if (!filterString || filterString === "(No filter components selected)") {
      explanation.textContent = "Build your filter using the controls above.";
      return;
    }
    
    // Generate human-readable explanation
    let parts = [];
    
    if (filterString.includes('tcp')) parts.push('TCP protocol traffic');
    if (filterString.includes('udp')) parts.push('UDP protocol traffic');
    if (filterString.includes('ip.src')) parts.push('from specific source IP');
    if (filterString.includes('ip.dst')) parts.push('to specific destination IP');
    if (filterString.includes('.port')) parts.push('on specified ports');
    if (filterString.includes('syn')) parts.push('with SYN flag set');
    if (filterString.includes('!')) parts.push('with exclusions applied');
    
    explanation.textContent = parts.length > 0 
      ? `This filter captures ${parts.join(', ')}.`
      : "Complex custom filter expression.";
  }

  // Update statistics
  function updateStatistics() {
    const filterCount = document.getElementById('filter-count');
    const presetCount = document.getElementById('preset-count');
    
    let activeCount = 0;
    if (document.getElementById('protocol').value) activeCount++;
    if (document.getElementById('src').value.trim()) activeCount++;
    if (document.getElementById('dst').value.trim()) activeCount++;
    if (document.getElementById('port').value.trim()) activeCount++;
    if (document.getElementById('custom').value.trim()) activeCount++;
    
    filterCount.textContent = activeCount;
    presetCount.textContent = filterState.presetFilters.size;
  }

  // History management
  function addToHistory(filter) {
    if (filter && filter !== "(No filter components selected)") {
      filterState.history = filterState.history.slice(0, filterState.historyIndex + 1);
      filterState.history.push({
        filter: filter,
        timestamp: new Date().toLocaleString()
      });
      filterState.historyIndex = filterState.history.length - 1;
      
      // Keep only last 50 items
      if (filterState.history.length > 50) {
        filterState.history = filterState.history.slice(-50);
        filterState.historyIndex = 49;
      }
      
      saveHistory();
      loadHistory();
    }
  }

  function saveHistory() {
    localStorage.setItem('wireshark-filter-history', JSON.stringify(filterState.history));
  }

  function loadHistory() {
    filterState.history = JSON.parse(localStorage.getItem('wireshark-filter-history') || '[]');
    filterState.historyIndex = filterState.history.length - 1;
    
    const container = document.getElementById('filter-history');
    container.innerHTML = filterState.history.slice(-10).reverse().map((item, index) => `
      <div class="history-item" onclick="loadFromHistory(${filterState.history.length - 1 - index})">
        <div class="history-filter">${item.filter}</div>
        <div class="history-actions">
          <small>${item.timestamp}</small>
        </div>
      </div>
    `).join('');
  }

  function loadFromHistory(index) {
    const item = filterState.history[index];
    if (item) {
      // This is a simplified version - in a real implementation,
      // you'd need to parse the filter and set the appropriate form fields
      document.getElementById('custom').value = item.filter;
      updateFilter();
      showToast('Filter loaded from history');
    }
  }

  // Undo/Redo functionality
  function undoLastAction() {
    if (filterState.historyIndex > 0) {
      filterState.historyIndex--;
      loadFromHistory(filterState.historyIndex);
      showToast('Action undone');
    } else {
      showToast('Nothing to undo', 'warning');
    }
  }

  function redoLastAction() {
    if (filterState.historyIndex < filterState.history.length - 1) {
      filterState.historyIndex++;
      loadFromHistory(filterState.historyIndex);
      showToast('Action redone');
    } else {
      showToast('Nothing to redo', 'warning');
    }
  }

  // Template management
  function showSaveTemplateModal() {
    const filter = buildFilter();
    if (!filter || filter === "(No filter components selected)") {
      showToast('Build a filter first before saving as template', 'warning');
      return;
    }
    document.getElementById('save-template-modal').style.display = 'block';
  }

  function closeSaveTemplateModal() {
    document.getElementById('save-template-modal').style.display = 'none';
    document.getElementById('template-name').value = '';
    document.getElementById('template-description').value = '';
  }

  function saveTemplate() {
    const name = document.getElementById('template-name').value.trim();
    const description = document.getElementById('template-description').value.trim();
    const filter = buildFilter();
    
    if (!name) {
      showToast('Please enter a template name', 'warning');
      return;
    }
    
    const template = {
      id: Date.now(),
      name: name,
      description: description,
      filter: filter,
      created: new Date().toLocaleString()
    };
    
    filterState.templates.push(template);
    localStorage.setItem('wireshark-templates', JSON.stringify(filterState.templates));
    
    closeSaveTemplateModal();
    loadTemplates();
    showToast('Template saved successfully');
  }

  function loadTemplates() {
    const container = document.getElementById('saved-templates');
    container.innerHTML = filterState.templates.map(template => `
      <div class="history-item">
        <div>
          <div style="font-weight: bold;">${template.name}</div>
          <div class="history-filter">${template.filter}</div>
          ${template.description ? `<div style="font-size: 12px; color: #666;">${template.description}</div>` : ''}
          <small style="color: #999;">${template.created}</small>
        </div>
        <div class="history-actions">
          <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;" onclick="loadTemplate(${template.id})">Load</button>
          <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteTemplate(${template.id})">Delete</button>
        </div>
      </div>
    `).join('');
  }

  function loadTemplate(id) {
    const template = filterState.templates.find(t => t.id === id);
    if (template) {
      // Simplified loading - in real implementation, parse and set form fields
      document.getElementById('custom').value = template.filter;
      updateFilter();
      showToast(`Template "${template.name}" loaded`);
    }
  }

  function deleteTemplate(id) {
    if (confirm('Are you sure you want to delete this template?')) {
      filterState.templates = filterState.templates.filter(t => t.id !== id);
      localStorage.setItem('wireshark-templates', JSON.stringify(filterState.templates));
      loadTemplates();
      showToast('Template deleted');
    }
  }

  function importTemplate() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const imported = JSON.parse(e.target.result);
            filterState.templates = filterState.templates.concat(imported);
            localStorage.setItem('wireshark-templates', JSON.stringify(filterState.templates));
            loadTemplates();
            showToast(`Imported ${imported.length} templates`);
          } catch (error) {
            showToast('Error importing templates', 'error');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  function exportTemplates() {
    const dataStr = JSON.stringify(filterState.templates, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'wireshark-templates.json';
    link.click();
    URL.revokeObjectURL(url);
    showToast('Templates exported successfully');
  }

  // Utility functions
  function clearAll() {
    document.getElementById('protocol').value = '';
    document.getElementById('src').value = '';
    document.getElementById('dst').value = '';
    document.getElementById('port').value = '';
    document.getElementById('port-range-from').value = '';
    document.getElementById('port-range-to').value = '';
    document.getElementById('custom').value = '';
    document.getElementById('mac-src').value = '';
    document.getElementById('mac-dst').value = '';
    document.getElementById('vlan-id').value = '';
    document.getElementById('packet-size').value = '';
    
    // Reset radio buttons
    document.querySelector('input[name="logic"][value="&&"]').checked = true;
    document.querySelector('input[name="portproto"][value="tcp"]').checked = true;
    
    // Clear checkboxes
    document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
    
    // Clear presets
    filterState.presetFilters.clear();
    document.querySelectorAll('.preset-button').forEach(btn => btn.classList.remove('active'));
    
    // Clear validation
    document.querySelectorAll('.validation-icon').forEach(icon => {
      icon.textContent = '';
      icon.className = 'validation-icon';
    });
    document.querySelectorAll('.error-message').forEach(msg => msg.textContent = '');
    
    updateFilter();
    showToast('All filters cleared');
  }

  function validateFilter() {
    const filter = buildFilter();
    if (!filter || filter === "(No filter components selected)") {
      showToast('No filter to validate', 'warning');
      return;
    }
    
    // Basic validation - check for common syntax errors
    const errors = [];
    
    // Check for unmatched parentheses
    const openParens = (filter.match(/\(/g) || []).length;
    const closeParens = (filter.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push('Unmatched parentheses');
    }
    
    // Check for invalid operators
    if (filter.match(/\s(&&|\|\|)\s*(&&|\|\|)/)) {
      errors.push('Consecutive operators');
    }
    
    // Check for incomplete expressions
    if (filter.match(/(&&|\|\|)\s*$/)) {
      errors.push('Incomplete expression');
    }
    
    if (errors.length === 0) {
      showToast('Filter syntax appears valid ✓', 'success');
    } else {
      showToast(`Validation errors: ${errors.join(', ')}`, 'error');
    }
  }

  function copyToClipboard() {
    const output = document.getElementById('output').textContent;
    if (!output || output === '(Your filter will appear here)') {
      showToast("Nothing to copy!", 'warning');
      return;
    }
    
    navigator.clipboard.writeText(output)
      .then(() => {
        showToast("Filter copied to clipboard!");
        addToHistory(output);
      })
      .catch(() => showToast("Failed to copy filter.", 'error'));
  }

  function showHelp() {
    const helpContent = document.getElementById('help-content');
    helpContent.innerHTML = `
      <h4>🔍 Filter Syntax Guide</h4>
      <ul>
        <li><strong>Protocols:</strong> tcp, udp, ip, http, dns, icmp</li>
        <li><strong>Addresses:</strong> ip.src == ***********, ip.dst != ********</li>
        <li><strong>Ports:</strong> tcp.port == 80, udp.port in {53 5353}</li>
        <li><strong>Ranges:</strong> tcp.port >= 1024 && tcp.port <= 65535</li>
        <li><strong>Flags:</strong> tcp.flags.syn == 1, tcp.flags.ack == 1</li>
        <li><strong>MAC Addresses:</strong> eth.src == aa:bb:cc:dd:ee:ff</li>
        <li><strong>VLAN:</strong> vlan.id == 100</li>
        <li><strong>Packet Size:</strong> frame.len > 1500</li>
        <li><strong>Logical Operators:</strong> && (AND), || (OR), ! (NOT)</li>
        <li><strong>Grouping:</strong> Use parentheses for complex logic</li>
      </ul>
      
      <h4>💡 Common Examples</h4>
      <ul>
        <li><code>tcp.port == 80 || tcp.port == 443</code> - Web traffic</li>
        <li><code>ip.src == ***********/24 && tcp.flags.syn == 1</code> - SYN packets from local network</li>
        <li><code>!(tcp.port == 22 || tcp.port == 80)</code> - Exclude SSH and HTTP</li>
        <li><code>tcp.port in {21 22 23 25 53 **************}</code> - Common service ports</li>
        <li><code>frame.len > 1500 && ip.proto == 6</code> - Large TCP packets</li>
      </ul>
      
      <h4>⚡ Performance Tips</h4>
      <ul>
        <li>Put most specific filters first</li>
        <li>Use exact matches when possible</li>
        <li>Avoid complex regex in custom filters</li>
        <li>Group related conditions with parentheses</li>
        <li>Test filters on small captures first</li>
      </ul>
      
      <h4>🎯 Best Practices</h4>
      <ul>
        <li>Start with broad filters, then narrow down</li>
        <li>Save frequently used filters as templates</li>
        <li>Use descriptive names for custom templates</li>
        <li>Validate complex filters before use</li>
        <li>Document complex filters for team sharing</li>
      </ul>
    `;
    document.getElementById('help-modal').style.display = 'block';
  }

  function closeHelpModal() {
    document.getElementById('help-modal').style.display = 'none';
  }

  // Close modals when clicking outside
  window.onclick = function(event) {
    const saveModal = document.getElementById('save-template-modal');
    const helpModal = document.getElementById('help-modal');
    
    if (event.target == saveModal) {
      closeSaveTemplateModal();
    }
    if (event.target == helpModal) {
      closeHelpModal();
    }
  }

  // Collapsible sections
  function toggleSection(element) {
    const section = element.closest('.section');
    section.classList.toggle('collapsed');
  }

  // Enhanced syntax highlighting with more precise regex
  function syntaxHighlight(filter) {
    if (!filter || filter === "(No filter components selected)") {
      return `<span style="color: #999; font-style: italic;">${filter}</span>`;
    }
    
    let highlighted = filter;
    
    // Protocol keywords
    highlighted = highlighted.replace(/\b(tcp|udp|ip|ipv6|http|https|dns|icmp|icmpv6|arp|dhcp|ftp|ssh|smtp|pop3|imap|ntp|snmp|ldap|ssl|tls|rtp|rtcp|sip|ethernet|eth|frame|vlan)\b/gi, 
      '<span class="keyword">$1</span>');
    
    // Field names
    highlighted = highlighted.replace(/\b(src|dst|port|len|id|flags|syn|ack|fin|rst|psh|urg|window|checksum|proto|addr|type|code|data|payload)\b/g, 
      '<span class="field">$1</span>');
    
    // Operators
    highlighted = highlighted.replace(/(\&\&|\|\||==|!=|>=|<=|>|<)/g, 
      '<span class="operator">$1</span>');
    
    // IP addresses and numbers
    highlighted = highlighted.replace(/\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?:\/\d{1,2})?|\d+)\b/g, 
      '<span class="value">$1</span>');
    
    // MAC addresses
    highlighted = highlighted.replace(/\b([0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2})\b/g, 
      '<span class="value">$1</span>');
    
    return highlighted;
  }

  // Enhanced auto-completion suggestions
  function setupAutoComplete() {
    const customInput = document.getElementById('custom');
    const suggestions = [
      'tcp.flags.syn == 1',
      'tcp.flags.ack == 1',
      'tcp.flags.fin == 1',
      'tcp.flags.rst == 1',
      'ip.addr == ',
      'tcp.port == ',
      'udp.port == ',
      'frame.len > ',
      'eth.dst == ',
      'eth.src == ',
      'vlan.id == ',
      'tcp.stream == ',
      'udp.stream == ',
      'http.request.method == "GET"',
      'http.response.code == 200',
      'dns.qry.name contains',
      'ssl.handshake.type == 1'
    ];
    
    // This would be implemented with a proper autocomplete library in production
    customInput.addEventListener('input', function(e) {
      // Auto-complete logic would go here
      // For now, just update the filter
      updateFilter();
    });
  }

  // Export functionality for different formats
  function exportFilter(format) {
    const filter = buildFilter();
    if (!filter || filter === "(No filter components selected)") {
      showToast("No filter to export", 'warning');
      return;
    }
    
    let content, filename, mimeType;
    
    switch(format) {
      case 'wireshark':
        content = filter;
        filename = 'wireshark-filter.txt';
        mimeType = 'text/plain';
        break;
      case 'tcpdump':
        // Convert Wireshark filter to tcpdump format (simplified)
        content = convertToTcpdump(filter);
        filename = 'tcpdump-filter.txt';
        mimeType = 'text/plain';
        break;
      case 'json':
        content = JSON.stringify({
          wireshark_filter: filter,
          created: new Date().toISOString(),
          description: "Generated by Advanced Wireshark Filter Builder"
        }, null, 2);
        filename = 'filter-config.json';
        mimeType = 'application/json';
        break;
    }
    
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
    
    showToast(`Filter exported as ${format.toUpperCase()}`);
  }

  // Convert Wireshark filter to tcpdump format (simplified conversion)
  function convertToTcpdump(wiresharkFilter) {
    let tcpdumpFilter = wiresharkFilter;
    
    // Basic conversions
    tcpdumpFilter = tcpdumpFilter.replace(/ip\.src/g, 'src host');
    tcpdumpFilter = tcpdumpFilter.replace(/ip\.dst/g, 'dst host');
    tcpdumpFilter = tcpdumpFilter.replace(/tcp\.port/g, 'port');
    tcpdumpFilter = tcpdumpFilter.replace(/udp\.port/g, 'port');
    tcpdumpFilter = tcpdumpFilter.replace(/\&\&/g, ' and ');
    tcpdumpFilter = tcpdumpFilter.replace(/\|\|/g, ' or ');
    tcpdumpFilter = tcpdumpFilter.replace(/==/g, '');
    tcpdumpFilter = tcpdumpFilter.replace(/!=/g, 'not ');
    
    return `# Converted from Wireshark filter (may need manual adjustment)\n${tcpdumpFilter}`;
  }

  // Advanced filter patterns
  const filterPatterns = {
    'Troubleshooting': [
      { name: 'TCP Retransmissions', filter: 'tcp.analysis.retransmission' },
      { name: 'TCP Out-of-Order', filter: 'tcp.analysis.out_of_order' },
      { name: 'TCP Lost Segments', filter: 'tcp.analysis.lost_segment' },
      { name: 'TCP Zero Window', filter: 'tcp.analysis.zero_window' },
      { name: 'Duplicate ACKs', filter: 'tcp.analysis.duplicate_ack' },
      { name: 'HTTP Errors', filter: 'http.response.code >= 400' },
      { name: 'DNS No Response', filter: 'dns.flags.response == 0' },
      { name: 'ARP Requests', filter: 'arp.opcode == 1' }
    ],
    'Security': [
      { name: 'Port Scans', filter: 'tcp.flags.syn == 1 and tcp.flags.ack == 0 and tcp.window_size <= 1024' },
      { name: 'Failed Logins', filter: 'tcp.flags.rst == 1 and tcp.port in {21 22 23 25 110 143}' },
      { name: 'Suspicious DNS', filter: 'dns.qry.name contains "bit" or dns.qry.name contains "tor"' },
      { name: 'Large Packets', filter: 'frame.len > 1500' },
      { name: 'Broadcast Storms', filter: 'eth.dst == ff:ff:ff:ff:ff:ff' },
      { name: 'ICMP Tunneling', filter: 'icmp and frame.len > 100' },
      { name: 'Suspicious Protocols', filter: 'tcp.port in {1337 31337 12345 54321}' }
    ],
    'Performance': [
      { name: 'High Latency', filter: 'tcp.analysis.ack_rtt > 0.1' },
      { name: 'Bandwidth Hogs', filter: 'frame.len > 1400' },
      { name: 'Fragmented Packets', filter: 'ip.flags.mf == 1 or ip.frag_offset > 0' },
      { name: 'Small Packets', filter: 'frame.len < 64' },
      { name: 'Keep-Alive Traffic', filter: 'tcp.len == 0 and tcp.flags == 0x10' },
      { name: 'Multicast Traffic', filter: 'ip.dst >= ********* and ip.dst <= ***************' }
    ]
  };

  // Add pattern presets to the interface
  function addPatternPresets() {
    const container = document.getElementById('advanced-tab');
    const patternSection = document.createElement('div');
    patternSection.className = 'section collapsible';
    patternSection.innerHTML = `
      <h3 onclick="toggleSection(this)">🎯 Filter Patterns</h3>
      <div class="section-content">
        ${Object.keys(filterPatterns).map(category => `
          <h4>${category}</h4>
          <div class="preset-grid">
            ${filterPatterns[category].map(pattern => `
              <div class="preset-button" onclick="applyPattern('${pattern.filter}')">
                <div class="preset-title">${pattern.name}</div>
                <div class="preset-filter">${pattern.filter}</div>
              </div>
            `).join('')}
          </div>
        `).join('')}
      </div>
    `;
    container.appendChild(patternSection);
  }

  function applyPattern(filter) {
    document.getElementById('custom').value = filter;
    updateFilter();
    showToast('Pattern applied to custom filter');
  }

  // Initialize everything when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupAutoComplete();
    addPatternPresets();
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
      if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
          case 's':
            e.preventDefault();
            showSaveTemplateModal();
            break;
          case 'Enter':
            e.preventDefault();
            buildFilter();
            break;
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              redoLastAction();
            } else {
              undoLastAction();
            }
            break;
        }
      }
    });
    
    // Auto-save state periodically
    setInterval(() => {
      const currentState = {
        protocol: document.getElementById('protocol').value,
        src: document.getElementById('src').value,
        dst: document.getElementById('dst').value,
        port: document.getElementById('port').value,
        custom: document.getElementById('custom').value,
        presets: Array.from(filterState.presetFilters)
      };
      localStorage.setItem('wireshark-current-state', JSON.stringify(currentState));
    }, 30000); // Save every 30 seconds
    
    // Restore state on load
    const savedState = localStorage.getItem('wireshark-current-state');
    if (savedState) {
      try {
        const state = JSON.parse(savedState);
        document.getElementById('protocol').value = state.protocol || '';
        document.getElementById('src').value = state.src || '';
        document.getElementById('dst').value = state.dst || '';
        document.getElementById('port').value = state.port || '';
        document.getElementById('custom').value = state.custom || '';
        
        if (state.presets) {
          state.presets.forEach(preset => filterState.presetFilters.add(preset));
          // Reactivate preset buttons
          document.querySelectorAll('.preset-button').forEach(btn => {
            const presetFilter = btn.querySelector('.preset-filter').textContent;
            if (filterState.presetFilters.has(presetFilter)) {
              btn.classList.add('active');
            }
          });
        }
        
        updateFilter();
      } catch (e) {
        console.log('Could not restore previous state');
      }
    }
  });

  // Make functions globally available
  window.switchTab = switchTab;
  window.validateIP = validateIP;
  window.togglePreset = togglePreset;
  window.buildFilter = buildFilter;
  window.updateFilter = updateFilter;
  window.removeFilter = removeFilter;
  window.clearAll = clearAll;
  window.validateFilter = validateFilter;
  window.copyToClipboard = copyToClipboard;
  window.showHelp = showHelp;
  window.closeHelpModal = closeHelpModal;
  window.showSaveTemplateModal = showSaveTemplateModal;
  window.closeSaveTemplateModal = closeSaveTemplateModal;
  window.saveTemplate = saveTemplate;
  window.loadTemplate = loadTemplate;
  window.deleteTemplate = deleteTemplate;
  window.importTemplate = importTemplate;
  window.exportTemplates = exportTemplates;
  window.loadFromHistory = loadFromHistory;
  window.undoLastAction = undoLastAction;
  window.redoLastAction = redoLastAction;
  window.toggleSection = toggleSection;
  window.applyPattern = applyPattern;
  window.exportFilter = exportFilter;

</script>

</body>
</html>
