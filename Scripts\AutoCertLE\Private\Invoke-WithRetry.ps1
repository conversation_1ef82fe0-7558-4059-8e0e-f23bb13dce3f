# Retry operations with exponential backoff
function Invoke-WithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [scriptblock]$ScriptBlock,
        
        [Parameter()]
        [int]$MaxAttempts = 5,
        
        [Parameter()]
        [int]$InitialDelaySeconds = 2,
        
        [Parameter()]
        [double]$BackoffMultiplier = 2,
        
        [Parameter()]
        [string]$OperationName = "Operation",
        
        [Parameter()]
        [scriptblock]$SuccessCondition = { $true }
    )

    $attempt = 1
    $delay = $InitialDelaySeconds

    while ($attempt -le $MaxAttempts) {
        Write-Debug "Attempt $attempt of $MaxAttempts for $OperationName"
        try {
            $result = & $ScriptBlock
            
            if (& $SuccessCondition) {
                Write-Debug "$OperationName succeeded on attempt $attempt"
                return $result
            }
            
            Write-Verbose "$OperationName attempt ${attempt}: Condition not met, retrying..."
        }
        catch {
            Write-Verbose "$OperationName attempt $attempt failed: $($_.Exception.Message)"
        }

        if ($attempt -eq $MaxAttempts) {
            Write-Error "All $MaxAttempts attempts for $OperationName failed"
            throw "Failed to complete $OperationName after $MaxAttempts attempts"
        }

        Write-Debug "Waiting $delay seconds before next attempt"
        Start-Sleep -Seconds $delay
        $delay = [math]::Min($delay * $BackoffMultiplier, 60) # Cap at 60 seconds
        $attempt++
    }
}
