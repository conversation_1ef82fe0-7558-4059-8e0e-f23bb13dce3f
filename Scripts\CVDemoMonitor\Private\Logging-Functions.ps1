# Logging-Functions.ps1
# Contains functions related to logging

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('INFO', 'WARNING', 'ERROR', 'DEBUG')]
        [string]$Level = 'INFO',

        [Parameter(Mandatory = $false)]
        [int]$MaxLogSizeMB = 10,

        [Parameter(Mandatory = $false)]
        [switch]$NoConsole
    )

    try {
        # Check if we should log this message based on the global LogLevel
        $logLevelPriority = @{
            'ERROR' = 0
            'WARNING' = 1
            'INFO' = 2
            'DEBUG' = 3
        }

        # Skip logging if the message level is less important than the global level
        if ($logLevelPriority[$Level] -gt $logLevelPriority[$script:LogLevel]) {
            return
        }

        # Format timestamp and message
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logMessage = "[$timestamp] [$Level] $Message"

        # Write to console with appropriate color
        if (-not $NoConsole) {
            switch ($Level) {
                'ERROR' {
                    Write-Host $logMessage -ForegroundColor Red
                }
                'WARNING' {
                    Write-Host $logMessage -ForegroundColor Yellow
                }
                'INFO' {
                    Write-Host $logMessage -ForegroundColor White
                }
                'DEBUG' {
                    if ($VerbosePreference -eq 'Continue' -or $script:LogLevel -eq 'DEBUG') {
                        Write-Host $logMessage -ForegroundColor Gray
                    }
                }
                default {
                    Write-Host $logMessage
                }
            }
        }

        # Write to log file if path is defined
        if ($script:LogPath) {
            # Check if log rotation is needed
            if ((Test-Path $script:LogPath -ErrorAction SilentlyContinue) -and
                ((Get-Item $script:LogPath -ErrorAction SilentlyContinue).Length -gt ($MaxLogSizeMB * 1MB))) {

                # Rotate logs - keep up to 3 backups
                try {
                    if (Test-Path "$script:LogPath.2.bak" -ErrorAction SilentlyContinue) {
                        Remove-Item "$script:LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$script:LogPath.1.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$script:LogPath.1.bak" "$script:LogPath.2.bak" -Force -ErrorAction SilentlyContinue
                    }

                    if (Test-Path "$script:LogPath.bak" -ErrorAction SilentlyContinue) {
                        Rename-Item "$script:LogPath.bak" "$script:LogPath.1.bak" -Force -ErrorAction SilentlyContinue
                    }

                    Rename-Item $script:LogPath "$script:LogPath.bak" -Force -ErrorAction SilentlyContinue
                }
                catch {
                    # If rotation fails, just append to the existing log
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to rotate log files: $errorMsg" -ForegroundColor Yellow
                }
            }

            # Ensure log directory exists
            $logDir = Split-Path -Path $script:LogPath -Parent
            if (-not (Test-Path -Path $logDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                }
                catch {
                    # If we can't create the directory, just write to console
                    $errorMsg = $_.Exception.Message
                    Write-Host "Failed to create log directory: $errorMsg" -ForegroundColor Red
                    return
                }
            }

            # Write to log file with error handling
            try {
                Add-Content -Path $script:LogPath -Value $logMessage -ErrorAction Stop
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Host "Failed to write to log file: $errorMsg" -ForegroundColor Red
            }
        }
    }
    catch {
        # Last resort error handling - just try to output something
        $errorMsg = $_.Exception.Message
        Write-Host "Error in Write-Log function: $errorMsg" -ForegroundColor Red
        Write-Host $Message -ForegroundColor Cyan
    }
}
