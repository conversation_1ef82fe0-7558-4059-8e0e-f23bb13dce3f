# Service-Functions.ps1
# Contains functions related to service monitoring

function Test-ServiceStatus {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking service status for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        # Map the executable name to the actual Windows service name
        $windowsServiceName = switch ($config.AppName) {
            "RecordingServer64.exe" { "CompleteView Recording Server" }
            "AdminService64.exe" { "CompleteView Administrative Service" }
            "ManagementServer.exe" { "CompleteView Management Server" }
            default { $null }
        }

        if ($windowsServiceName) {
            try {
                $service = Get-Service -Name $windowsServiceName -ErrorAction Stop

                if ($service.Status -ne 'Running') {
                    Write-Log "$friendlyName service is not running (Status: $($service.Status))" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Service Not Running*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Status:* $($service.Status)
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent service status notification for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send service status notification for ${friendlyName}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
                else {
                    Write-Log "$friendlyName service is running" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error checking service status for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Could not determine Windows service name for $friendlyName" -Level 'WARNING'
        }
    }
}

function Measure-ResourceUsage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [int]$MemoryThresholdMB = 2048,  # 2GB

        [Parameter(Mandatory = $false)]
        [int]$CpuThresholdPercent = 80
    )

    Write-Log "Checking resource usage for monitored services" -Level 'INFO'

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $appName = $config.AppName
        $friendlyName = $config.FriendlyName
        $processName = $appName -replace '\.exe$', ''

        try {
            $process = Get-Process -Name $processName -ErrorAction SilentlyContinue

            if ($process) {
                # Check memory usage
                $memoryUsageMB = [Math]::Round($process.WorkingSet64 / 1MB, 2)
                Write-Log "$friendlyName memory usage: $memoryUsageMB MB" -Level 'INFO'

                # Check CPU usage (requires multiple samples)
                $cpuSamples = @()
                for ($i = 0; $i -lt 3; $i++) {
                    $startCPU = $process.TotalProcessorTime
                    $startTime = Get-Date
                    Start-Sleep -Seconds 2
                    $process.Refresh()
                    $endCPU = $process.TotalProcessorTime
                    $endTime = Get-Date
                    $cpuUsage = [Math]::Round(($endCPU - $startCPU).TotalSeconds / ($endTime - $startTime).TotalSeconds * 100 / [Environment]::ProcessorCount, 2)
                    $cpuSamples += $cpuUsage
                }

                $avgCpuUsage = ($cpuSamples | Measure-Object -Average).Average
                Write-Log "$friendlyName CPU usage: $avgCpuUsage%" -Level 'INFO'

                # Check if thresholds are exceeded
                $memoryExceeded = $memoryUsageMB -gt $MemoryThresholdMB
                $cpuExceeded = $avgCpuUsage -gt $CpuThresholdPercent

                if ($memoryExceeded -or $cpuExceeded) {
                    $alertMessage = "$friendlyName resource usage alert:"
                    if ($memoryExceeded) { $alertMessage += " Memory: $memoryUsageMB MB (threshold: $MemoryThresholdMB MB)" }
                    if ($cpuExceeded) { $alertMessage += " CPU: $avgCpuUsage% (threshold: $CpuThresholdPercent%)" }

                    Write-Log $alertMessage -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *High Resource Usage*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
*Memory:* $memoryUsageMB MB (threshold: $MemoryThresholdMB MB)
*CPU:* $avgCpuUsage% (threshold: $CpuThresholdPercent%)"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent resource usage alert for $friendlyName" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send resource usage alert for ${friendlyName}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
            }
            else {
                Write-Log "Process $processName not found - service may not be running" -Level 'WARNING'
            }
        }
        catch {
            $errorMsg = $_.Exception.Message
            Write-Log "Error checking resource usage for ${friendlyName}: $errorMsg" -Level 'ERROR'
        }
    }
}

function Test-DiskSpace {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [string[]]$PathsToCheck = @(
            "C:\Program Files\Salient Security Platform",
            "C:\ProgramData\Salient Security Platform"
        ),

        [Parameter(Mandatory = $false)]
        [int]$ThresholdPercent = 10,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false
    )

    Write-Log "Checking disk space for critical paths" -Level 'INFO'

    $checkedDrives = @{}

    foreach ($path in $PathsToCheck) {
        if (Test-Path $path) {
            $drive = (Get-Item $path).PSDrive.Name

            # Skip if we've already checked this drive
            if ($checkedDrives.ContainsKey($drive)) {
                continue
            }

            $checkedDrives[$drive] = $true

            try {
                $driveInfo = Get-PSDrive -Name $drive -PSProvider FileSystem
                $freeSpaceGB = [Math]::Round($driveInfo.Free / 1GB, 2)
                $totalSpaceGB = [Math]::Round(($driveInfo.Free + $driveInfo.Used) / 1GB, 2)
                $freePercent = [Math]::Round(($driveInfo.Free / ($driveInfo.Free + $driveInfo.Used)) * 100, 2)

                Write-Log "Drive ${drive}: has $freeSpaceGB GB free ($freePercent%)" -Level 'INFO'

                if ($freePercent -lt $ThresholdPercent) {
                    Write-Log "Low disk space on drive ${drive}: ($freePercent% free, threshold: $ThresholdPercent%)" -Level 'WARNING'

                    # Send notification if Slack is enabled
                    if (-not $NoSlack -and $WebhookUrl) {
                        $messageText = ":warning: *Low Disk Space*

*Host:* $env:COMPUTERNAME
*Drive:* ${drive}:
*Free Space:* $freeSpaceGB GB of $totalSpaceGB GB ($freePercent%)
*Threshold:* $ThresholdPercent%
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

                        try {
                            Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                            Write-Log "Sent low disk space notification for drive ${drive}:" -Level 'INFO'
                        }
                        catch {
                            $errorMsg = $_.Exception.Message
                            Write-Log "Failed to send low disk space notification for drive ${drive}: $errorMsg" -Level 'ERROR'
                        }
                    }
                }
            }
            catch {
                Write-Log "Error checking disk space for drive ${drive}: $_" -Level 'ERROR'
            }
        }
        else {
            Write-Log "Path not found: $path" -Level 'WARNING'
        }
    }
}
