<#
.SYNOPSIS
    Displays the advanced options menu.

.DESCRIP<PERSON>ON
    Shows the advanced options menu with additional configuration options.

.EXAMPLE
    Show-AdvancedOptions
#>
function Show-AdvancedOptions {
    [CmdletBinding()]
    param ()

    while ($true) {
        Clear-Host
        $currentServer = (Get-PAServer).Name
        Write-Host "=== Advanced Options ===`n"
        Write-Host "1) Change ACME server (current: $currentServer)"
        Write-Host "2) Manage saved settings"
        Write-Host "3) Security options"
        Write-Host "4) Export logs"
        Write-Host "0) Back"
        $advancedChoice = Read-Host "`nEnter your choice (0-4)"
        switch ($advancedChoice) {
            '0' { return }
            '1' { Set-ACMEServer }
            '2' { Show-SettingsMenu }
            '3' {
                # Security options
                Show-SecurityOptions
            }
            '4' {
                # Export logs
                $exportPath = Export-Logs
                if ($exportPath) {
                    Write-Host "Logs exported to: $exportPath" -ForegroundColor Green
                } else {
                    Write-Warning "Failed to export logs."
                }
            }
            default { Write-Warning "`nInvalid selection. Please choose 0-4." }
        }
        Read-Host "`nPress Enter to return to the advanced options"
    }
}
