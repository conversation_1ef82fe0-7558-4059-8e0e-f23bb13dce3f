# Function to get available installation targets
function Get-InstallationTargets {
    [CmdletBinding()]
    param()
    
    $targets = @(
        @{
            Id = "LocalStore"
            Name = "Local Certificate Store"
            Description = "Install to the local machine's certificate store"
            IsAvailable = $true
            RequiresAdmin = $true
        },
        @{
            Id = "PEM"
            Name = "PEM Files"
            Description = "Export as PEM certificate and key files"
            IsAvailable = $true
            RequiresAdmin = $false
        },
        @{
            Id = "PFX"
            Name = "PFX File"
            Description = "Export as a PFX/PKCS#12 file"
            IsAvailable = $true
            RequiresAdmin = $false
        },
        @{
            Id = "RecordingServer"
            Name = "CompleteView Recording Server"
            Description = "Install to CompleteView Recording Server"
            IsAvailable = (Test-Path (Get-RSCertFolder))
            RequiresAdmin = $true
        },
        @{
            Id = "ManagementServer"
            Name = "CompleteView Management Server"
            Description = "Install to CompleteView Management Server"
            IsAvailable = (Get-Module -ListAvailable -Name WebAdministration) -ne $null
            RequiresAdmin = $true
        },
        @{
            Id = "Nginx"
            Name = "Nginx Web Server"
            Description = "Install to Nginx web server"
            IsAvailable = (Get-Command nginx -ErrorAction SilentlyContinue) -ne $null
            RequiresAdmin = $true
        },
        @{
            Id = "Apache"
            Name = "Apache Web Server"
            Description = "Install to Apache web server"
            IsAvailable = (Test-Path "C:\Apache*\conf" -ErrorAction SilentlyContinue) -or (Test-Path "C:\Program Files\Apache*\conf" -ErrorAction SilentlyContinue)
            RequiresAdmin = $true
        }
    )
    
    return $targets
}

# Function to check if OpenSSL is available
function Test-OpenSSLAvailable {
    [CmdletBinding()]
    param()
    
    try {
        $opensslVersion = Invoke-WithRetry -ScriptBlock {
            $process = Start-Process -FilePath "openssl" -ArgumentList "version" -NoNewWindow -Wait -PassThru -RedirectStandardOutput "$env:TEMP\openssl_version.txt"
            if ($process.ExitCode -eq 0) {
                return Get-Content "$env:TEMP\openssl_version.txt"
            }
            return $null
        } -MaxAttempts 1 -InitialDelaySeconds 1 -OperationName "OpenSSL version check"
        
        if ($opensslVersion) {
            Write-Verbose "OpenSSL is available: $opensslVersion"
            return $true
        }
    } catch {
        Write-Verbose "OpenSSL is not available: $($_.Exception.Message)"
    }
    
    return $false
}

# Function to install certificate to Nginx
function Install-CertificateToNginx {
    [CmdletBinding(SupportsShouldProcess=$true)]
    param(
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        
        [Parameter()]
        [string]$ConfigPath,
        
        [Parameter()]
        [switch]$RestartService
    )
    
    try {
        # Get certificate content
        $certContent = Get-CertificatePEMContent -Certificate $Certificate -IncludeKey
        if (-not $certContent.Success) {
            throw "Failed to get certificate content: $($certContent.ErrorMessage)"
        }
        
        # Determine Nginx configuration path if not provided
        if (-not $ConfigPath) {
            $nginxCommand = Get-Command nginx -ErrorAction Stop
            $nginxPath = Split-Path -Parent $nginxCommand.Source
            $possibleConfigPaths = @(
                (Join-Path -Path $nginxPath -ChildPath "conf"),
                (Join-Path -Path $nginxPath -ChildPath "etc\nginx"),
                (Join-Path -Path $nginxPath -ChildPath "conf\nginx")
            )
            
            foreach ($path in $possibleConfigPaths) {
                if (Test-Path $path) {
                    $ConfigPath = $path
                    break
                }
            }
            
            if (-not $ConfigPath) {
                throw "Could not determine Nginx configuration path. Please specify the path manually."
            }
        }
        
        # Create certificate directory if it doesn't exist
        $certDir = Join-Path -Path $ConfigPath -ChildPath "certs"
        if (-not (Test-Path $certDir)) {
            New-Item -ItemType Directory -Path $certDir -Force | Out-Null
        }
        
        # Save certificate and key files
        $domain = $Certificate.MainDomain
        $certFile = Join-Path -Path $certDir -ChildPath "$domain.crt"
        $keyFile = Join-Path -Path $certDir -ChildPath "$domain.key"
        
        Set-Content -Path $certFile -Value $certContent.CertContent -Encoding ASCII
        Set-Content -Path $keyFile -Value $certContent.KeyContent -Encoding ASCII
        
        Write-Host "`nCertificate installed to Nginx:" -ForegroundColor Green
        Write-Host "Certificate: $certFile"
        Write-Host "Private Key: $keyFile"
        
        # Provide configuration example
        Write-Host "`nAdd the following to your Nginx server block:" -ForegroundColor Cyan
        Write-Host "server {" -ForegroundColor Yellow
        Write-Host "    listen 443 ssl;" -ForegroundColor Yellow
        Write-Host "    server_name $domain;" -ForegroundColor Yellow
        Write-Host "    ssl_certificate $certFile;" -ForegroundColor Yellow
        Write-Host "    ssl_certificate_key $keyFile;" -ForegroundColor Yellow
        Write-Host "    # ... other configuration ..." -ForegroundColor Yellow
        Write-Host "}" -ForegroundColor Yellow
        
        Write-Log "Certificate for $domain installed to Nginx at $certDir" -Level 'Success'
        
        # Restart Nginx if requested
        if ($RestartService -and $PSCmdlet.ShouldProcess("Nginx service", "Restart")) {
            $process = Start-Process -FilePath "nginx" -ArgumentList "-s reload" -NoNewWindow -Wait -PassThru
            if ($process.ExitCode -eq 0) {
                Write-Host "`nNginx service reloaded successfully." -ForegroundColor Green
                Write-Log "Nginx service reloaded after certificate installation." -Level 'Success'
            } else {
                Write-Warning "Failed to reload Nginx service. Please reload it manually."
                Write-Log "Failed to reload Nginx service after certificate installation." -Level 'Warning'
            }
        }
        
        return $true
    } catch {
        Write-Error "Failed to install certificate to Nginx: $($_)"
        Write-Log "Failed to install certificate to Nginx: $($_)" -Level 'Error'
        return $false
    }
}

# Function to install certificate to Apache
function Install-CertificateToApache {
    [CmdletBinding(SupportsShouldProcess=$true)]
    param(
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        
        [Parameter()]
        [string]$ConfigPath,
        
        [Parameter()]
        [switch]$RestartService
    )
    
    try {
        # Get certificate content
        $certContent = Get-CertificatePEMContent -Certificate $Certificate -IncludeKey
        if (-not $certContent.Success) {
            throw "Failed to get certificate content: $($certContent.ErrorMessage)"
        }
        
        # Determine Apache configuration path if not provided
        if (-not $ConfigPath) {
            $possibleConfigPaths = @(
                "C:\Apache24\conf",
                "C:\Apache\conf",
                "C:\Program Files\Apache Software Foundation\Apache2.4\conf",
                "C:\Program Files\Apache Software Foundation\Apache2.2\conf"
            )
            
            foreach ($path in $possibleConfigPaths) {
                if (Test-Path $path) {
                    $ConfigPath = $path
                    break
                }
            }
            
            if (-not $ConfigPath) {
                throw "Could not determine Apache configuration path. Please specify the path manually."
            }
        }
        
        # Create certificate directory if it doesn't exist
        $certDir = Join-Path -Path $ConfigPath -ChildPath "ssl"
        if (-not (Test-Path $certDir)) {
            New-Item -ItemType Directory -Path $certDir -Force | Out-Null
        }
        
        # Save certificate and key files
        $domain = $Certificate.MainDomain
        $certFile = Join-Path -Path $certDir -ChildPath "$domain.crt"
        $keyFile = Join-Path -Path $certDir -ChildPath "$domain.key"
        
        Set-Content -Path $certFile -Value $certContent.CertContent -Encoding ASCII
        Set-Content -Path $keyFile -Value $certContent.KeyContent -Encoding ASCII
        
        Write-Host "`nCertificate installed to Apache:" -ForegroundColor Green
        Write-Host "Certificate: $certFile"
        Write-Host "Private Key: $keyFile"
        
        # Provide configuration example
        Write-Host "`nAdd the following to your Apache virtual host configuration:" -ForegroundColor Cyan
        Write-Host "<VirtualHost *:443>" -ForegroundColor Yellow
        Write-Host "    ServerName $domain" -ForegroundColor Yellow
        Write-Host "    SSLEngine on" -ForegroundColor Yellow
        Write-Host "    SSLCertificateFile $certFile" -ForegroundColor Yellow
        Write-Host "    SSLCertificateKeyFile $keyFile" -ForegroundColor Yellow
        Write-Host "    # ... other configuration ..." -ForegroundColor Yellow
        Write-Host "</VirtualHost>" -ForegroundColor Yellow
        
        Write-Log "Certificate for $domain installed to Apache at $certDir" -Level 'Success'
        
        # Restart Apache if requested
        if ($RestartService -and $PSCmdlet.ShouldProcess("Apache service", "Restart")) {
            $apacheServices = Get-Service -Name "Apache*" -ErrorAction SilentlyContinue
            if ($apacheServices) {
                foreach ($service in $apacheServices) {
                    Restart-Service -Name $service.Name -Force
                    Write-Host "`nApache service '$($service.Name)' restarted." -ForegroundColor Green
                    Write-Log "Apache service '$($service.Name)' restarted after certificate installation." -Level 'Success'
                }
            } else {
                Write-Warning "No Apache service found. Please restart it manually."
                Write-Log "No Apache service found for restart after certificate installation." -Level 'Warning'
            }
        }
        
        return $true
    } catch {
        Write-Error "Failed to install certificate to Apache: $($_)"
        Write-Log "Failed to install certificate to Apache: $($_)" -Level 'Error'
        return $false
    }
}

# Function to validate certificate chain
function Test-CertificateChain {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [object]$Certificate
    )
    
    try {
        # Get the X509Certificate2 object
        $x509Cert = $Certificate.Certificate
        
        # Create a new X509Chain object
        $chain = New-Object System.Security.Cryptography.X509Certificates.X509Chain
        $chain.ChainPolicy.RevocationMode = [System.Security.Cryptography.X509Certificates.X509RevocationMode]::Online
        $chain.ChainPolicy.RevocationFlag = [System.Security.Cryptography.X509Certificates.X509RevocationFlag]::EntireChain
        
        # Build the chain
        $isValid = $chain.Build($x509Cert)
        
        $result = @{
            IsValid = $isValid
            Errors = @()
        }
        
        # If the chain is not valid, get the errors
        if (-not $isValid) {
            foreach ($status in $chain.ChainStatus) {
                $result.Errors += "$($status.StatusInformation.Trim()) ($($status.Status))"
            }
        }
        
        return $result
    } catch {
        Write-Error "Failed to validate certificate chain: $($_)"
        Write-Log "Failed to validate certificate chain: $($_)" -Level 'Error'
        return @{
            IsValid = $false
            Errors = @("Exception: $($_.Exception.Message)")
        }
    } finally {
        if ($chain) {
            $chain.Dispose()
        }
    }
}
