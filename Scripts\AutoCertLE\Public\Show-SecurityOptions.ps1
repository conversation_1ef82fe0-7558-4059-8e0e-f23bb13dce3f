<#
.SYNOPSIS
    Displays the security options menu.

.DESCRIP<PERSON><PERSON>
    Shows the security options menu with certificate and account security features.

.EXAMPLE
    Show-SecurityOptions
#>
function Show-SecurityOptions {
    [CmdletBinding()]
    param ()
    
    while ($true) {
        Clear-Host
        Write-Host "=== Security Options ===`n"
        Write-Host "1) Backup ACME account key"
        Write-Host "2) Restore ACME account key"
        Write-Host "3) Validate certificate key security"
        Write-Host "4) Manage secure credentials"
        Write-Host "0) Back"
        
        $securityChoice = Read-Host "`nEnter your choice (0-4)"
        
        switch ($securityChoice) {
            '0' { return }
            '1' {
                # Backup ACME account key
                $backupPath = Read-Host "`nEnter backup file path (leave blank for default)"
                if (-not $backupPath) {
                    $backupPath = Join-Path -Path ([Environment]::GetFolderPath("Desktop")) -ChildPath "ACME_Account_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').zip"
                }
                
                $password = Read-Host -AsSecureString "Enter a password to protect the backup"
                $result = Backup-ACMEAccountKey -BackupPath $backupPath -Password $password
                
                if ($result) {
                    Write-Host "`nACME account key backed up successfully to: $result" -ForegroundColor Green
                } else {
                    Write-Warning "`nFailed to backup ACME account key."
                }
            }
            '2' {
                # Restore ACME account key
                $backupPath = Read-Host "`nEnter backup file path"
                if (-not $backupPath -or -not (Test-Path $backupPath)) {
                    Write-Warning "Invalid backup file path."
                } else {
                    $password = Read-Host -AsSecureString "Enter the backup password"
                    $result = Restore-ACMEAccountKey -BackupPath $backupPath -Password $password
                    
                    if ($result) {
                        Write-Host "`nACME account key restored successfully." -ForegroundColor Green
                    } else {
                        Write-Warning "`nFailed to restore ACME account key."
                    }
                }
            }
            '3' {
                # Validate certificate key security
                $certificates = Get-ExistingCertificates
                
                if ($certificates.Count -eq 0) {
                    Write-Warning "`nNo certificates found."
                } else {
                    Write-Host "`nSelect a certificate to validate:"
                    for ($i = 0; $i -lt $certificates.Count; $i++) {
                        Write-Host "$($i + 1)) $($certificates[$i].MainDomain)"
                    }
                    
                    $certChoice = Read-Host "`nEnter certificate number (1-$($certificates.Count))"
                    if ($certChoice -match '^\d+$' -and [int]$certChoice -ge 1 -and [int]$certChoice -le $certificates.Count) {
                        $selectedCert = $certificates[[int]$certChoice - 1]
                        $validation = Test-CertificateKeySecurity -Certificate $selectedCert
                        
                        Write-Host "`nCertificate Key Security Validation:" -ForegroundColor Cyan
                        Write-Host "Certificate: $($selectedCert.MainDomain)"
                        Write-Host "Key Type: $($validation.KeyType)"
                        Write-Host "Key Size: $($validation.KeySize)"
                        Write-Host "Security Status: $(if ($validation.IsValid) { 'Valid' } else { 'Invalid' })"
                        
                        if ($validation.Warnings.Count -gt 0) {
                            Write-Host "`nWarnings:" -ForegroundColor Yellow
                            foreach ($warning in $validation.Warnings) {
                                Write-Host "- $warning" -ForegroundColor Yellow
                            }
                        } else {
                            Write-Host "`nNo security issues found." -ForegroundColor Green
                        }
                    } else {
                        Write-Warning "`nInvalid selection."
                    }
                }
            }
            '4' {
                # Manage secure credentials
                Show-CredentialManager
            }
            default { Write-Warning "`nInvalid selection. Please choose 0-4." }
        }
        
        Read-Host "`nPress Enter to return to the security options"
    }
}

<#
.SYNOPSIS
    Displays the credential manager.

.DESCRIPTION
    Shows the credential manager for storing and retrieving secure credentials.

.EXAMPLE
    Show-CredentialManager
#>
function Show-CredentialManager {
    [CmdletBinding()]
    param ()
    
    while ($true) {
        Clear-Host
        Write-Host "=== Credential Manager ===`n"
        Write-Host "1) Store new credential"
        Write-Host "2) View stored credentials"
        Write-Host "3) Remove credential"
        Write-Host "0) Back"
        
        $credChoice = Read-Host "`nEnter your choice (0-3)"
        
        switch ($credChoice) {
            '0' { return }
            '1' {
                # Store new credential
                $credName = Read-Host "`nEnter credential name"
                if (-not $credName) {
                    Write-Warning "Credential name cannot be empty."
                    continue
                }
                
                $username = Read-Host "Enter username"
                $password = Read-Host -AsSecureString "Enter password"
                
                $result = Set-SecureCredential -CredentialName $credName -Username $username -Password $password
                
                if ($result) {
                    Write-Host "`nCredential '$credName' stored successfully." -ForegroundColor Green
                } else {
                    Write-Warning "`nFailed to store credential."
                }
            }
            '2' {
                # View stored credentials
                $credPath = "$env:LOCALAPPDATA\PoshACME\credentials"
                if (-not (Test-Path $credPath)) {
                    Write-Warning "`nNo stored credentials found."
                    continue
                }
                
                $credFiles = Get-ChildItem -Path $credPath -Filter "*.xml"
                if ($credFiles.Count -eq 0) {
                    Write-Warning "`nNo stored credentials found."
                    continue
                }
                
                Write-Host "`nStored Credentials:" -ForegroundColor Cyan
                foreach ($file in $credFiles) {
                    $credName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
                    $cred = Get-SecureCredential -CredentialName $credName
                    Write-Host "- $credName (Username: $($cred.UserName))"
                }
            }
            '3' {
                # Remove credential
                $credPath = "$env:LOCALAPPDATA\PoshACME\credentials"
                if (-not (Test-Path $credPath)) {
                    Write-Warning "`nNo stored credentials found."
                    continue
                }
                
                $credFiles = Get-ChildItem -Path $credPath -Filter "*.xml"
                if ($credFiles.Count -eq 0) {
                    Write-Warning "`nNo stored credentials found."
                    continue
                }
                
                Write-Host "`nSelect credential to remove:"
                for ($i = 0; $i -lt $credFiles.Count; $i++) {
                    $credName = [System.IO.Path]::GetFileNameWithoutExtension($credFiles[$i].Name)
                    Write-Host "$($i + 1)) $credName"
                }
                
                $removeChoice = Read-Host "`nEnter credential number (1-$($credFiles.Count))"
                if ($removeChoice -match '^\d+$' -and [int]$removeChoice -ge 1 -and [int]$removeChoice -le $credFiles.Count) {
                    $credName = [System.IO.Path]::GetFileNameWithoutExtension($credFiles[[int]$removeChoice - 1].Name)
                    $result = Remove-SecureCredential -CredentialName $credName
                    
                    if ($result) {
                        Write-Host "`nCredential '$credName' removed successfully." -ForegroundColor Green
                    } else {
                        Write-Warning "`nFailed to remove credential."
                    }
                } else {
                    Write-Warning "`nInvalid selection."
                }
            }
            default { Write-Warning "`nInvalid selection. Please choose 0-3." }
        }
        
        Read-Host "`nPress Enter to return to the credential manager"
    }
}
