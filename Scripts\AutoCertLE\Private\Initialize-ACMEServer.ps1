# Ensure the ACME server is set
function Initialize-ACMEServer {
    [CmdletBinding()]
    param()
    
    Write-Debug "Checking ACME server configuration"
    if (-not (Get-PAServer)) {
        Write-Verbose "No ACME server configured. Setting to Let's Encrypt Production."
        Set-PAServer LE_PROD
        Write-Debug "ACME server set successfully"
    } else {
        Write-Debug "ACME server already configured"
    }
}
