<#
.SYNOPSIS
    Displays existing certificates managed by Posh-ACME.

.DESCRIPTION
    Lists all certificates managed by Posh-ACME, including their expiration dates
    and renewal status.

.EXAMPLE
    Get-ExistingCertificates
#>
function Get-ExistingCertificates {
    [CmdletBinding()]
    param ()

    # Ensure the ACME server is set
    Initialize-ACMEServer

    # Get all orders
    $orders = Get-PAOrder
    if (-not $orders) {
        Write-Host "`nNo certificates found." -ForegroundColor Yellow
        Read-Host "`nPress Enter to return to the main menu"
        return
    }

    # Display certificates
    Write-Host "`nExisting Certificates:" -ForegroundColor Cyan
    Write-Host "------------------------"

    $i = 1
    $certList = @()
    foreach ($order in $orders) {
        try {
            $cert = Get-CachedPACertificate -MainDomain $order.MainDomain
            
            # Calculate days until expiration
            $daysRemaining = [math]::Ceiling(($cert.Certificate.NotAfter - (Get-Date)).TotalDays)
            
            # Determine status color
            if ($daysRemaining -le 0) {
                $statusColor = "Red"
                $status = "Expired"
            } elseif ($daysRemaining -le 7) {
                $statusColor = "Red"
                $status = "Critical"
            } elseif ($daysRemaining -le 30) {
                $statusColor = "Yellow"
                $status = "Warning"
            } else {
                $statusColor = "Green"
                $status = "Valid"
            }
            
            # Add to certificate list
            $certList += [PSCustomObject]@{
                Index = $i
                MainDomain = $order.MainDomain
                Expires = $cert.Certificate.NotAfter
                DaysRemaining = $daysRemaining
                Status = $status
                StatusColor = $statusColor
                Order = $order
                Certificate = $cert
            }
            
            $i++
        } catch {
            Write-Warning "Error retrieving certificate for $($order.MainDomain): $($_)"
            Write-Log "Error retrieving certificate for $($order.MainDomain): $($_)" -Level 'Warning'
        }
    }

    # Display the certificates in a table
    foreach ($cert in $certList) {
        Write-Host "$($cert.Index)) $($cert.MainDomain)" -NoNewline
        Write-Host " - Expires: $($cert.Expires.ToString('yyyy-MM-dd'))" -NoNewline
        Write-Host " - Status: " -NoNewline
        Write-Host $cert.Status -ForegroundColor $cert.StatusColor
    }

    Write-Host "0) Back"

    # Prompt for action
    $choice = Get-ValidatedInput -Prompt "`nSelect a certificate to view details or 0 to go back" -ValidOptions (1..$certList.Count)
    if ($choice -eq 0) {
        return
    }

    # Get the selected certificate
    $selectedCert = $certList[$choice - 1]
    
    # Display certificate details
    Write-Host "`nCertificate Details:" -ForegroundColor Cyan
    Write-Host "-------------------"
    Write-Host "Domain: $($selectedCert.MainDomain)"
    Write-Host "Issuer: $($selectedCert.Certificate.Certificate.Issuer)"
    Write-Host "Valid From: $($selectedCert.Certificate.Certificate.NotBefore.ToString('yyyy-MM-dd HH:mm:ss'))"
    Write-Host "Valid Until: $($selectedCert.Certificate.Certificate.NotAfter.ToString('yyyy-MM-dd HH:mm:ss'))"
    Write-Host "Days Remaining: $($selectedCert.DaysRemaining)"
    Write-Host "Status: " -NoNewline
    Write-Host $selectedCert.Status -ForegroundColor $selectedCert.StatusColor
    
    # Display subject alternative names
    $sans = $selectedCert.Certificate.Certificate.DnsNameList
    if ($sans.Count -gt 0) {
        Write-Host "`nSubject Alternative Names:"
        foreach ($san in $sans) {
            Write-Host "- $san"
        }
    }
    
    # Display certificate actions
    Write-Host "`nActions:" -ForegroundColor Cyan
    Write-Host "1) Install certificate"
    Write-Host "2) Renew certificate"
    Write-Host "3) Revoke certificate"
    Write-Host "4) Delete certificate"
    Write-Host "0) Back"
    
    $actionChoice = Get-ValidatedInput -Prompt "`nEnter your choice (0-4)" -ValidOptions 1,2,3,4
    switch ($actionChoice) {
        0 { Get-ExistingCertificates }  # Go back to certificate list
        1 { Install-Certificate -PACertificate $selectedCert.Certificate }
        2 {
            try {
                Write-Host "`nRenewing certificate for $($selectedCert.MainDomain)..." -ForegroundColor Yellow
                $newCert = Submit-Renewal -MainDomain $selectedCert.MainDomain -Force
                
                if ($newCert) {
                    Write-Host "`nCertificate renewed successfully." -ForegroundColor Green
                    Write-Log "Certificate for $($selectedCert.MainDomain) renewed successfully." -Level 'Success'
                    
                    # Prompt to install the renewed certificate
                    $installRenewed = Read-Host "`nDo you want to install the renewed certificate? (Y/N)"
                    if ($installRenewed -match '^[Yy]$') {
                        Install-Certificate -PACertificate $newCert
                    }
                } else {
                    Write-Error "Failed to renew certificate."
                    Write-Log "Failed to renew certificate for $($selectedCert.MainDomain)." -Level 'Error'
                }
            } catch {
                Write-Error "Error renewing certificate: $($_)"
                Write-Log "Error renewing certificate for $($selectedCert.MainDomain): $($_)" -Level 'Error'
            }
            Read-Host "`nPress Enter to return to the certificate list"
            Get-ExistingCertificates
        }
        3 { Revoke-Certificate -MainDomain $selectedCert.MainDomain }
        4 { Remove-Certificate -MainDomain $selectedCert.MainDomain }
    }
}
