# Get next file version from Recording Server certificate folder
function Get-NextFileVersion {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$folderPath,
        
        [Parameter(Mandatory = $true)]
        [string]$baseName, # 'cert' or 'pvkey'
        
        [Parameter()]
        [string]$extension = ".pem"
    )
    $latestVersion = -1
    $files = Get-ChildItem -Path $folderPath -Filter "$baseName*${extension}"
    foreach ($file in $files) {
        if ($file.Name -match "${baseName}(\d+)$extension") {
            [int]$versionNumber = $Matches[1]
            if ($versionNumber -gt $latestVersion) {
                $latestVersion = $versionNumber
            }
        }
    }
    return ($latestVersion + 1).ToString("D3")
}

# Get Recording Server certificate folder path
function Get-RSCertFolder {
    [CmdletBinding()]
    param ()
    $certFolderPaths = @(
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
        "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates"
    )
    foreach ($path in $certFolderPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    Write-Error "Failed to find any predefined certificate folders."
    Write-Log "Failed to find any predefined certificate folders." -Level 'Error'
    return $null
}

# Save PEM files with auto-versioning
function Save-PEMFiles {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$directory,
        
        [Parameter(Mandatory = $true)]
        [string]$certContent,
        
        [Parameter(Mandatory = $true)]
        [string]$keyContent,
        
        [Parameter()]
        [switch]$NoVersioning
    )

    Write-Debug "Saving PEM files to $directory"
    if (-not (Test-Path $directory)) {
        $msg = "Certificate directory does not exist: '$directory'"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        return $null
    }

    try {
        # Get next version with retry for file system operations
        if (-not $NoVersioning) {
            $certVersion = Invoke-WithRetry -ScriptBlock {
                Get-NextFileVersion -folderPath $directory -baseName "cert"
            } -MaxAttempts 3 -InitialDelaySeconds 1 `
              -OperationName "Version number generation"

            $certOutputFile = Join-Path -Path $directory -ChildPath ("cert" + $certVersion + ".pem")
            $keyOutputFile = Join-Path -Path $directory -ChildPath ("pvkey" + $certVersion + ".pem")
        } else {
            $certOutputFile = Join-Path -Path $directory -ChildPath "cert.pem"
            $keyOutputFile = Join-Path -Path $directory -ChildPath "pvkey.pem"
        }

        # Save files with retry for locked files
        Invoke-WithRetry -ScriptBlock {
            Set-Content -Path $certOutputFile -Value $certContent -Encoding ascii -ErrorAction Stop
            Set-Content -Path $keyOutputFile -Value $keyContent -Encoding ascii -ErrorAction Stop
        } -MaxAttempts 5 -InitialDelaySeconds 2 `
          -OperationName "PEM file save" `
          -SuccessCondition { Test-Path $certOutputFile -and Test-Path $keyOutputFile }

        return @{
            CertFile = $certOutputFile
            KeyFile = $keyOutputFile
        }
    } catch {
        $msg = "Failed to save PEM files to '$directory' after multiple attempts. Certificate: '$certOutputFile', Key: '$keyOutputFile'. Error: $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        throw
    }
}

# Function to get certificate PEM content
function Get-CertificatePEMContent {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        
        [Parameter()]
        [switch]$IncludeKey
    )

    $result = @{
        CertContent = $null
        KeyContent = $null
        Success = $false
        ErrorMessage = $null
    }

    try {
        # Get certificate content
        if ($Certificate.CertificatePEM) {
            $result.CertContent = Get-Content -Path $Certificate.CertificatePEM -Raw
        } elseif ($Certificate.PEM) {
            $result.CertContent = $Certificate.PEM
        } elseif ($Certificate.CertFile) {
            $result.CertContent = Get-Content -Path $Certificate.CertFile -Raw
        } else {
            throw "Unable to retrieve PEM content from certificate object."
        }

        # Get key content if requested
        if ($IncludeKey) {
            if ($Certificate.KeyFile) {
                $result.KeyContent = Get-Content -Path $Certificate.KeyFile -Raw
            } else {
                throw "Unable to retrieve key content from certificate object."
            }
        }

        $result.Success = $true
    } catch {
        $result.ErrorMessage = $_.Exception.Message
        Write-Error $result.ErrorMessage
        Write-Log $result.ErrorMessage -Level 'Error'
    }

    return $result
}
