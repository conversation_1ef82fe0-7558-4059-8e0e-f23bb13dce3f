@{
    # Script module or binary module file associated with this manifest.
    RootModule = 'CVDemoMonitor.psm1'

    # Version number of this module.
    ModuleVersion = '1.0.0'

    # ID used to uniquely identify this module
    GUID = '8f7c5550-6a57-4d6c-9f8e-c89f5b32a1a0'

    # Author of this module
    Author = '<EMAIL>'

    # Company or vendor of this module
    CompanyName = 'Salient Systems'

    # Copyright statement for this module
    Copyright = '(c) 2023 Salient Systems. All rights reserved.'

    # Description of the functionality provided by this module
    Description = 'Comprehensive monitoring solution for CompleteView services with Slack alerting capabilities and resilient operation.'

    # Minimum version of the Windows PowerShell engine required by this module
    PowerShellVersion = '5.1'

    # Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
    FunctionsToExport = @('Start-CVMonitoring')

    # Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
    CmdletsToExport = @()

    # Variables to export from this module
    VariablesToExport = '*'

    # Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
    AliasesToExport = @()

    # Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
    PrivateData = @{
        PSData = @{
            # Tags applied to this module. These help with module discovery in online galleries.
            Tags = @('Monitoring', 'CompleteView', 'Salient')

            # A URL to the license for this module.
            # LicenseUri = ''

            # A URL to the main website for this project.
            # ProjectUri = ''

            # A URL to an icon representing this module.
            # IconUri = ''

            # ReleaseNotes of this module
            ReleaseNotes = 'Initial release of the CVDemoMonitor module'
        }
    }
}
