# Functions for Salient CompleteView certificate management

# Function to detect server type
function Get-SalientServerType {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$ComputerName = $env:COMPUTERNAME
    )

    try {
        $result = @{
            IsManagementServer = $false
            IsRecordingServer = $false
            ServerName = $ComputerName
        }

        # Check for local services if no computer name is specified or it's the local machine
        if ($ComputerName -eq $env:COMPUTERNAME) {
            $mgmtService = Get-Service -Name "CompleteView Management Server" -ErrorAction SilentlyContinue
            $recService = Get-Service -Name "CompleteView Recording Server" -ErrorAction SilentlyContinue

            if ($mgmtService) {
                $result.IsManagementServer = $true
                Write-Verbose "Management Server service detected on local machine"
            }

            if ($recService) {
                $result.IsRecordingServer = $true
                Write-Verbose "Recording Server service detected on local machine"
            }
        } else {
            # Check for remote services
            try {
                $mgmtService = Get-Service -Name "CompleteView Management Server" -ComputerName $ComputerName -ErrorAction Stop
                $result.IsManagementServer = $true
                Write-Verbose "Management Server service detected on $ComputerName"
            } catch {
                Write-Verbose "Management Server service not found on $ComputerName"
            }

            try {
                $recService = Get-Service -Name "CompleteView Recording Server" -ComputerName $ComputerName -ErrorAction Stop
                $result.IsRecordingServer = $true
                Write-Verbose "Recording Server service detected on $ComputerName"
            } catch {
                Write-Verbose "Recording Server service not found on $ComputerName"
            }
        }

        return $result
    } catch {
        Write-Error "Error detecting Salient server type: $($_.Exception.Message)"
        Write-Log "Error detecting Salient server type: $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to get Recording Server certificate folder
function Get-RecordingServerCertFolder {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$ComputerName = $env:COMPUTERNAME
    )

    try {
        $possiblePaths = @(
            "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
            "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates",
            "C:\Program Files\Symmetry Security Platform\Symmetry CompleteView\Recording Server\Certificates"
        )

        # For local machine
        if ($ComputerName -eq $env:COMPUTERNAME) {
            foreach ($path in $possiblePaths) {
                if (Test-Path $path) {
                    Write-Verbose "Found Recording Server certificate folder: $path"
                    return $path
                }
            }
        } else {
            # For remote machine, check using UNC path
            foreach ($path in $possiblePaths) {
                $uncPath = "\\$ComputerName\$(($path -replace ':' , '$'))"
                if (Test-Path $uncPath) {
                    Write-Verbose "Found Recording Server certificate folder on $ComputerName - $uncPath"
                    return $uncPath
                }
            }
        }

        Write-Warning "Recording Server certificate folder not found"
        return $null
    } catch {
        Write-Error "Error finding Recording Server certificate folder: $($_.Exception.Message)"
        Write-Log "Error finding Recording Server certificate folder: $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to get next available certificate number for Recording Server
function Get-NextCertificateNumber {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$CertFolder
    )

    try {
        if (-not (Test-Path $CertFolder)) {
            Write-Warning "Certificate folder does not exist: $CertFolder"
            return 0
        }

        # Get all certificate files
        $certFiles = Get-ChildItem -Path $CertFolder -Filter "cert*.pem" -ErrorAction SilentlyContinue

        if ($certFiles.Count -eq 0) {
            Write-Verbose "No existing certificate files found, starting with 000"
            return 0
        }

        # Extract numbers from filenames and find the highest
        $highestNum = -1
        foreach ($file in $certFiles) {
            if ($file.Name -match 'cert(\d+)\.pem') {
                $num = [int]$matches[1]
                if ($num -gt $highestNum) {
                    $highestNum = $num
                }
            }
        }

        # Return the next number
        return $highestNum + 1
    } catch {
        Write-Error "Error determining next certificate number: $($_.Exception.Message)"
        Write-Log "Error determining next certificate number: $($_.Exception.Message)" -Level 'Error'
        return 0
    }
}

# Function to format certificate number as 3-digit string
function Format-CertificateNumber {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [int]$Number
    )

    return "{0:D3}" -f $Number
}

# Function to install certificate to Recording Server
function Install-CertificateToRecordingServer {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,

        [Parameter()]
        [string]$ComputerName = $env:COMPUTERNAME,

        [Parameter()]
        [switch]$CreateFolder
    )

    try {
        # Get certificate content
        $certContent = Get-CertificatePEMContent -Certificate $Certificate -IncludeKey
        if (-not $certContent.Success) {
            throw "Failed to get certificate content: $($certContent.ErrorMessage)"
        }

        # Get Recording Server certificate folder
        $certFolder = Get-RecordingServerCertFolder -ComputerName $ComputerName

        if (-not $certFolder) {
            if ($CreateFolder) {
                # Try to create the default folder
                $defaultPath = "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates"

                if ($ComputerName -eq $env:COMPUTERNAME) {
                    # Create local folder
                    if (-not (Test-Path $defaultPath)) {
                        New-Item -ItemType Directory -Path $defaultPath -Force | Out-Null
                    }
                    $certFolder = $defaultPath
                } else {
                    # Create remote folder
                    $uncPath = "\\$ComputerName\$(($defaultPath -replace ':', '$'))"
                    if (-not (Test-Path $uncPath)) {
                        New-Item -ItemType Directory -Path $uncPath -Force | Out-Null
                    }
                    $certFolder = $uncPath
                }

                Write-Verbose "Created Recording Server certificate folder: $certFolder"
            } else {
                throw "Recording Server certificate folder not found"
            }
        }

        # Get next certificate number
        $certNumber = Get-NextCertificateNumber -CertFolder $certFolder
        $formattedNumber = Format-CertificateNumber -Number $certNumber

        # Create certificate filenames
        $certFile = Join-Path -Path $certFolder -ChildPath "cert$formattedNumber.pem"
        $keyFile = Join-Path -Path $certFolder -ChildPath "pvkey$formattedNumber.pem"

        # Save certificate and key files with verification
        $saveSuccess = $false

        try {
            # First attempt - standard Set-Content
            Set-Content -Path $certFile -Value $certContent.CertContent -Encoding ASCII -ErrorAction Stop
            Set-Content -Path $keyFile -Value $certContent.KeyContent -Encoding ASCII -ErrorAction Stop

            # Verify files were created and have content
            if ((Test-Path $certFile) -and (Test-Path $keyFile)) {
                $certContent1 = Get-Content -Path $certFile -Raw -ErrorAction Stop
                $keyContent1 = Get-Content -Path $keyFile -Raw -ErrorAction Stop

                if (-not [string]::IsNullOrWhiteSpace($certContent1) -and -not [string]::IsNullOrWhiteSpace($keyContent1)) {
                    $saveSuccess = $true
                    Write-Verbose "Certificate files saved successfully"
                } else {
                    Write-Warning "Certificate files were created but content verification failed. Trying alternative method."
                }
            } else {
                Write-Warning "Certificate files were not created. Trying alternative method."
            }
        } catch {
            Write-Warning "Failed to save certificate files using Set-Content: $($_.Exception.Message). Trying alternative method."
        }

        # Second attempt - use .NET methods if Set-Content fails
        if (-not $saveSuccess) {
            try {
                [System.IO.File]::WriteAllText($certFile, $certContent.CertContent)
                [System.IO.File]::WriteAllText($keyFile, $certContent.KeyContent)

                # Verify files were created and have content
                if ((Test-Path $certFile) -and (Test-Path $keyFile)) {
                    $certContent2 = Get-Content -Path $certFile -Raw -ErrorAction Stop
                    $keyContent2 = Get-Content -Path $keyFile -Raw -ErrorAction Stop

                    if (-not [string]::IsNullOrWhiteSpace($certContent2) -and -not [string]::IsNullOrWhiteSpace($keyContent2)) {
                        $saveSuccess = $true
                        Write-Verbose "Certificate files saved successfully using .NET methods"
                    } else {
                        throw "Certificate files were created but content verification failed."
                    }
                } else {
                    throw "Certificate files were not created using .NET methods."
                }
            } catch {
                throw "Failed to save certificate files using .NET methods: $($_.Exception.Message)"
            }
        }

        if (-not $saveSuccess) {
            throw "Failed to save certificate files after multiple attempts."
        }

        Write-Host "`nCertificate installed to Recording Server:" -ForegroundColor Green
        Write-Host "Server: $ComputerName"
        Write-Host "Certificate: $certFile"
        Write-Host "Private Key: $keyFile"
        Write-Host "Certificate Number: $formattedNumber"

        Write-Host "`nIMPORTANT: You must manually select this certificate in the CompleteView Management Console." -ForegroundColor Yellow
        Write-Host "Do NOT restart the Recording Server service as this may cause recording interruptions." -ForegroundColor Yellow

        Write-Log "Certificate for $($Certificate.MainDomain) installed to Recording Server $ComputerName as cert$formattedNumber.pem" -Level 'Success'

        return @{
            Success = $true
            CertificateFile = $certFile
            KeyFile = $keyFile
            CertificateNumber = $formattedNumber
            ServerName = $ComputerName
        }
    } catch {
        Write-Error "Failed to install certificate to Recording Server: $($_.Exception.Message)"
        Write-Log "Failed to install certificate to Recording Server: $($_.Exception.Message)" -Level 'Error'
        return @{
            Success = $false
            ErrorMessage = $_.Exception.Message
        }
    }
}

# Function to install certificate to Management Server
function Install-CertificateToManagementServer {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,

        [Parameter()]
        [string]$ComputerName = $env:COMPUTERNAME,

        [Parameter()]
        [securestring]$Password,

        [Parameter()]
        [switch]$InstallToLocalMachine
    )

    try {
        # For remote installation, we need to create a PFX file and copy it
        if ($ComputerName -ne $env:COMPUTERNAME) {
            # Create a temporary PFX file
            $tempFolder = [System.IO.Path]::GetTempPath()
            $pfxFile = Join-Path -Path $tempFolder -ChildPath "$($Certificate.MainDomain).pfx"

            # If no password provided, create a secure empty password
            if (-not $Password) {
                $Password = New-Object System.Security.SecureString
            }

            # Export the certificate to PFX
            $cert = $Certificate.Certificate
            $certBytes = $cert.Export([System.Security.Cryptography.X509Certificates.X509ContentType]::Pfx, $Password)
            [System.IO.File]::WriteAllBytes($pfxFile, $certBytes)

            # Copy the PFX file to the remote server
            $remoteTempFolder = "\\$ComputerName\c$\Temp"
            if (-not (Test-Path $remoteTempFolder)) {
                try {
                    New-Item -ItemType Directory -Path $remoteTempFolder -Force -ErrorAction Stop | Out-Null
                    Write-Verbose "Created remote temporary directory: $remoteTempFolder"
                } catch {
                    throw "Failed to create temporary directory on $ComputerName. Error: $($_.Exception.Message)"
                }
            }

            $remotePfxFile = "$remoteTempFolder\$($Certificate.MainDomain).pfx"

            # Try copying with multiple methods if needed
            $copySuccess = $false

            # Method 1: Standard Copy-Item
            try {
                Copy-Item -Path $pfxFile -Destination $remotePfxFile -Force -ErrorAction Stop

                # Verify the file was copied successfully
                if (Test-Path $remotePfxFile) {
                    $sourceHash = Get-FileHash -Path $pfxFile -Algorithm SHA256
                    $destHash = Get-FileHash -Path $remotePfxFile -Algorithm SHA256

                    if ($sourceHash.Hash -eq $destHash.Hash) {
                        $copySuccess = $true
                        Write-Verbose "File copied successfully to $remotePfxFile"
                    } else {
                        Write-Warning "File hash mismatch after copy. Trying alternative method."
                    }
                } else {
                    Write-Warning "File not found after copy. Trying alternative method."
                }
            } catch {
                Write-Warning "Standard copy failed: $($_.Exception.Message). Trying alternative method."
            }

            # Method 2: Use robocopy if standard copy fails
            if (-not $copySuccess) {
                try {
                    $tempFolder = [System.IO.Path]::GetDirectoryName($pfxFile)
                    $fileName = [System.IO.Path]::GetFileName($pfxFile)
                    $remoteFolder = "\\$ComputerName\c$\Temp"

                    $robocopyArgs = @(
                        "`"$tempFolder`"",
                        "`"$remoteFolder`"",
                        "`"$fileName`"",
                        "/R:3",    # Retry 3 times
                        "/W:5",    # Wait 5 seconds between retries
                        "/NP",     # No progress
                        "/NFL",    # No file list
                        "/NDL",    # No directory list
                        "/MT:8"    # Multi-threaded
                    )

                    $robocopyProcess = Start-Process -FilePath "robocopy.exe" -ArgumentList $robocopyArgs -NoNewWindow -Wait -PassThru

                    # Robocopy success codes are 0-7
                    if ($robocopyProcess.ExitCode -lt 8) {
                        # Verify the file was copied successfully
                        if (Test-Path $remotePfxFile) {
                            $sourceHash = Get-FileHash -Path $pfxFile -Algorithm SHA256
                            $destHash = Get-FileHash -Path $remotePfxFile -Algorithm SHA256

                            if ($sourceHash.Hash -eq $destHash.Hash) {
                                $copySuccess = $true
                                Write-Verbose "File copied successfully using robocopy to $remotePfxFile"
                            } else {
                                Write-Warning "File hash mismatch after robocopy."
                            }
                        } else {
                            Write-Warning "File not found after robocopy."
                        }
                    } else {
                        Write-Warning "Robocopy failed with exit code $($robocopyProcess.ExitCode)."
                    }
                } catch {
                    Write-Warning "Robocopy failed: $($_.Exception.Message)."
                }
            }

            # If all copy methods failed, throw an error
            if (-not $copySuccess) {
                throw "Failed to copy certificate file to remote server $ComputerName. Please check network connectivity and permissions."
            }

            # Create a script to import the certificate on the remote server
            $scriptContent = @"
`$pfxFile = "$remotePfxFile"
`$certStore = "Cert:\LocalMachine\My"

# Import the certificate
`$pfxBytes = [System.IO.File]::ReadAllBytes(`$pfxFile)
`$cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
`$cert.Import(`$pfxBytes, "", "Exportable,PersistKeySet,MachineKeySet")

# Add to certificate store
`$store = New-Object System.Security.Cryptography.X509Certificates.X509Store "My", "LocalMachine"
`$store.Open("ReadWrite")
`$store.Add(`$cert)
`$store.Close()

# Clean up
Remove-Item -Path `$pfxFile -Force

Write-Output "Certificate imported successfully to `$certStore"
"@

            $scriptFile = Join-Path -Path $tempFolder -ChildPath "ImportCert.ps1"
            Set-Content -Path $scriptFile -Value $scriptContent

            # Execute the script on the remote server
            Invoke-Command -ComputerName $ComputerName -FilePath $scriptFile

            # Clean up local files
            Remove-Item -Path $pfxFile -Force
            Remove-Item -Path $scriptFile -Force

            Write-Host "`nCertificate installed to Management Server:" -ForegroundColor Green
            Write-Host "Server: $ComputerName"
            Write-Host "Certificate Store: LocalMachine\My"
            Write-Host "Subject: $($Certificate.MainDomain)"

            Write-Host "`nIMPORTANT: You must manually select this certificate in the CompleteView Management Console." -ForegroundColor Yellow

            Write-Log "Certificate for $($Certificate.MainDomain) installed to Management Server $ComputerName" -Level 'Success'

            return @{
                Success = $true
                ServerName = $ComputerName
                CertificateSubject = $Certificate.MainDomain
            }
        } else {
            # For local installation, use the certificate store directly
            $storeLocation = if ($InstallToLocalMachine) { "LocalMachine" } else { "CurrentUser" }
            $store = New-Object System.Security.Cryptography.X509Certificates.X509Store "My", $storeLocation
            $store.Open("ReadWrite")
            $store.Add($Certificate.Certificate)
            $store.Close()

            Write-Host "`nCertificate installed to Management Server:" -ForegroundColor Green
            Write-Host "Server: $ComputerName (local)"
            Write-Host "Certificate Store: $storeLocation\My"
            Write-Host "Subject: $($Certificate.MainDomain)"

            Write-Host "`nIMPORTANT: You must manually select this certificate in the CompleteView Management Console." -ForegroundColor Yellow

            Write-Log "Certificate for $($Certificate.MainDomain) installed to local Management Server certificate store ($storeLocation)" -Level 'Success'

            return @{
                Success = $true
                ServerName = $ComputerName
                CertificateSubject = $Certificate.MainDomain
                StoreLocation = $storeLocation
            }
        }
    } catch {
        Write-Error "Failed to install certificate to Management Server: $($_.Exception.Message)"
        Write-Log "Failed to install certificate to Management Server: $($_.Exception.Message)" -Level 'Error'
        return @{
            Success = $false
            ErrorMessage = $_.Exception.Message
        }
    }
}

# Function to test connectivity to a remote server
function Test-RemoteServerConnectivity {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ComputerName,

        [Parameter()]
        [switch]$TestPSRemoting,

        [Parameter()]
        [switch]$TestFileShare
    )

    try {
        $results = @{
            ServerName = $ComputerName
            Ping = $false
            PSRemoting = $false
            FileShare = $false
            AllTestsPassed = $false
        }

        # Test basic connectivity with ping
        Write-Verbose "Testing ping connectivity to $ComputerName..."
        $pingResult = Test-Connection -ComputerName $ComputerName -Count 1 -Quiet -ErrorAction SilentlyContinue
        $results.Ping = $pingResult

        if (-not $pingResult) {
            Write-Warning "Cannot ping $ComputerName. Network connectivity may be an issue."
            return $results
        }

        # Test PowerShell Remoting if requested
        if ($TestPSRemoting) {
            Write-Verbose "Testing PowerShell Remoting connectivity to $ComputerName..."
            try {
                $psRemotingResult = Invoke-Command -ComputerName $ComputerName -ScriptBlock { $env:COMPUTERNAME } -ErrorAction Stop
                $results.PSRemoting = ($psRemotingResult -eq $ComputerName)

                if (-not $results.PSRemoting) {
                    Write-Warning "PowerShell Remoting test failed for $ComputerName. Returned unexpected result."
                }
            } catch {
                Write-Warning "PowerShell Remoting not available on $ComputerName. Error: $($_.Exception.Message)"
            }
        }

        # Test file share access if requested
        if ($TestFileShare) {
            Write-Verbose "Testing file share connectivity to $ComputerName..."
            $testPath = "\\$ComputerName\c$"
            $fileShareResult = Test-Path -Path $testPath -ErrorAction SilentlyContinue
            $results.FileShare = $fileShareResult

            if (-not $fileShareResult) {
                Write-Warning "Cannot access administrative share on $ComputerName. File sharing may be disabled or you may not have sufficient permissions."
            }
        }

        # Check if all requested tests passed
        $results.AllTestsPassed = $results.Ping -and
                                 (-not $TestPSRemoting -or $results.PSRemoting) -and
                                 (-not $TestFileShare -or $results.FileShare)

        return $results
    } catch {
        Write-Error "Error testing connectivity to $ComputerName - $($_.Exception.Message)"
        Write-Log "Error testing connectivity to $ComputerName - $($_.Exception.Message)" -Level 'Error'
        return @{
            ServerName = $ComputerName
            Ping = $false
            PSRemoting = $false
            FileShare = $false
            AllTestsPassed = $false
            Error = $_.Exception.Message
        }
    }
}

# Function to distribute certificate to multiple servers
function Send-CertificateToServers {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,

        [Parameter()]
        [string[]]$ManagementServers,

        [Parameter()]
        [string[]]$RecordingServers,

        [Parameter()]
        [securestring]$Password,

        [Parameter()]
        [switch]$CreateFolders,

        [Parameter()]
        [switch]$SkipConnectivityTest
    )

    try {
        $results = @{
            ManagementServers = @()
            RecordingServers = @()
            SuccessCount = 0
            FailureCount = 0
            SkippedServers = @()
        }

        # Test connectivity to all servers first if not skipped
        if (-not $SkipConnectivityTest) {
            Write-Host "`nTesting connectivity to servers..." -ForegroundColor Cyan
            $unreachableServers = @()

            # Test Management Servers
            if ($ManagementServers -and $ManagementServers.Count -gt 0) {
                foreach ($server in $ManagementServers) {
                    Write-Host "  Testing $server..." -NoNewline
                    $connectivityResult = Test-RemoteServerConnectivity -ComputerName $server -TestPSRemoting -TestFileShare

                    if ($connectivityResult.AllTestsPassed) {
                        Write-Host " Success" -ForegroundColor Green
                    } else {
                        Write-Host " Failed" -ForegroundColor Red
                        $unreachableServers += $server

                        # Add detailed diagnostics
                        $diagnostics = @()
                        if (-not $connectivityResult.Ping) { $diagnostics += "Cannot ping server" }
                        if (-not $connectivityResult.PSRemoting) { $diagnostics += "PowerShell Remoting not available" }
                        if (-not $connectivityResult.FileShare) { $diagnostics += "Administrative share not accessible" }

                        $results.SkippedServers += @{
                            ServerName = $server
                            ServerType = "ManagementServer"
                            Reason = $diagnostics -join ", "
                        }
                    }
                }
            }

            # Test Recording Servers
            if ($RecordingServers -and $RecordingServers.Count -gt 0) {
                foreach ($server in $RecordingServers) {
                    Write-Host "  Testing $server..." -NoNewline
                    $connectivityResult = Test-RemoteServerConnectivity -ComputerName $server -TestFileShare

                    if ($connectivityResult.AllTestsPassed) {
                        Write-Host " Success" -ForegroundColor Green
                    } else {
                        Write-Host " Failed" -ForegroundColor Red
                        $unreachableServers += $server

                        # Add detailed diagnostics
                        $diagnostics = @()
                        if (-not $connectivityResult.Ping) { $diagnostics += "Cannot ping server" }
                        if (-not $connectivityResult.FileShare) { $diagnostics += "Administrative share not accessible" }

                        $results.SkippedServers += @{
                            ServerName = $server
                            ServerType = "RecordingServer"
                            Reason = $diagnostics -join ", "
                        }
                    }
                }
            }

            # Filter out unreachable servers
            if ($unreachableServers.Count -gt 0) {
                Write-Warning "`nThe following servers are unreachable and will be skipped:"
                foreach ($server in $unreachableServers) {
                    Write-Warning "  - $server"
                }

                $ManagementServers = $ManagementServers | Where-Object { $unreachableServers -notcontains $_ }
                $RecordingServers = $RecordingServers | Where-Object { $unreachableServers -notcontains $_ }

                if (($null -eq $ManagementServers -or $ManagementServers.Count -eq 0) -and
                    ($null -eq $RecordingServers -or $RecordingServers.Count -eq 0)) {
                    Write-Warning "No reachable servers remaining. Distribution canceled."
                    return $results
                }

                $confirm = Read-Host "`nContinue with distribution to reachable servers only? (Y/N)"
                if ($confirm -notmatch '^[Yy]$') {
                    Write-Host "Distribution canceled." -ForegroundColor Yellow
                    return $results
                }
            }
        }

        # Install to Management Servers
        if ($ManagementServers -and $ManagementServers.Count -gt 0) {
            Write-Host "`nInstalling certificate to Management Servers..." -ForegroundColor Cyan

            foreach ($server in $ManagementServers) {
                Write-Host "Installing to $server..." -ForegroundColor Yellow
                $result = Install-CertificateToManagementServer -Certificate $Certificate -ComputerName $server -Password $Password -InstallToLocalMachine

                $serverResult = @{
                    ServerName = $server
                    Success = $result.Success
                }

                if ($result.Success) {
                    $results.SuccessCount++
                    Write-Host "  Success" -ForegroundColor Green
                } else {
                    $results.FailureCount++
                    $serverResult.ErrorMessage = $result.ErrorMessage
                    Write-Host "  Failed: $($result.ErrorMessage)" -ForegroundColor Red
                }

                $results.ManagementServers += $serverResult
            }
        }

        # Install to Recording Servers
        if ($RecordingServers -and $RecordingServers.Count -gt 0) {
            Write-Host "`nInstalling certificate to Recording Servers..." -ForegroundColor Cyan

            foreach ($server in $RecordingServers) {
                Write-Host "Installing to $server..." -ForegroundColor Yellow
                $result = Install-CertificateToRecordingServer -Certificate $Certificate -ComputerName $server -CreateFolder:$CreateFolders

                $serverResult = @{
                    ServerName = $server
                    Success = $result.Success
                }

                if ($result.Success) {
                    $results.SuccessCount++
                    $serverResult.CertificateNumber = $result.CertificateNumber
                    Write-Host "  Success (Certificate Number: $($result.CertificateNumber))" -ForegroundColor Green
                } else {
                    $results.FailureCount++
                    $serverResult.ErrorMessage = $result.ErrorMessage
                    Write-Host "  Failed: $($result.ErrorMessage)" -ForegroundColor Red
                }

                $results.RecordingServers += $serverResult
            }
        }

        Write-Host "`nCertificate distribution complete:" -ForegroundColor Cyan
        Write-Host "Successful installations: $($results.SuccessCount)" -ForegroundColor Green
        Write-Host "Failed installations: $($results.FailureCount)" -ForegroundColor $(if ($results.FailureCount -gt 0) { "Red" } else { "Green" })

        Write-Log "Certificate distribution for $($Certificate.MainDomain) completed with $($results.SuccessCount) successes and $($results.FailureCount) failures" -Level $(if ($results.FailureCount -gt 0) { "Warning" } else { "Success" })

        return $results
    } catch {
        Write-Error "Failed to distribute certificate: $($_.Exception.Message)"
        Write-Log "Failed to distribute certificate: $($_.Exception.Message)" -Level 'Error'
        return @{
            ManagementServers = @()
            RecordingServers = @()
            SuccessCount = 0
            FailureCount = 1
            ErrorMessage = $_.Exception.Message
        }
    }
}
