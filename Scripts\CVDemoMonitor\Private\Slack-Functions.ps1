# Slack-Functions.ps1
# Contains functions related to Slack notifications

function Test-NetworkConnectivity {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$TargetHost,

        [Parameter(Mandatory = $false)]
        [int]$Port = 443,

        [Parameter(Mandatory = $false)]
        [int]$TimeoutMilliseconds = 3000
    )

    try {
        # Extract hostname from URL if needed
        if ($TargetHost -match '^https?://([^:/]+)') {
            $TargetHost = $matches[1]
        }

        Write-Log "Testing network connectivity to $TargetHost on port $Port" -Level 'DEBUG'

        # Create TCP client
        $tcpClient = New-Object System.Net.Sockets.TcpClient

        # Begin async connection attempt
        $connectResult = $tcpClient.BeginConnect($TargetHost, $Port, $null, $null)

        # Wait for connection with timeout
        $waitResult = $connectResult.AsyncWaitHandle.WaitOne($TimeoutMilliseconds, $false)

        # Check if connection succeeded
        if ($waitResult) {
            try {
                # Complete the connection
                $tcpClient.EndConnect($connectResult)
                $connected = $tcpClient.Connected
            }
            catch {
                $connected = $false
                Write-Log "TCP connection error: $($_.Exception.Message)" -Level 'DEBUG'
            }
        }
        else {
            $connected = $false
            Write-Log "TCP connection timed out after $TimeoutMilliseconds ms" -Level 'DEBUG'
        }

        # Close the connection
        if ($tcpClient.Connected) {
            $tcpClient.Close()
        }

        # Dispose of the client
        $tcpClient.Dispose()

        return $connected
    }
    catch {
        Write-Log "Error in Test-NetworkConnectivity: $($_.Exception.Message)" -Level 'DEBUG'
        return $false
    }
}

function Add-SlackMessageToQueue {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $true)]
        [string]$MessageText,

        [Parameter(Mandatory = $false)]
        [string]$QueuePath = ""
    )

    try {
        # If no queue path is provided, use the default location
        if ([string]::IsNullOrEmpty($QueuePath)) {
            # Use the script's base directory or temp folder as fallback
            if ($script:config -and $script:config.BaseDirectory) {
                $queueDir = Join-Path -Path $script:config.BaseDirectory -ChildPath "SlackQueue"
            }
            else {
                $queueDir = Join-Path -Path $env:TEMP -ChildPath "CVMonitorSlackQueue"
            }

            # Ensure queue directory exists
            if (-not (Test-Path -Path $queueDir -ErrorAction SilentlyContinue)) {
                try {
                    New-Item -Path $queueDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Log "Created Slack message queue directory: $queueDir" -Level 'INFO'
                }
                catch {
                    # If we can't create the directory, use temp directly
                    $queueDir = $env:TEMP
                    Write-Log "Failed to create queue directory, using $queueDir instead: $($_.Exception.Message)" -Level 'WARNING'
                }
            }

            $QueuePath = Join-Path -Path $queueDir -ChildPath "slack_queue.json"
        }

        # Create message object with timestamp and unique ID
        $messageObj = @{
            Id = [guid]::NewGuid().ToString()
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            WebhookUrl = $WebhookUrl
            MessageText = $MessageText
            RetryCount = 0
        }

        # Load existing queue if it exists
        $queue = @()
        if (Test-Path -Path $QueuePath -ErrorAction SilentlyContinue) {
            try {
                $queueContent = Get-Content -Path $QueuePath -Raw -ErrorAction Stop
                if (-not [string]::IsNullOrEmpty($queueContent)) {
                    $queue = $queueContent | ConvertFrom-Json -ErrorAction Stop
                    # Ensure it's an array even if there's only one item
                    if ($queue -isnot [array]) {
                        $queue = @($queue)
                    }
                }
            }
            catch {
                Write-Log "Error reading queue file, creating new queue: $($_.Exception.Message)" -Level 'WARNING'
                $queue = @()
            }
        }

        # Add new message to queue
        $queue += $messageObj

        # Save updated queue
        $queue | ConvertTo-Json -Depth 5 | Out-File -FilePath $QueuePath -Force -ErrorAction Stop

        Write-Log "Message queued successfully. Queue now contains $($queue.Count) messages." -Level 'INFO'
        return $QueuePath
    }
    catch {
        Write-Log "Error adding message to queue: $($_.Exception.Message)" -Level 'ERROR'
        return $null
    }
}

function Send-SlackMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $true)]
        [string]$MessageText,

        [Parameter(Mandatory = $false)]
        [switch]$QueueOnFailure,

        [Parameter(Mandatory = $false)]
        [int]$MaxRetries = 3,

        [Parameter(Mandatory = $false)]
        [int]$RetryDelaySeconds = 2
    )

    try {
        # Check network connectivity first
        $slackHost = if ($WebhookUrl -match '^https?://([^/]+)') { $matches[1] } else { "hooks.slack.com" }
        $isReachable = Test-NetworkConnectivity -TargetHost $slackHost -Port 443

        if (-not $isReachable) {
            Write-Log "Slack is not reachable. Network connectivity issue detected." -Level 'WARNING'
            
            if ($QueueOnFailure) {
                Write-Log "Queuing message for later delivery" -Level 'INFO'
                Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
                return
            }
            else {
                throw "Slack is not reachable. Network connectivity issue detected."
            }
        }

        # Prepare the message payload
        $payload = @{
            text = $MessageText
        } | ConvertTo-Json

        # Set up the request
        $params = @{
            Uri = $WebhookUrl
            Method = 'POST'
            Body = $payload
            ContentType = 'application/json'
            UseBasicParsing = $true
            ErrorAction = 'Stop'
        }

        # Try to send the message with retries
        $attempt = 0
        $success = $false

        while (-not $success -and $attempt -lt $MaxRetries) {
            $attempt++
            
            try {
                $response = Invoke-WebRequest @params
                
                if ($response.StatusCode -eq 200) {
                    Write-Log "Successfully sent Slack message (Attempt $attempt)" -Level 'INFO'
                    $success = $true
                }
                else {
                    Write-Log "Unexpected status code: $($response.StatusCode) (Attempt $attempt)" -Level 'WARNING'
                    Start-Sleep -Seconds $RetryDelaySeconds
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error sending Slack message (Attempt $attempt): $errorMsg" -Level 'WARNING'
                
                if ($attempt -lt $MaxRetries) {
                    Write-Log "Retrying in $RetryDelaySeconds seconds..." -Level 'INFO'
                    Start-Sleep -Seconds $RetryDelaySeconds
                }
            }
        }

        # If all attempts failed and queuing is enabled, queue the message
        if (-not $success -and $QueueOnFailure) {
            Write-Log "All attempts to send Slack message failed. Queuing for later delivery." -Level 'WARNING'
            Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
            return
        }
        elseif (-not $success) {
            throw "Failed to send Slack message after $MaxRetries attempts."
        }
    }
    catch {
        $errorMsg = $_.Exception.Message
        Write-Log "Error in Send-SlackMessage: $errorMsg" -Level 'ERROR'
        
        if ($QueueOnFailure) {
            Write-Log "Attempting to queue message due to error" -Level 'INFO'
            Add-SlackMessageToQueue -WebhookUrl $WebhookUrl -MessageText $MessageText
        }
        else {
            throw $_
        }
    }
}

function Send-SlackNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$AppName,

        [Parameter(Mandatory = $true)]
        [string]$Hostname,

        [Parameter(Mandatory = $true)]
        [string]$CrashTime,

        [Parameter(Mandatory = $false)]
        [string]$FaultingModule = "Unknown",

        [Parameter(Mandatory = $false)]
        [string]$ExceptionCode = "Unknown",

        [Parameter(Mandatory = $false)]
        [string]$DumpPath = "",

        [Parameter(Mandatory = $true)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$DumpAvailable = $false,

        [Parameter(Mandatory = $false)]
        [bool]$InitialAlert = $true
    )

    try {
        # Get friendly name for the application
        $friendlyName = switch ($AppName) {
            "RecordingServer64.exe" { "CompleteView Recording Server" }
            "AdminService64.exe" { "CompleteView Administrative Service" }
            "ManagementServer.exe" { "CompleteView Management Server" }
            default { $AppName }
        }

        # Create the message text based on whether this is an initial alert or follow-up
        if ($InitialAlert) {
            $emoji = ":rotating_light:"
            $title = "*CompleteView Service Crash Detected*"
            
            $messageText = "$emoji $title

*Service:* $friendlyName
*Host:* $Hostname
*Time:* $CrashTime
*Faulting Module:* $FaultingModule
*Exception Code:* $ExceptionCode"

            if ($DumpAvailable) {
                $messageText += "`n*Crash Dump:* Available at $DumpPath"
            }
            else {
                $messageText += "`n*Crash Dump:* Waiting for dump file..."
            }
        }
        else {
            # This is a follow-up notification about the dump file
            if ($DumpAvailable) {
                $emoji = ":file_folder:"
                $title = "*Crash Dump Available*"
                
                $messageText = "$emoji $title

*Service:* $friendlyName
*Host:* $Hostname
*Crash Time:* $CrashTime
*Dump File:* $DumpPath"
            }
            else {
                $emoji = ":warning:"
                $title = "*Crash Dump Not Available*"
                
                $messageText = "$emoji $title

*Service:* $friendlyName
*Host:* $Hostname
*Crash Time:* $CrashTime
*Note:* No crash dump was generated after waiting for the timeout period."
            }
        }

        # Send the message
        Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText -QueueOnFailure
    }
    catch {
        Write-Log "Error in Send-SlackNotification: $($_.Exception.Message)" -Level 'ERROR'
        throw $_
    }
}
