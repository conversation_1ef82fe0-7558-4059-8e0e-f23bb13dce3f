<#
.SYNOPSIS
    Configures automatic renewal for certificates.

.DESCRIP<PERSON>ON
    Sets up scheduled tasks to automatically renew certificates before they expire.

.EXAMPLE
    Set-AutomaticRenewal
#>
function Set-AutomaticRenewal {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='Medium')]
    param ()

    # Get renewal configuration
    $config = Get-RenewalConfig

    # Display current settings
    Write-Host "`nCurrent Automatic Renewal Settings:" -ForegroundColor Cyan
    Write-Host "Renewal Hour: $($config.RenewalHour) (24-hour format)"
    Write-Host "Renewal Minute: $($config.RenewalMinute)"
    Write-Host "Use Randomization: $($config.UseRandomization)"
    if ($config.UseRandomization) {
        Write-Host "Randomization Window: $($config.RandomizationWindow) minutes"
    }

    # Check if the scheduled task exists
    $taskName = "AutoCertLE Certificate Renewal"
    $taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue

    if ($taskExists) {
        Write-Host "`nAutomatic renewal is currently enabled." -ForegroundColor Green
    } else {
        Write-Host "`nAutomatic renewal is not currently enabled." -ForegroundColor Yellow
    }

    # Options
    Write-Host "`nOptions:" -ForegroundColor Cyan
    Write-Host "1) Enable automatic renewal"
    Write-Host "2) Disable automatic renewal"
    Write-Host "3) Modify renewal settings"
    Write-Host "0) Back"

    $choice = Get-ValidatedInput -Prompt "`nEnter your choice (0-3)" -ValidOptions 1,2,3
    switch ($choice) {
        0 { return }
        1 {
            # Enable automatic renewal
            try {
                # Get the script path
                $scriptPath = $PSCommandPath
                if (-not $scriptPath) {
                    $scriptPath = Join-Path -Path $PSScriptRoot -ChildPath "AutoCertLE.ps1"
                }
                
                if (-not (Test-Path $scriptPath)) {
                    # Try to find the script in the module directory
                    $moduleRoot = Split-Path -Parent $PSScriptRoot
                    $scriptPath = Join-Path -Path $moduleRoot -ChildPath "AutoCertLE.ps1"
                    
                    if (-not (Test-Path $scriptPath)) {
                        # Create a wrapper script
                        $wrapperPath = Join-Path -Path $env:TEMP -ChildPath "AutoCertLE-Renewal.ps1"
                        $wrapperContent = @"
# AutoCertLE Renewal Wrapper
Import-Module AutoCertLE
Invoke-AutoCertRenewal -RenewAll -NonInteractive
"@
                        Set-Content -Path $wrapperPath -Value $wrapperContent -Force
                        $scriptPath = $wrapperPath
                    }
                }
                
                # Create the scheduled task
                $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`" -RenewAll -NonInteractive"
                
                # Set the trigger time
                $hour = $config.RenewalHour
                $minute = $config.RenewalMinute
                
                # Create a daily trigger
                $trigger = New-ScheduledTaskTrigger -Daily -At "$hour`:$minute"
                
                # Set randomization if enabled
                if ($config.UseRandomization) {
                    $randomDelay = New-TimeSpan -Minutes $config.RandomizationWindow
                    $trigger.RandomDelay = $randomDelay
                }
                
                # Set the principal to run with highest privileges
                $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
                
                # Set the settings
                $settings = New-ScheduledTaskSettingsSet -StartWhenAvailable -DontStopOnIdleEnd -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
                
                # Register the task
                if ($taskExists) {
                    # Update existing task
                    Set-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings
                } else {
                    # Create new task
                    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings
                }
                
                Write-Host "`nAutomatic renewal has been enabled." -ForegroundColor Green
                Write-Host "Certificates will be renewed daily at $hour`:$minute."
                if ($config.UseRandomization) {
                    Write-Host "A random delay of up to $($config.RandomizationWindow) minutes will be applied."
                }
                
                Write-Log "Automatic renewal enabled with schedule: Daily at $hour`:$minute" -Level 'Success'
            } catch {
                Write-Error "Failed to enable automatic renewal: $($_)"
                Write-Log "Failed to enable automatic renewal: $($_)" -Level 'Error'
            }
        }
        2 {
            # Disable automatic renewal
            try {
                if ($taskExists) {
                    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
                    Write-Host "`nAutomatic renewal has been disabled." -ForegroundColor Green
                    Write-Log "Automatic renewal disabled" -Level 'Success'
                } else {
                    Write-Host "`nAutomatic renewal is not currently enabled." -ForegroundColor Yellow
                }
            } catch {
                Write-Error "Failed to disable automatic renewal: $($_)"
                Write-Log "Failed to disable automatic renewal: $($_)" -Level 'Error'
            }
        }
        3 {
            # Modify renewal settings
            try {
                # Prompt for new settings
                $newHour = Read-Host "`nEnter the hour for renewal (0-23, current: $($config.RenewalHour))"
                if ([string]::IsNullOrWhiteSpace($newHour)) {
                    $newHour = $config.RenewalHour
                } elseif (-not ([int]::TryParse($newHour, [ref]$null)) -or [int]$newHour -lt 0 -or [int]$newHour -gt 23) {
                    Write-Warning "Invalid hour. Using current value: $($config.RenewalHour)"
                    $newHour = $config.RenewalHour
                }
                
                $newMinute = Read-Host "Enter the minute for renewal (0-59, current: $($config.RenewalMinute))"
                if ([string]::IsNullOrWhiteSpace($newMinute)) {
                    $newMinute = $config.RenewalMinute
                } elseif (-not ([int]::TryParse($newMinute, [ref]$null)) -or [int]$newMinute -lt 0 -or [int]$newMinute -gt 59) {
                    Write-Warning "Invalid minute. Using current value: $($config.RenewalMinute)"
                    $newMinute = $config.RenewalMinute
                }
                
                $useRandomization = Read-Host "Use randomization? (Y/N, current: $($config.UseRandomization))"
                if ([string]::IsNullOrWhiteSpace($useRandomization)) {
                    $useRandomization = $config.UseRandomization
                } else {
                    $useRandomization = $useRandomization -match '^[Yy]$'
                }
                
                $randomizationWindow = $config.RandomizationWindow
                if ($useRandomization) {
                    $newWindow = Read-Host "Enter randomization window in minutes (current: $($config.RandomizationWindow))"
                    if ([string]::IsNullOrWhiteSpace($newWindow)) {
                        $randomizationWindow = $config.RandomizationWindow
                    } elseif (-not ([int]::TryParse($newWindow, [ref]$null)) -or [int]$newWindow -lt 1) {
                        Write-Warning "Invalid window. Using current value: $($config.RandomizationWindow)"
                        $randomizationWindow = $config.RandomizationWindow
                    } else {
                        $randomizationWindow = [int]$newWindow
                    }
                }
                
                # Update configuration
                $config.RenewalHour = [int]$newHour
                $config.RenewalMinute = [int]$newMinute
                $config.UseRandomization = $useRandomization
                $config.RandomizationWindow = $randomizationWindow
                
                # Save configuration
                Save-RenewalConfig -Config $config
                
                Write-Host "`nRenewal settings updated." -ForegroundColor Green
                Write-Log "Renewal settings updated: Hour=$newHour, Minute=$newMinute, UseRandomization=$useRandomization, Window=$randomizationWindow" -Level 'Success'
                
                # Update the scheduled task if it exists
                if ($taskExists) {
                    $updateTask = Read-Host "`nDo you want to update the scheduled task with these new settings? (Y/N)"
                    if ($updateTask -match '^[Yy]$') {
                        # Call the enable function to update the task
                        Set-AutomaticRenewal
                    }
                }
            } catch {
                Write-Error "Failed to update renewal settings: $($_)"
                Write-Log "Failed to update renewal settings: $($_)" -Level 'Error'
            }
        }
    }
    
    Read-Host "`nPress Enter to return to the main menu"
}
