# Functions for WinRM-based certificate distribution

# Function to test WinRM connectivity
function Test-WinRMConnectivity {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ComputerName,
        
        [Parameter()]
        [System.Management.Automation.PSCredential]$Credential
    )
    
    try {
        Write-Verbose "Testing WinRM connectivity to $ComputerName..."
        
        $params = @{
            ComputerName = $ComputerName
            ScriptBlock = { $env:COMPUTERNAME }
            ErrorAction = 'Stop'
        }
        
        if ($Credential) {
            $params.Credential = $Credential
        }
        
        $result = Invoke-Command @params
        
        return @{
            Success = $true
            ComputerName = $ComputerName
            ReturnedName = $result
        }
    } catch {
        Write-Verbose "WinRM connectivity test failed: $($_.Exception.Message)"
        return @{
            Success = $false
            ComputerName = $ComputerName
            Error = $_.Exception.Message
        }
    }
}

# Function to copy a certificate to a remote server using WinRM
function Copy-CertificateViaWinRM {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ComputerName,
        
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        
        [Parameter()]
        [System.Management.Automation.PSCredential]$Credential,
        
        [Parameter()]
        [securestring]$Password,
        
        [Parameter()]
        [ValidateSet("ManagementServer", "RecordingServer")]
        [string]$ServerType,
        
        [Parameter()]
        [switch]$CreateFolders
    )
    
    try {
        # Prepare the remote session parameters
        $sessionParams = @{
            ComputerName = $ComputerName
            ErrorAction = 'Stop'
        }
        
        if ($Credential) {
            $sessionParams.Credential = $Credential
        }
        
        # Create a remote session
        $session = New-PSSession @sessionParams
        
        # Determine the installation approach based on server type
        if ($ServerType -eq "ManagementServer") {
            # For Management Server, we'll export the certificate to PFX and import it remotely
            
            # Export the certificate to PFX bytes
            if (-not $Password) {
                $Password = New-Object System.Security.SecureString
            }
            
            $pfxBytes = $Certificate.Certificate.Export(
                [System.Security.Cryptography.X509Certificates.X509ContentType]::Pfx, 
                $Password
            )
            
            # Convert to base64 for safe transfer
            $pfxBase64 = [Convert]::ToBase64String($pfxBytes)
            
            # Create a script block to import the certificate on the remote server
            $scriptBlock = {
                param($pfxBase64, $passwordSecure)
                
                try {
                    # Convert base64 back to bytes
                    $pfxBytes = [Convert]::FromBase64String($pfxBase64)
                    
                    # Create a temporary file for the PFX
                    $tempFile = Join-Path -Path $env:TEMP -ChildPath "temp_cert.pfx"
                    [System.IO.File]::WriteAllBytes($tempFile, $pfxBytes)
                    
                    # Import the certificate to the local machine store
                    $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
                    $cert.Import($tempFile, $passwordSecure, "Exportable,PersistKeySet,MachineKeySet")
                    
                    $store = New-Object System.Security.Cryptography.X509Certificates.X509Store "My", "LocalMachine"
                    $store.Open("ReadWrite")
                    $store.Add($cert)
                    $store.Close()
                    
                    # Clean up
                    Remove-Item -Path $tempFile -Force
                    
                    return @{
                        Success = $true
                        Message = "Certificate imported successfully to LocalMachine\My store"
                        Subject = $cert.Subject
                        Thumbprint = $cert.Thumbprint
                        NotAfter = $cert.NotAfter
                    }
                } catch {
                    return @{
                        Success = $false
                        Error = $_.Exception.Message
                    }
                }
            }
            
            # Execute the script block on the remote server
            $result = Invoke-Command -Session $session -ScriptBlock $scriptBlock -ArgumentList $pfxBase64, $Password
            
        } elseif ($ServerType -eq "RecordingServer") {
            # For Recording Server, we'll transfer the PEM files
            
            # Get certificate content
            $certContent = Get-CertificatePEMContent -Certificate $Certificate -IncludeKey
            if (-not $certContent.Success) {
                throw "Failed to get certificate content: $($certContent.ErrorMessage)"
            }
            
            # Create a script block to save the certificate files on the remote server
            $scriptBlock = {
                param($certContent, $keyContent, $createFolders)
                
                try {
                    # Find the certificate folder
                    $possiblePaths = @(
                        "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates",
                        "C:\Program Files\Salient Security Platform\CompleteView\Recording Server\Certificates",
                        "C:\Program Files\Symmetry Security Platform\Symmetry CompleteView\Recording Server\Certificates"
                    )
                    
                    $certFolder = $null
                    foreach ($path in $possiblePaths) {
                        if (Test-Path $path) {
                            $certFolder = $path
                            break
                        }
                    }
                    
                    # Create folder if it doesn't exist and createFolders is true
                    if (-not $certFolder -and $createFolders) {
                        $certFolder = "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates"
                        New-Item -ItemType Directory -Path $certFolder -Force | Out-Null
                    }
                    
                    if (-not $certFolder) {
                        return @{
                            Success = $false
                            Error = "Recording Server certificate folder not found"
                        }
                    }
                    
                    # Get next certificate number
                    $certFiles = Get-ChildItem -Path $certFolder -Filter "cert*.pem" -ErrorAction SilentlyContinue
                    $highestNum = -1
                    
                    if ($certFiles.Count -gt 0) {
                        foreach ($file in $certFiles) {
                            if ($file.Name -match 'cert(\d+)\.pem') {
                                $num = [int]$matches[1]
                                if ($num -gt $highestNum) {
                                    $highestNum = $num
                                }
                            }
                        }
                    }
                    
                    $nextNum = $highestNum + 1
                    $formattedNum = "{0:D3}" -f $nextNum
                    
                    # Create certificate filenames
                    $certFile = Join-Path -Path $certFolder -ChildPath "cert$formattedNum.pem"
                    $keyFile = Join-Path -Path $certFolder -ChildPath "pvkey$formattedNum.pem"
                    
                    # Save certificate and key files
                    [System.IO.File]::WriteAllText($certFile, $certContent)
                    [System.IO.File]::WriteAllText($keyFile, $keyContent)
                    
                    # Verify files were created
                    $certExists = Test-Path $certFile
                    $keyExists = Test-Path $keyFile
                    
                    if ($certExists -and $keyExists) {
                        return @{
                            Success = $true
                            CertificateFile = $certFile
                            KeyFile = $keyFile
                            CertificateNumber = $formattedNum
                            Message = "Certificate files created successfully"
                        }
                    } else {
                        return @{
                            Success = $false
                            Error = "Failed to verify certificate files were created"
                            CertExists = $certExists
                            KeyExists = $keyExists
                        }
                    }
                } catch {
                    return @{
                        Success = $false
                        Error = $_.Exception.Message
                    }
                }
            }
            
            # Execute the script block on the remote server
            $result = Invoke-Command -Session $session -ScriptBlock $scriptBlock -ArgumentList $certContent.CertContent, $certContent.KeyContent, $CreateFolders
        } else {
            throw "Invalid server type specified: $ServerType"
        }
        
        # Close the session
        Remove-PSSession -Session $session
        
        return $result
    } catch {
        Write-Error "Failed to copy certificate via WinRM: $($_.Exception.Message)"
        Write-Log "Failed to copy certificate via WinRM: $($_.Exception.Message)" -Level 'Error'
        
        # Make sure to clean up the session if it exists
        if ($session) {
            Remove-PSSession -Session $session -ErrorAction SilentlyContinue
        }
        
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

# Function to distribute certificates using WinRM
function Send-CertificatesViaWinRM {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        
        [Parameter()]
        [string[]]$ManagementServers,
        
        [Parameter()]
        [string[]]$RecordingServers,
        
        [Parameter()]
        [System.Management.Automation.PSCredential]$Credential,
        
        [Parameter()]
        [securestring]$Password,
        
        [Parameter()]
        [switch]$CreateFolders,
        
        [Parameter()]
        [switch]$SkipConnectivityTest
    )
    
    try {
        $results = @{
            ManagementServers = @()
            RecordingServers = @()
            SuccessCount = 0
            FailureCount = 0
            SkippedServers = @()
        }
        
        # Test WinRM connectivity to all servers first if not skipped
        if (-not $SkipConnectivityTest) {
            Write-Host "`nTesting WinRM connectivity to servers..." -ForegroundColor Cyan
            $unreachableServers = @()
            
            # Test Management Servers
            if ($ManagementServers -and $ManagementServers.Count -gt 0) {
                foreach ($server in $ManagementServers) {
                    Write-Host "  Testing $server..." -NoNewline
                    $connectivityResult = Test-WinRMConnectivity -ComputerName $server -Credential $Credential
                    
                    if ($connectivityResult.Success) {
                        Write-Host " Success" -ForegroundColor Green
                    } else {
                        Write-Host " Failed" -ForegroundColor Red
                        $unreachableServers += $server
                        
                        $results.SkippedServers += @{
                            ServerName = $server
                            ServerType = "ManagementServer"
                            Reason = "WinRM connectivity failed: $($connectivityResult.Error)"
                        }
                    }
                }
            }
            
            # Test Recording Servers
            if ($RecordingServers -and $RecordingServers.Count -gt 0) {
                foreach ($server in $RecordingServers) {
                    Write-Host "  Testing $server..." -NoNewline
                    $connectivityResult = Test-WinRMConnectivity -ComputerName $server -Credential $Credential
                    
                    if ($connectivityResult.Success) {
                        Write-Host " Success" -ForegroundColor Green
                    } else {
                        Write-Host " Failed" -ForegroundColor Red
                        $unreachableServers += $server
                        
                        $results.SkippedServers += @{
                            ServerName = $server
                            ServerType = "RecordingServer"
                            Reason = "WinRM connectivity failed: $($connectivityResult.Error)"
                        }
                    }
                }
            }
            
            # Filter out unreachable servers
            if ($unreachableServers.Count -gt 0) {
                Write-Warning "`nThe following servers are unreachable via WinRM and will be skipped:"
                foreach ($server in $unreachableServers) {
                    Write-Warning "  - $server"
                }
                
                $ManagementServers = $ManagementServers | Where-Object { $unreachableServers -notcontains $_ }
                $RecordingServers = $RecordingServers | Where-Object { $unreachableServers -notcontains $_ }
                
                if (($null -eq $ManagementServers -or $ManagementServers.Count -eq 0) -and
                    ($null -eq $RecordingServers -or $RecordingServers.Count -eq 0)) {
                    Write-Warning "No reachable servers remaining. Distribution canceled."
                    return $results
                }
                
                $confirm = Read-Host "`nContinue with distribution to reachable servers only? (Y/N)"
                if ($confirm -notmatch '^[Yy]$') {
                    Write-Host "Distribution canceled." -ForegroundColor Yellow
                    return $results
                }
            }
        }
        
        # Install to Management Servers
        if ($ManagementServers -and $ManagementServers.Count -gt 0) {
            Write-Host "`nInstalling certificate to Management Servers via WinRM..." -ForegroundColor Cyan
            
            foreach ($server in $ManagementServers) {
                Write-Host "Installing to $server..." -ForegroundColor Yellow
                $result = Copy-CertificateViaWinRM -ComputerName $server -Certificate $Certificate -Credential $Credential -Password $Password -ServerType "ManagementServer"
                
                $serverResult = @{
                    ServerName = $server
                    Success = $result.Success
                }
                
                if ($result.Success) {
                    $results.SuccessCount++
                    $serverResult.Thumbprint = $result.Thumbprint
                    $serverResult.NotAfter = $result.NotAfter
                    Write-Host "  Success" -ForegroundColor Green
                } else {
                    $results.FailureCount++
                    $serverResult.Error = $result.Error
                    Write-Host "  Failed: $($result.Error)" -ForegroundColor Red
                }
                
                $results.ManagementServers += $serverResult
            }
        }
        
        # Install to Recording Servers
        if ($RecordingServers -and $RecordingServers.Count -gt 0) {
            Write-Host "`nInstalling certificate to Recording Servers via WinRM..." -ForegroundColor Cyan
            
            foreach ($server in $RecordingServers) {
                Write-Host "Installing to $server..." -ForegroundColor Yellow
                $result = Copy-CertificateViaWinRM -ComputerName $server -Certificate $Certificate -Credential $Credential -ServerType "RecordingServer" -CreateFolders:$CreateFolders
                
                $serverResult = @{
                    ServerName = $server
                    Success = $result.Success
                }
                
                if ($result.Success) {
                    $results.SuccessCount++
                    $serverResult.CertificateNumber = $result.CertificateNumber
                    Write-Host "  Success (Certificate Number: $($result.CertificateNumber))" -ForegroundColor Green
                } else {
                    $results.FailureCount++
                    $serverResult.Error = $result.Error
                    Write-Host "  Failed: $($result.Error)" -ForegroundColor Red
                }
                
                $results.RecordingServers += $serverResult
            }
        }
        
        return $results
    } catch {
        Write-Error "Failed to distribute certificates via WinRM: $($_.Exception.Message)"
        Write-Log "Failed to distribute certificates via WinRM: $($_.Exception.Message)" -Level 'Error'
        return @{
            ManagementServers = @()
            RecordingServers = @()
            SuccessCount = 0
            FailureCount = 1
            Error = $_.Exception.Message
        }
    }
}
