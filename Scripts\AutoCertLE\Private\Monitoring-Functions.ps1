# Function to check certificate expiration
function Test-CertificateExpiration {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Certificate,
        
        [Parameter()]
        [int]$WarningThresholdDays = 14,
        
        [Parameter()]
        [int]$CriticalThresholdDays = 7
    )
    
    try {
        $now = Get-Date
        $expirationDate = $Certificate.Certificate.NotAfter
        $daysUntilExpiration = ($expirationDate - $now).Days
        
        $status = @{
            Certificate = $Certificate
            ExpirationDate = $expirationDate
            DaysUntilExpiration = $daysUntilExpiration
            Status = 'OK'
            IsExpired = $false
            IsCritical = $false
            IsWarning = $false
        }
        
        if ($daysUntilExpiration -lt 0) {
            $status.Status = 'Expired'
            $status.IsExpired = $true
        } elseif ($daysUntilExpiration -le $CriticalThresholdDays) {
            $status.Status = 'Critical'
            $status.IsCritical = $true
        } elseif ($daysUntilExpiration -le $WarningThresholdDays) {
            $status.Status = 'Warning'
            $status.IsWarning = $true
        }
        
        return $status
    } catch {
        Write-Error "Failed to check certificate expiration: $($_.Exception.Message)"
        Write-Log "Failed to check certificate expiration: $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to monitor all certificates
function Get-CertificateMonitoringReport {
    [CmdletBinding()]
    param (
        [Parameter()]
        [int]$WarningThresholdDays,
        
        [Parameter()]
        [int]$CriticalThresholdDays
    )
    
    try {
        $settings = Get-ScriptSettings
        
        if (-not $PSBoundParameters.ContainsKey('WarningThresholdDays')) {
            $WarningThresholdDays = $settings.ExpirationWarningThresholdDays
        }
        
        if (-not $PSBoundParameters.ContainsKey('CriticalThresholdDays')) {
            $CriticalThresholdDays = $settings.ExpirationCriticalThresholdDays
        }
        
        $certificates = Get-ExistingCertificates
        $report = @{
            Certificates = @()
            TotalCount = $certificates.Count
            OkCount = 0
            WarningCount = 0
            CriticalCount = 0
            ExpiredCount = 0
            GeneratedDate = Get-Date
        }
        
        foreach ($cert in $certificates) {
            $status = Test-CertificateExpiration -Certificate $cert -WarningThresholdDays $WarningThresholdDays -CriticalThresholdDays $CriticalThresholdDays
            
            if ($status) {
                $report.Certificates += $status
                
                if ($status.IsExpired) {
                    $report.ExpiredCount++
                } elseif ($status.IsCritical) {
                    $report.CriticalCount++
                } elseif ($status.IsWarning) {
                    $report.WarningCount++
                } else {
                    $report.OkCount++
                }
            }
        }
        
        return $report
    } catch {
        Write-Error "Failed to generate certificate monitoring report: $($_.Exception.Message)"
        Write-Log "Failed to generate certificate monitoring report: $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to display certificate monitoring report
function Show-CertificateMonitoringReport {
    [CmdletBinding()]
    param (
        [Parameter()]
        [switch]$AutoRenew,
        
        [Parameter()]
        [switch]$SendNotification
    )
    
    try {
        $settings = Get-ScriptSettings
        
        if (-not $settings.EnableExpirationMonitoring) {
            Write-Warning "Certificate monitoring is disabled. Enable it in the settings menu."
            return
        }
        
        $report = Get-CertificateMonitoringReport
        
        if (-not $report) {
            Write-Warning "Failed to generate certificate monitoring report."
            return
        }
        
        Clear-Host
        Write-Host "=== Certificate Monitoring Report ===`n" -ForegroundColor Cyan
        Write-Host "Generated: $($report.GeneratedDate)"
        Write-Host "Total Certificates: $($report.TotalCount)"
        Write-Host "OK: $($report.OkCount)" -ForegroundColor Green
        Write-Host "Warning: $($report.WarningCount)" -ForegroundColor Yellow
        Write-Host "Critical: $($report.CriticalCount)" -ForegroundColor Red
        Write-Host "Expired: $($report.ExpiredCount)" -ForegroundColor Red
        
        Write-Host "`nCertificate Details:" -ForegroundColor Cyan
        $report.Certificates | Sort-Object -Property DaysUntilExpiration | ForEach-Object {
            $statusColor = switch ($_.Status) {
                'OK' { 'Green' }
                'Warning' { 'Yellow' }
                'Critical' { 'Red' }
                'Expired' { 'Red' }
                default { 'White' }
            }
            
            Write-Host "`n$($_.Certificate.MainDomain)" -ForegroundColor $statusColor
            Write-Host "  Status: $($_.Status)" -ForegroundColor $statusColor
            Write-Host "  Expires: $($_.ExpirationDate)"
            Write-Host "  Days until expiration: $($_.DaysUntilExpiration)"
            
            if ($AutoRenew -and ($_.IsWarning -or $_.IsCritical -or $_.IsExpired)) {
                Write-Host "  Auto-renewing..." -ForegroundColor Yellow
                $renewalResult = Invoke-AutoCertRenewal -MainDomain $_.Certificate.MainDomain -Force
                if ($renewalResult) {
                    Write-Host "  Renewal successful." -ForegroundColor Green
                } else {
                    Write-Host "  Renewal failed." -ForegroundColor Red
                }
            }
        }
        
        if ($SendNotification) {
            # Send email notification with report
            $criticalCerts = $report.Certificates | Where-Object { $_.IsCritical -or $_.IsExpired }
            if ($criticalCerts.Count -gt 0) {
                $subject = "Certificate Monitoring Alert: $($criticalCerts.Count) certificates need attention"
                
                $body = @"
<html>
<body>
<h2>Certificate Monitoring Alert</h2>
<p>The following certificates need immediate attention:</p>
<table border="1" cellpadding="5">
<tr>
<th>Domain</th>
<th>Status</th>
<th>Expiration Date</th>
<th>Days Until Expiration</th>
</tr>
"@
                
                foreach ($cert in $criticalCerts) {
                    $statusColor = if ($cert.IsExpired) { "#FF0000" } else { "#FFA500" }
                    $body += @"
<tr>
<td>$($cert.Certificate.MainDomain)</td>
<td style="background-color: $statusColor">$($cert.Status)</td>
<td>$($cert.ExpirationDate)</td>
<td>$($cert.DaysUntilExpiration)</td>
</tr>
"@
                }
                
                $body += @"
</table>
<p>Please take action to renew these certificates as soon as possible.</p>
<p>This is an automated notification from the AutoCertLE certificate management system.</p>
</body>
</html>
"@
                
                Send-EmailNotification -Subject $subject -Body $body
            }
        }
        
        return $report
    } catch {
        Write-Error "Failed to display certificate monitoring report: $($_.Exception.Message)"
        Write-Log "Failed to display certificate monitoring report: $($_.Exception.Message)" -Level 'Error'
        return $null
    }
}

# Function to schedule certificate monitoring
function Set-CertificateMonitoringSchedule {
    [CmdletBinding()]
    param (
        [Parameter()]
        [switch]$Enable,
        
        [Parameter()]
        [switch]$Disable,
        
        [Parameter()]
        [int]$DailyHour = 8,
        
        [Parameter()]
        [int]$DailyMinute = 0,
        
        [Parameter()]
        [switch]$AutoRenew,
        
        [Parameter()]
        [switch]$SendNotification
    )
    
    try {
        $settings = Get-ScriptSettings
        
        if ($Disable) {
            $settings.EnableExpirationMonitoring = $false
            Save-ScriptSettings -Settings $settings
            
            # Remove scheduled task if it exists
            $taskName = "AutoCertLE_CertificateMonitoring"
            $taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
            
            if ($taskExists) {
                Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
                Write-Host "Certificate monitoring schedule disabled." -ForegroundColor Green
                Write-Log "Certificate monitoring schedule disabled." -Level 'Info'
            }
            
            return $true
        }
        
        if ($Enable -or $settings.EnableExpirationMonitoring) {
            $settings.EnableExpirationMonitoring = $true
            Save-ScriptSettings -Settings $settings
            
            # Create scheduled task
            $taskName = "AutoCertLE_CertificateMonitoring"
            $taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
            
            $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-NoProfile -ExecutionPolicy Bypass -Command `"Import-Module AutoCertLE; Show-CertificateMonitoringReport -AutoRenew:$($AutoRenew.IsPresent) -SendNotification:$($SendNotification.IsPresent)`""
            $trigger = New-ScheduledTaskTrigger -Daily -At "$($DailyHour.ToString('00')):$($DailyMinute.ToString('00'))"
            $settings = New-ScheduledTaskSettingsSet -StartWhenAvailable -DontStopOnIdleEnd -AllowStartIfOnBatteries
            
            if ($taskExists) {
                # Update existing task
                Set-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings
            } else {
                # Create new task
                Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Description "AutoCertLE Certificate Monitoring"
            }
            
            Write-Host "Certificate monitoring scheduled for $($DailyHour.ToString('00')):$($DailyMinute.ToString('00')) daily." -ForegroundColor Green
            Write-Log "Certificate monitoring scheduled for $($DailyHour.ToString('00')):$($DailyMinute.ToString('00')) daily." -Level 'Info'
            
            return $true
        }
        
        return $false
    } catch {
        Write-Error "Failed to set certificate monitoring schedule: $($_.Exception.Message)"
        Write-Log "Failed to set certificate monitoring schedule: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}
