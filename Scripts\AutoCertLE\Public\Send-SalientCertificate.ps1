<#
.SYNOPSIS
    Distributes a certificate to multiple Salient CompleteView servers.

.DESCRIPTION
    Distributes a Let's Encrypt certificate to multiple Salient CompleteView Management Servers and/or Recording Servers.
    This is particularly useful for wildcard certificates that need to be installed on multiple servers.

.PARAMETER Certificate
    The certificate object to distribute. If not provided, you will be prompted to select from existing certificates.

.PARAMETER ManagementServers
    An array of Management Server names to install the certificate to.

.PARAMETER RecordingServers
    An array of Recording Server names to install the certificate to.

.PARAMETER Password
    The password to use for PFX files when installing to Management Servers.

.PARAMETER CreateFolders
    Creates the certificate folders if they don't exist on Recording Servers.

.PARAMETER SkipConnectivityTest
    Skips the connectivity test before attempting to distribute certificates. Use this if you're sure all servers are reachable.

.PARAMETER UseWinRM
    Uses Windows Remote Management (WinRM) for certificate distribution instead of file shares. This may work better through firewalls.

.PARAMETER Credential
    Specifies the credentials to use for remote connections when using WinRM. If not provided, the current user's credentials are used.

.EXAMPLE
    Send-SalientCertificate

.EXAMPLE
    Send-SalientCertificate -ManagementServers "MS01","MS02" -RecordingServers "RS01","RS02","RS03"

.EXAMPLE
    Send-SalientCertificate -Certificate $cert -RecordingServers "RS01","RS02" -CreateFolders

.EXAMPLE
    Send-SalientCertificate -ManagementServers "MS01","MS02" -SkipConnectivityTest

.EXAMPLE
    Send-SalientCertificate -ManagementServers "MS01" -RecordingServers "RS01" -UseWinRM

.EXAMPLE
    Send-SalientCertificate -ManagementServers "MS01" -UseWinRM -Credential (Get-Credential)
#>
function Send-SalientCertificate {
    [CmdletBinding()]
    param (
        [Parameter()]
        [object]$Certificate,

        [Parameter()]
        [string[]]$ManagementServers,

        [Parameter()]
        [string[]]$RecordingServers,

        [Parameter()]
        [securestring]$Password,

        [Parameter()]
        [switch]$CreateFolders,

        [Parameter()]
        [switch]$SkipConnectivityTest,

        [Parameter()]
        [switch]$UseWinRM,

        [Parameter()]
        [System.Management.Automation.PSCredential]$Credential
    )

    try {
        # If no certificate is provided, prompt to select one
        if (-not $Certificate) {
            $certificates = Get-ExistingCertificates

            if ($certificates.Count -eq 0) {
                Write-Warning "No certificates found. Please register a certificate first."
                return $false
            }

            Write-Host "`nSelect a certificate to distribute:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $certificates.Count; $i++) {
                Write-Host "$($i + 1)) $($certificates[$i].MainDomain) (expires: $($certificates[$i].Certificate.NotAfter))"
            }

            $certChoice = Get-ValidatedInput -Prompt "`nEnter your choice (1-$($certificates.Count))" -ValidOptions (1..$certificates.Count)
            $Certificate = $certificates[$certChoice - 1]
        }

        # If no servers are provided, prompt for them
        if ((-not $ManagementServers -or $ManagementServers.Count -eq 0) -and
            (-not $RecordingServers -or $RecordingServers.Count -eq 0)) {

            Write-Host "`nEnter Management Server names (comma-separated, leave blank for none):" -ForegroundColor Cyan
            $mgmtInput = Read-Host

            if (-not [string]::IsNullOrWhiteSpace($mgmtInput)) {
                $ManagementServers = $mgmtInput -split ',' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
            }

            Write-Host "`nEnter Recording Server names (comma-separated, leave blank for none):" -ForegroundColor Cyan
            $recInput = Read-Host

            if (-not [string]::IsNullOrWhiteSpace($recInput)) {
                $RecordingServers = $recInput -split ',' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
            }

            if ((-not $ManagementServers -or $ManagementServers.Count -eq 0) -and
                (-not $RecordingServers -or $RecordingServers.Count -eq 0)) {
                Write-Warning "No servers specified. Distribution canceled."
                return $false
            }
        }

        # If password is not provided and there are Management Servers, prompt for it
        if (-not $Password -and $ManagementServers -and $ManagementServers.Count -gt 0) {
            $Password = Read-Host -AsSecureString "Enter password for PFX files (leave blank for no password)"
        }

        # Confirm distribution
        $mgmtCount = if ($ManagementServers) { $ManagementServers.Count } else { 0 }
        $recCount = if ($RecordingServers) { $RecordingServers.Count } else { 0 }
        $serverCount = $mgmtCount + $recCount
        Write-Host "`nReady to distribute certificate for $($Certificate.MainDomain) to $serverCount servers." -ForegroundColor Cyan

        if ($ManagementServers -and $ManagementServers.Count -gt 0) {
            Write-Host "Management Servers: $($ManagementServers -join ', ')"
        }

        if ($RecordingServers -and $RecordingServers.Count -gt 0) {
            Write-Host "Recording Servers: $($RecordingServers -join ', ')"
        }

        $confirm = Read-Host "`nContinue with distribution? (Y/N)"
        if ($confirm -notmatch '^[Yy]$') {
            Write-Host "Distribution canceled." -ForegroundColor Yellow
            return $false
        }

        # Distribute the certificate
        if ($UseWinRM) {
            Write-Host "`nUsing Windows Remote Management (WinRM) for certificate distribution..." -ForegroundColor Cyan
            $results = Send-CertificatesViaWinRM -Certificate $Certificate -ManagementServers $ManagementServers -RecordingServers $RecordingServers -Credential $Credential -Password $Password -CreateFolders:$CreateFolders -SkipConnectivityTest:$SkipConnectivityTest
        } else {
            Write-Host "`nUsing file shares for certificate distribution..." -ForegroundColor Cyan
            $results = Send-CertificateToServers -Certificate $Certificate -ManagementServers $ManagementServers -RecordingServers $RecordingServers -Password $Password -CreateFolders:$CreateFolders -SkipConnectivityTest:$SkipConnectivityTest
        }

        # Display summary
        Write-Host "`nCertificate Distribution Summary:" -ForegroundColor Cyan
        Write-Host "Certificate: $($Certificate.MainDomain)"
        Write-Host "Successful installations: $($results.SuccessCount)" -ForegroundColor Green
        Write-Host "Failed installations: $($results.FailureCount)" -ForegroundColor $(if ($results.FailureCount -gt 0) { "Red" } else { "Green" })

        if ($results.ManagementServers.Count -gt 0) {
            Write-Host "`nManagement Server Results:" -ForegroundColor Cyan
            foreach ($result in $results.ManagementServers) {
                if ($result.Success) {
                    Write-Host "  $($result.ServerName): Success" -ForegroundColor Green
                } else {
                    Write-Host "  $($result.ServerName): Failed - $($result.ErrorMessage)" -ForegroundColor Red
                }
            }
        }

        if ($results.RecordingServers.Count -gt 0) {
            Write-Host "`nRecording Server Results:" -ForegroundColor Cyan
            foreach ($result in $results.RecordingServers) {
                if ($result.Success) {
                    Write-Host "  $($result.ServerName): Success (Certificate Number: $($result.CertificateNumber))" -ForegroundColor Green
                } else {
                    Write-Host "  $($result.ServerName): Failed - $($result.ErrorMessage)" -ForegroundColor Red
                }
            }
        }

        Write-Host "`nIMPORTANT: You must manually select these certificates in the CompleteView Management Console." -ForegroundColor Yellow
        Write-Host "Do NOT restart services as this may cause recording interruptions." -ForegroundColor Yellow

        return $results
    } catch {
        Write-Error "Failed to distribute certificate: $($_.Exception.Message)"
        Write-Log "Failed to distribute certificate: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}
