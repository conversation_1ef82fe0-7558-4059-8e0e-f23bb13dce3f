# Function to initialize logging
function Initialize-Logging {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$LogDirectory = "$env:LOCALAPPDATA\PoshACME\logs",

        [Parameter()]
        [int]$MaxLogSizeMB = 10,

        [Parameter()]
        [int]$MaxLogFiles = 5
    )

    # Create log directory if it doesn't exist
    if (-not (Test-Path $LogDirectory)) {
        New-Item -ItemType Directory -Path $LogDirectory -Force | Out-Null
    }

    # Set the log file path
    $script:logFile = Join-Path -Path $LogDirectory -ChildPath "AutoCertLE.log"
    $script:maxLogSizeMB = $MaxLogSizeMB
    $script:maxLogFiles = $MaxLogFiles

    # Perform log rotation if needed
    Start-LogRotation
}

# Function to rotate logs
function Start-LogRotation {
    [CmdletBinding()]
    param()

    if (-not (Test-Path $script:logFile)) {
        return
    }

    # Check if log file exceeds maximum size
    $logFileInfo = Get-Item $script:logFile
    if ($logFileInfo.Length -gt ($script:maxLogSizeMB * 1MB)) {
        Write-Verbose "Log file exceeds maximum size, rotating logs"

        # Rotate existing log files
        for ($i = $script:maxLogFiles - 1; $i -ge 1; $i--) {
            $oldLog = "$script:logFile.$i"
            $newLog = "$script:logFile.$($i + 1)"

            if (Test-Path $oldLog) {
                if ($i -eq $script:maxLogFiles - 1) {
                    # Delete the oldest log file
                    Remove-Item $oldLog -Force
                } else {
                    # Rename log file
                    Move-Item $oldLog $newLog -Force
                }
            }
        }

        # Rename current log file
        Move-Item $script:logFile "$script:logFile.1" -Force
    }
}

# Function to log messages
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter()]
        [ValidateSet('Info', 'Warning', 'Error', 'Success', 'Debug')]
        [string]$Level = 'Info',

        [Parameter()]
        [switch]$NoRotate
    )

    # Only log if the message is meaningful
    if ([string]::IsNullOrWhiteSpace($Message)) {
        return
    }

    # Filter out routine informational messages
    if ($Level -eq 'Info') {
        $routinePatterns = @(
            'ACME server set to',
            'Certificate cache cleared',
            'User exited the script',
            'Selected certificate:',
            'Detecting DNS provider'
        )

        foreach ($pattern in $routinePatterns) {
            if ($Message -like "*$pattern*") {
                return
            }
        }
    }

    # Perform log rotation if needed and not explicitly disabled
    if (-not $NoRotate) {
        Start-LogRotation
    }

    # Format the log message
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logMessage = "$timestamp [$Level] $Message"

    # Write to log file
    try {
        $logMessage | Out-File -FilePath $script:logFile -Append -ErrorAction Stop
    } catch {
        Write-Warning "Failed to write to log file: $($_.Exception.Message)"
    }

    # Also output debug messages to console when in verbose mode
    if ($Level -eq 'Debug' -and $VerbosePreference -eq 'Continue') {
        Write-Verbose $Message
    }
}

# Function to export logs
function Export-Logs {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$ExportPath = [Environment]::GetFolderPath("Desktop")
    )

    $exportFileName = "AutoCertLE_Logs_$(Get-Date -Format 'yyyyMMdd_HHmmss').zip"
    $exportFilePath = Join-Path -Path $ExportPath -ChildPath $exportFileName

    try {
        $logDirectory = Split-Path -Parent $script:logFile
        $logFiles = Get-ChildItem -Path $logDirectory -Filter "AutoCertLE*log*"

        if (-not $logFiles) {
            Write-Warning "No log files found to export"
            return $null
        }

        # Create a temporary directory
        $tempDir = Join-Path -Path ([System.IO.Path]::GetTempPath()) -ChildPath ([System.Guid]::NewGuid().ToString())
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

        # Copy log files to temporary directory
        foreach ($logFile in $logFiles) {
            Copy-Item -Path $logFile.FullName -Destination $tempDir
        }

        # Create zip file
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $exportFilePath)

        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force

        Write-Host "Logs exported to: $exportFilePath" -ForegroundColor Green
        return $exportFilePath
    } catch {
        Write-Error "Failed to export logs: $($_.Exception.Message)"
        return $null
    }
}

# Initialize logging when the module is loaded
Initialize-Logging
