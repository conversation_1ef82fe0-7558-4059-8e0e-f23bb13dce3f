:: Set script directory path to Scripts subfolder
set "SCRIPT_DIR=%~dp0Scripts\"

:: Check if PowerShell script exists
if not exist "%SCRIPT_DIR%CameraAudit_v7.ps1" (
    echo ===============================================================
    echo ERROR: Required files not found
    echo ===============================================================
    echo.
    echo Main script not found in CameraAudit v7\Scripts directory.
    echo Expected location: CameraAudit v7\Scripts\CameraAudit_v7.ps1
    echo Current path: %SCRIPT_DIR%CameraAudit_v7.ps1
    echo.
    echo Please ensure all files are extracted correctly and maintain
    echo the original folder structure.
    echo.
    pause
    exit /b 1
)
echo ===============================================================
echo Launching CompleteView Camera Audit Tool v7...
echo ===============================================================
echo.
:: Launch PowerShell with execution policy bypass
powershell.exe -NoProfile -ExecutionPolicy Bypass -File "%SCRIPT_DIR%CameraAudit_v7.ps1"
:: Check if PowerShell execution was successful
if %errorLevel% neq 0 (
    echo.
    echo ===============================================================
    echo ERROR: Script execution failed
    echo ===============================================================
    echo.
    echo Error code: %errorLevel%
    echo.
    echo Please ensure you're running the batch file as Administrator.
    echo If the error persists, contact technical support.
    echo.
    pause
    exit /b %errorLevel%
)
exit /b 0