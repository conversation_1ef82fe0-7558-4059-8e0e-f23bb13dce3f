BeforeAll {
    # Import the module
    $modulePath = Join-Path -Path $PSScriptRoot -ChildPath '..\AutoCertLE\AutoCertLE.psd1'
    Import-Module $modulePath -Force
}

Describe "AutoCertLE Module Tests" {
    Context "Module Loading" {
        It "Should load the module without errors" {
            Get-Module AutoCertLE | Should -Not -BeNullOrEmpty
        }
        
        It "Should export the expected functions" {
            $expectedFunctions = @(
                'Register-Certificate',
                'Install-Certificate',
                'Install-SalientCertificate',
                'Send-SalientCertificate',
                'Set-AutomaticRenewal',
                'Get-ExistingCertificates',
                'Revoke-Certificate',
                'Remove-Certificate'
            )
            
            foreach ($function in $expectedFunctions) {
                Get-Command -Module AutoCertLE -Name $function -ErrorAction SilentlyContinue | 
                    Should -Not -BeNullOrEmpty -Because "function $function should be exported"
            }
        }
    }
    
    Context "DNS Provider Detection" {
        It "Should detect Cloudflare for cloudflare.com" {
            # Mock the DNS resolution to return Cloudflare nameservers
            Mock Resolve-DnsName {
                return @(
                    @{
                        Type = 'NS'
                        NameHost = 'ns1.cloudflare.com'
                    },
                    @{
                        Type = 'NS'
                        NameHost = 'ns2.cloudflare.com'
                    }
                )
            } -ParameterFilter { $Name -eq 'cloudflare.com' -and $Type -eq 'NS' }
            
            # Call the function with mocked dependencies
            $result = Get-DomainDNSProvider -Domain 'cloudflare.com' -IncludeDetails
            
            # Verify the result
            $result.Provider | Should -Be 'Cloudflare'
            $result.Confidence | Should -BeGreaterThan 70
        }
        
        It "Should detect Route53 for aws.amazon.com" {
            # Mock the DNS resolution to return AWS nameservers
            Mock Resolve-DnsName {
                return @(
                    @{
                        Type = 'NS'
                        NameHost = 'ns-1.awsdns-01.com'
                    },
                    @{
                        Type = 'NS'
                        NameHost = 'ns-2.awsdns-02.net'
                    }
                )
            } -ParameterFilter { $Name -eq 'aws.amazon.com' -and $Type -eq 'NS' }
            
            # Call the function with mocked dependencies
            $result = Get-DomainDNSProvider -Domain 'aws.amazon.com' -IncludeDetails
            
            # Verify the result
            $result.Provider | Should -Be 'Route53'
            $result.Confidence | Should -BeGreaterThan 70
        }
        
        It "Should fall back to Manual when detection fails" {
            # Mock the DNS resolution to return unknown nameservers
            Mock Resolve-DnsName {
                return @(
                    @{
                        Type = 'NS'
                        NameHost = 'ns1.unknown-provider.com'
                    },
                    @{
                        Type = 'NS'
                        NameHost = 'ns2.unknown-provider.com'
                    }
                )
            } -ParameterFilter { $Name -eq 'example.com' -and $Type -eq 'NS' }
            
            # Call the function with mocked dependencies
            $result = Get-SuggestedDNSPlugin -Domain 'example.com'
            
            # Verify the result
            $result.SuggestedPlugin | Should -Be 'Manual'
        }
    }
    
    Context "Certificate Management" {
        BeforeAll {
            # Mock the Posh-ACME functions
            Mock Get-PAServer { return @{ Name = 'LE_STAGE' } }
            Mock Set-PAServer { return $true }
            Mock New-PACertificate { 
                return @{
                    MainDomain = 'test.example.com'
                    Certificate = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new()
                    CertFile = 'C:\fakepath\cert.cer'
                    KeyFile = 'C:\fakepath\cert.key'
                    PfxFile = 'C:\fakepath\cert.pfx'
                }
            }
            Mock Get-PACertificate {
                return @{
                    MainDomain = 'test.example.com'
                    Certificate = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new()
                    CertFile = 'C:\fakepath\cert.cer'
                    KeyFile = 'C:\fakepath\cert.key'
                    PfxFile = 'C:\fakepath\cert.pfx'
                }
            }
        }
        
        It "Should get existing certificates" {
            Mock Get-PACertificate {
                return @(
                    @{
                        MainDomain = 'test1.example.com'
                        Certificate = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new()
                    },
                    @{
                        MainDomain = 'test2.example.com'
                        Certificate = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new()
                    }
                )
            }
            
            $certs = Get-ExistingCertificates
            $certs.Count | Should -Be 2
            $certs[0].MainDomain | Should -Be 'test1.example.com'
        }
    }
    
    Context "Salient-Specific Functions" {
        It "Should detect server types correctly" {
            # Mock the Get-Service function
            Mock Get-Service { return $true } -ParameterFilter { $Name -eq 'CompleteView Management Server' }
            Mock Get-Service { return $null } -ParameterFilter { $Name -eq 'CompleteView Recording Server' }
            
            $serverInfo = Get-SalientServerType
            $serverInfo.IsManagementServer | Should -Be $true
            $serverInfo.IsRecordingServer | Should -Be $false
        }
        
        It "Should find Recording Server certificate folder" {
            # Mock the Test-Path function
            Mock Test-Path { return $true } -ParameterFilter { 
                $Path -eq "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates" 
            }
            
            $folder = Get-RecordingServerCertFolder
            $folder | Should -Be "C:\Program Files\Salient Security Platform\CompleteView 2020\Recording Server\Certificates"
        }
    }
    
    Context "WinRM Functions" {
        It "Should test WinRM connectivity" {
            # Mock the Invoke-Command function
            Mock Invoke-Command { return $env:COMPUTERNAME } -ParameterFilter { $ComputerName -eq 'localhost' }
            
            $result = Test-WinRMConnectivity -ComputerName 'localhost'
            $result.Success | Should -Be $true
        }
    }
}
