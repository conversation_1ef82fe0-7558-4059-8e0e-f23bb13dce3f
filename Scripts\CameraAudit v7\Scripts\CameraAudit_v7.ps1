<#
.SYNOPSIS
    Retrieves server and camera information and exports it to an Excel spreadsheet.

.DESCRIPTION
    This PowerShell script extracts camera information from CV v7 recording servers.
    Queries SQL to retrieve all recording server information, then performs API GET 
    requests to gather camera stream properties and configuration data, matching
    cameras based on GUIDs. Exports the aggregated data to an Excel spreadsheet
    on the user's desktop.
    
    Execute only on the Management Server.

.NOTES
    Author: <EMAIL>
    Creation Date: 05/23/24
    Last Updated: 02/12/25

.CHANGELOG
    Version 2.0 - 02/12/25
    
    NEW FEATURES:
    - Added timestamp to output filename for better version control
    - Implemented local module detection before attempting online installation
    - Added interactive folder browser when config file not found in default locations
    - Added manufacturer information to camera details export
    
    IMPROVEMENTS:
    - Enhanced credential handling using PSCredential object for better security
    - Added IP address resolution caching to improve performance
    - Improved error handling with detailed HTTP status code responses
    - Added progress tracking for server processing
    - Added TCP port testing before API calls
    - Added proper cleanup of sensitive data in memory
    - Added timestamp to failed server records
    
    SECURITY:
    - Implemented proper cleanup of credentials from memory
    
    PERFORMANCE:
    - Added IP address resolution caching
    
    RELIABILITY:
    - Added detailed error handling for HTTP status codes
    - Added connection testing before API calls
    - Improved ImportExcel module installation process
    - Added error handling for SQL connections
    
    UI/UX:
    - Added progress indicators for server processing
    - Enhanced console output with color coding
    - Added success/failure counters during processing
    - Improved Excel output formatting with frozen headers and bold top row
    
    BUG FIXES:
    - Fixed potential memory leaks in SQL connections
    - Addressed missing error handling in hostname resolution
    - Fixed Excel worksheet formatting issues
    
    DOCUMENTATION:
    - Added detailed function documentation
    - Improved error message clarity
    - Added status tracking for server processing
#>

# Stop script on non-terminating errors
$ErrorActionPreference = 'Stop'

# Define paths and parameters
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$OutputPath = Join-Path ([Environment]::GetFolderPath("Desktop")) "CameraDetails_${timestamp}.xlsx"

# Add parameter validation
[ValidateRange(1,300)]
[int]$ApiTimeout = 30

# Initialize caches and counters
$script:resolvedIPCache = @{}

function Install-ImportExcelModule {
    # First check if module is already installed
    if (Get-Module -ListAvailable -Name ImportExcel) {
        Import-Module ImportExcel -ErrorAction Stop
        Write-Host "ImportExcel module already installed" -ForegroundColor Green
        return
    }

    # Get script parent directory
    $rootPath = Split-Path $PSScriptRoot -Parent
    
    # Try local installation first from Modules folder in root
    Write-Host "Module not found. Checking for local module..." -ForegroundColor Cyan
    $localModulePath = Join-Path $rootPath "Modules\ImportExcel"
    
    if (Test-Path $localModulePath) {
        Write-Host "ImportExcel module found in local Modules folder" -ForegroundColor Green
        try {
            Import-Module $localModulePath -ErrorAction Stop
            Write-Host "ImportExcel module loaded from local path" -ForegroundColor Green
            return
        }
        catch {
            Write-Warning "Failed to import local ImportExcel module: $($_.Exception.Message)"
            Write-Host "Attempting online installation..." -ForegroundColor Cyan
        }
    }
    else {
        Write-Host "Local module not found. Attempting online installation..." -ForegroundColor Cyan
    }

    # Try online installation if local fails
    try {
        Install-Module -Name ImportExcel -Scope CurrentUser -Force -ErrorAction Stop
        Write-Host "ImportExcel module installed successfully from online repository" -ForegroundColor Green
        Import-Module ImportExcel -ErrorAction Stop
    }
    catch {
        Write-Error "Failed to install ImportExcel module from any location: $($_.Exception.Message)"
        throw
    }
}

function Resolve-HttpError {
    param(
        [Parameter(Mandatory=$true)]
        [System.Net.WebException]$Exception,
        [string]$ServerName
    )
    
    $response = $Exception.Response
    if ($response) {
        $statusCode = [int]$response.StatusCode
        
        switch ($statusCode) {
            401 { 
                Write-Warning "Authentication failed for server $ServerName. Please verify credentials."
                return "Auth Failed: Invalid credentials"
            }
            403 { 
                Write-Warning "Access forbidden for server $ServerName. Check user permissions."
                return "Auth Failed: Insufficient permissions"
            }
            404 { 
                Write-Warning "API endpoint not found on server $ServerName. Verify server version."
                return "API Error: Endpoint not found"
            }
            500 { 
                Write-Warning "Internal server error on $ServerName."
                return "Server Error: Internal error"
            }
            502 { 
                Write-Warning "Bad gateway error for $ServerName. Check network connectivity."
                return "Network Error: Bad gateway"
            }
            503 { 
                Write-Warning "Service unavailable on $ServerName. Server might be starting up or overloaded."
                return "Server Error: Service unavailable"
            }
            504 { 
                Write-Warning "Gateway timeout for $ServerName. Check network connectivity."
                return "Network Error: Gateway timeout"
            }
            default {
                Write-Warning "HTTP Error $statusCode occurred for server $ServerName"
                return "HTTP Error: Status $statusCode"
            }
        }
    }
    else {
        Write-Warning "Network error occurred for server ${ServerName}: $($Exception.Message)"
        return "Network Error: Connection failed"
    }
}

function Get-JsonFilePath {
    # Define possible appsettings.json paths
    $possiblePaths = @(
        "C:\Program Files\Salient Security Platform\CompleteView\Management Server\appsettings.json",
        "C:\Program Files\Salient Security Platform\CompleteView 2020\Management Server\appsettings.json",
        "C:\Program Files\Symmetry Security Platform\Symmetry CompleteView\Management Server\appsettings.json"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            Write-Host "Found appsettings.json in default location" -ForegroundColor Green
            return $path
        }
    }

    Write-Warning "appsettings.json not found in default locations"
    Write-Host "Please select the Management Server application folder" -ForegroundColor Cyan
    
    Add-Type -AssemblyName System.Windows.Forms
    $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
    $folderBrowser.Description = "Select Management Server application folder"
    $folderBrowser.RootFolder = "MyComputer"
    
    $dialogResult = $folderBrowser.ShowDialog()
    if ($dialogResult -eq [System.Windows.Forms.DialogResult]::OK) {
        $selectedPath = $folderBrowser.SelectedPath
        $possibleFile = Join-Path -Path $selectedPath -ChildPath "appsettings.json"
        
        if (Test-Path $possibleFile) {
            Write-Host "Found appsettings.json in selected location" -ForegroundColor Green
            return $possibleFile
        }
        else {
            Write-Error "appsettings.json not found in selected folder"
            throw "Configuration file not found"
        }
    }
    else {
        Write-Error "Folder selection canceled by user"
        throw "User canceled selection"
    }
}

function Get-ConnectionString {
    Write-Host "Reading SQL connection string..." -ForegroundColor Cyan
    try {
        $jsonContent = Get-Content -Path $jsonFilePath -Raw | ConvertFrom-Json -ErrorAction Stop
        if ([string]::IsNullOrWhiteSpace($jsonContent.ConnectionString)) {
            throw "Connection string is empty in config file"
        }
        Write-Host "SQL connection string retrieved" -ForegroundColor Green
        return $jsonContent.ConnectionString
    }
    catch {
        throw "Failed to parse connection string from config: $($_.Exception.Message)"
    }
}

function Get-SQLServerInfo {
    Write-Host "Querying SQL for recording servers..." -ForegroundColor Cyan
    $query = @"
SELECT 
    rs.Name AS DisplayName,
    rs.RecordingServerId,
    com.HostAddress AS IP
FROM 
    rs.RecordingServers rs
JOIN 
    com.Connections com
    ON rs.ConnectionId = com.ConnectionId
WHERE
    rs.IsEnabled = 1;
"@
    
    $serverInfoArray = @()
    $sqlConnection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    try {
        $sqlConnection.Open()
        $sqlCmd = $sqlConnection.CreateCommand()
        $sqlCmd.CommandTimeout = 30  # Add timeout
        $sqlCmd.CommandText = $query
        $reader = $sqlCmd.ExecuteReader()
        
        while ($reader.Read()) {
            $serverInfoArray += [PSCustomObject]@{
                DisplayName = $reader["DisplayName"]
                ServerId = $reader["RecordingServerId"]
                IP = $reader["IP"]
                Status = "Pending"
            }
        }
    }
    catch [System.Data.SqlClient.SqlException] {
        Write-Error "SQL Error: $($_.Exception.Message)"
        throw
    }
    finally {
        if ($reader) { $reader.Close() }
        if ($sqlConnection.State -eq 'Open') { $sqlConnection.Close() }
        $sqlConnection.Dispose()
    }
    
    Write-Host "Found $($serverInfoArray.Count) recording servers" -ForegroundColor Green
    return $serverInfoArray
}

function Initialize-ApiAuthentication {
    param(
        [Parameter(Mandatory=$true)]
        [ValidateNotNull()]
        [System.Management.Automation.PSCredential]$Credentials
    )
    
    Write-Host "Setting up API authentication..." -ForegroundColor Cyan
    
    # Validate credentials
    if ([string]::IsNullOrWhiteSpace($Credentials.UserName) -or 
        [string]::IsNullOrWhiteSpace($Credentials.GetNetworkCredential().Password)) {
        throw "Invalid credentials provided"
    }
    
    try {
        $encodedCredentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(
            "$($Credentials.UserName):$($Credentials.GetNetworkCredential().Password)"
        ))
        return @{ Authorization = "Basic $encodedCredentials" }
    }
    catch {
        throw "Failed to encode credentials: $($_.Exception.Message)"
    }
}

function Resolve-ServerAddresses {
    param(
        [Parameter(Mandatory=$true)]
        [array]$Servers
    )
    
    Write-Host "`nResolving server hostnames..." -ForegroundColor Cyan
    $totalServers = $Servers.Count
    $currentServer = 0
    
    foreach ($server in $Servers) {
        $currentServer++
        Write-Host "[$currentServer/$totalServers] Processing $($server.DisplayName)..." -ForegroundColor Cyan -NoNewline
        
        if ([string]::IsNullOrWhiteSpace($server.IP)) {
            Write-Host " SKIPPED" -ForegroundColor Yellow
            Write-Warning "Server '$($server.DisplayName)' - IP is empty"
            $server.Status = "Empty IP"
            continue
        }
        
        if (-not [System.Net.IPAddress]::TryParse($server.IP, [ref]$null)) {
            $resolvedIP = Resolve-HostnameToIP -Hostname $server.IP
            if ($resolvedIP) {
                $server.IP = $resolvedIP
                Write-Host " RESOLVED to $resolvedIP" -ForegroundColor Green
            }
            else {
                Write-Host " FAILED" -ForegroundColor Red
                Write-Warning "Server '$($server.DisplayName)' - host cannot be resolved"
                $server.Status = "IP Resolution Failed"
                continue
            }
        } else {
            Write-Host " OK (IP already valid)" -ForegroundColor Green
        }
    }
    
    return $Servers
}
function Resolve-HostnameToIP {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Hostname
    )
    
    if ($script:resolvedIPCache.ContainsKey($Hostname)) {
        return $script:resolvedIPCache[$Hostname]
    }
    
    try {
        $ipAddresses = [System.Net.Dns]::GetHostAddresses($Hostname) | 
            Where-Object { $_.AddressFamily -eq 'InterNetwork' }
        
        if ($ipAddresses) {
            $resolvedIP = $ipAddresses[0].IPAddressToString
            $script:resolvedIPCache[$Hostname] = $resolvedIP
            return $resolvedIP
        }
        return $null
    }
    catch {
        return $null
    }
}

function Get-CameraDetails {
    Write-Host "`nGathering camera details from recording servers..." -ForegroundColor Cyan
    $connectedServers = @()
    $failedServers = @()
    
    # Track processing statistics
    $totalServers = $serverInfoArray.Count
    $currentServer = 0
    $successCount = 0
    $failureCount = 0

    if ($serverInfoArray.Count -eq 0) {
        Write-Warning "No servers found to process"
        return @{
            Connected = @()
            Failed = @()
        }
    }

    # Add retry logic for API calls
    $maxRetries = 3
    $retryDelay = 5
    
    foreach ($server in $serverInfoArray) {
        $currentServer++
        Write-Host "`n[$currentServer/$totalServers] Processing $($server.DisplayName)..." -ForegroundColor Cyan
        
        # Skip servers with known resolution failures
        if ($server.Status -in @("IP Resolution Failed", "Empty IP")) {
            $failedServers += [PSCustomObject]@{
                Server = $server.DisplayName
                ServerId = $server.ServerId
                IP = $server.IP
                Status = $server.Status
                TimeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            }
            continue
        }
        
        try {
            # Test server connectivity first
            $testConnection = Test-NetConnection -ComputerName $server.IP -Port 4502 -WarningAction SilentlyContinue
            if (-not $testConnection.TcpTestSucceeded) {
                throw [System.Net.WebException]::new("Server port 4502 is not accessible")
            }

            for ($retry = 1; $retry -le $maxRetries; $retry++) {
                try {
                    $streamProps = Invoke-RestMethod -Uri "http://$($server.IP):4502/v2.0/cameras/streamProps" `
                        -Method GET -Headers $headers -TimeoutSec $ApiTimeout
                    break
                }
                catch {
                    if ($retry -eq $maxRetries) { throw }
                    Write-Warning "Attempt $retry of $maxRetries failed. Retrying in $retryDelay seconds..."
                    Start-Sleep -Seconds $retryDelay
                }
            }

            try {
                $configCameras = Invoke-RestMethod -Uri "http://$($server.IP):4502/v2.0/config/cameras" `
                    -Method GET -Headers $headers -TimeoutSec $ApiTimeout
            }
            catch [System.Net.WebException] {
                $errorStatus = Resolve-HttpError -Exception $_ -ServerName $server.DisplayName
                throw [System.Exception]::new("Failed to get camera configuration: $errorStatus")
            }
            
            # Match cameras by GUID and aggregate properties
            foreach ($camera in $configCameras.cameras) {
                $guid = $camera.cameraEntity.cameraGuid
                $matchingStream = $streamProps.streamProps | Where-Object { $_.guid -eq $guid }
                
                if ($matchingStream) {
                    $connectedServers += [PSCustomObject]@{
                        Server = $server.DisplayName
                        ServerId = $server.ServerId
                        CameraName = $camera.cameraEntity.cameraName
                        CameraGuid = $guid
                        IP = $camera.ipSettings.ipAddress
                        Model = $camera.ipSettings.ipmodel
                        Manufacturer = $camera.ipSettings.manufacturer
                        Compressor = $matchingStream.compressor
                        Bitrate = $matchingStream.bitRateKbps
                        FPS = $matchingStream.frameRateFps
                        Resolution = "$($matchingStream.imageWidth)x$($matchingStream.imageHeight)"
                        Transcoder = $matchingStream.transcoder
                        LastUpdated = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                    }
                }
            }
            
            # Update success metrics
            $server.Status = "Success"
            $successCount++
            Write-Host "  Found $($connectedServers.Count) cameras" -ForegroundColor Green
            Write-Host "  Status: SUCCESS ($successCount succeeded, $failureCount failed)" -ForegroundColor Green
        }
        catch {
            # Log failure details and update metrics
            Write-Warning "Failed to process server $($server.DisplayName): $($_.Exception.Message)"
            $failedServers += [PSCustomObject]@{
                Server = $server.DisplayName
                ServerId = $server.ServerId
                IP = $server.IP
                Status = "Failed: $($_.Exception.Message)"
                TimeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            }
            $server.Status = "Failed"
            $failureCount++
            Write-Host "  Status: FAILED ($successCount succeeded, $failureCount failed)" -ForegroundColor Red
        }
    }
    
    # Return combined results
    return @{
        Connected = $connectedServers
        Failed = $failedServers
    }
}

function Set-ExcelFormatting {
    param (
        [string]$FilePath,
        [string]$WorksheetName
    )
    
    Write-Host "Applying enhanced Excel formatting..." -ForegroundColor Cyan
    
    # Open Excel file for formatting
    $excel = Open-ExcelPackage -Path $FilePath
    $ws = $excel.Workbook.Worksheets[$WorksheetName]
    
    # Determine last column with data
    $lastColumn = $ws.Dimension.Columns
    
    # Create server highlighting tracking
    $serverHighlight = @{}
    $currentServer = $null
    $applyGray = $false
    
    # Assign alternating gray shading based on server groups
    for ($rowIndex = 2; $rowIndex -le $ws.Dimension.Rows; $rowIndex++) {
        $serverName = $ws.Cells["A$rowIndex"].Text
        if ($serverName -ne $currentServer) {
            $currentServer = $serverName
            $applyGray = -not $applyGray
            $serverHighlight[$serverName] = $applyGray
        }
        
        # Apply formatting only if gray shading is toggled on
        if ($serverHighlight[$serverName]) {
            $range = $ws.Cells["A${rowIndex}:$($lastColumn)${rowIndex}"]
            $range.Style.Fill.PatternType = [OfficeOpenXml.Style.ExcelFillStyle]::Solid
            $range.Style.Fill.BackgroundColor.SetColor([System.Drawing.Color]::LightGray)
        }
    }
    
    # Auto-adjust column width
    for ($col = 1; $col -le $lastColumn; $col++) {
        $ws.Column($col).AutoFit()
    }
    
    # Save and close Excel file
    Close-ExcelPackage $excel
    Write-Host "Excel formatting applied successfully." -ForegroundColor Green
}

function Remove-SensitiveData {
    param(
        [Parameter(Mandatory=$false)]
        [System.Management.Automation.PSCredential]$Credentials,
        
        [Parameter(Mandatory=$false)]
        [System.Security.SecureString]$EncodedCredentials,
        
        [Parameter(Mandatory=$false)]
        [hashtable]$Headers
    )
    
    if ($Credentials) {
        $Credentials = $null
    }
    
    if ($EncodedCredentials) {
        $EncodedCredentials = $null
    }
    
    if ($Headers) {
        $Headers.Authorization = $null
        $Headers = $null
    }
    
    [System.GC]::Collect()
}

# Main script execution
$creds = $null
$headers = $null
$sqlConnection = $null

try {
    # Prompt for admin credentials
    Write-Host "`nPlease enter CV admin credentials" -ForegroundColor Cyan
    $creds = Get-Credential -Message "Enter CompleteView admin credentials" -UserName "admin"

    if (-not $creds -or [string]::IsNullOrWhiteSpace($creds.UserName) -or 
        [string]::IsNullOrWhiteSpace($creds.GetNetworkCredential().Password)) {
        throw "Valid credentials are required"
    }

    # Install ImportExcel module
    Install-ImportExcelModule
    
    # Get appsettings.json file path
    $jsonFilePath = Get-JsonFilePath
    Write-Host "Using configuration file: $jsonFilePath" -ForegroundColor Green
    
    # Get SQL connection string
    $connectionString = Get-ConnectionString
    
    # Get recording server information from SQL
    $serverInfoArray = Get-SQLServerInfo
    
    # Setup API authentication
    $headers = Initialize-ApiAuthentication -Credentials $creds
    
    # Resolve server addresses
    $serverInfoArray = Resolve-ServerAddresses -Servers $serverInfoArray
    
    # Gather camera details
    $cameraData = Get-CameraDetails
    
    # Export to Excel
    Write-Host "`nExporting results to spreadsheet..." -ForegroundColor Cyan
    
    if ($cameraData.Connected) {
        # Sort data by Server to ensure grouping
        $sortedData = $cameraData.Connected | Sort-Object Server
        
        # Export to Excel with basic formatting
        $excelParams = @{
            Path = $OutputPath
            WorksheetName = 'Connected Servers'
            AutoSize = $true
            AutoFilter = $true
            FreezeTopRow = $true
            BoldTopRow = $true
        }
        
        $sortedData | Export-Excel @excelParams
        
        # Apply enhanced formatting
        Set-ExcelFormatting -FilePath $OutputPath -WorksheetName "Connected Servers"
        
        Write-Host "Exported $($cameraData.Connected.Count) connected cameras with alternating server backgrounds" -ForegroundColor Green
    }
    else {
        Write-Warning "No connected cameras to export"
    }
    
    if ($cameraData.Failed) {
        $cameraData.Failed | Export-Excel -Path $OutputPath -WorksheetName 'Failed Servers' -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -Append
        Write-Warning "Exported $($cameraData.Failed.Count) failed servers"
    }
    
    Write-Host "`nSpreadsheet created: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Error "Script execution failed: $($_.Exception.Message)"
    throw
}
finally {
    # Enhanced cleanup
    if ($sqlConnection -and $sqlConnection.State -eq 'Open') { 
        $sqlConnection.Close() 
        $sqlConnection.Dispose()
    }
    Remove-SensitiveData -Credentials $creds -Headers $headers
    Write-Host "`nScript complete" -ForegroundColor Green
    Read-Host -Prompt "Press Enter to exit"
}