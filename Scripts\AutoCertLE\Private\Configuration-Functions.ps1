# Function to get renewal configuration
function Get-RenewalConfig {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$ConfigPath = "$env:LOCALAPPDATA\PoshACME\renewal_config.json"
    )

    if (Test-Path $ConfigPath) {
        try {
            $config = Get-Content $ConfigPath | ConvertFrom-Json
            return $config
        } catch {
            Write-Warning "Failed to load renewal configuration: $($_)"
            Write-Log "Failed to load renewal configuration: $($_)" -Level 'Warning'
        }
    }

    # Return default configuration
    return @{
        RenewalHour = 2  # 2 AM
        RenewalMinute = (Get-Random -Minimum 0 -Maximum 59)  # Random minute
        UseRandomization = $true
        RandomizationWindow = 60  # minutes
    }
}

# Function to save renewal configuration
function Save-RenewalConfig {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Config,

        [Parameter()]
        [string]$ConfigPath = "$env:LOCALAPPDATA\PoshACME\renewal_config.json"
    )

    try {
        $Config | ConvertTo-Json | Set-Content -Path $ConfigPath
        return $true
    } catch {
        $msg = "Failed to save renewal configuration to '$ConfigPath': $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
        return $false
    }
}

# Function to get script settings
function Get-ScriptSettings {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\script_settings.json"
    )

    if (Test-Path $SettingsPath) {
        try {
            $settings = Get-Content $SettingsPath -Raw | ConvertFrom-Json

            # Add any missing settings with default values
            $defaultSettings = Get-DefaultSettings
            foreach ($key in $defaultSettings.Keys) {
                if (-not $settings.PSObject.Properties.Name.Contains($key)) {
                    $settings | Add-Member -MemberType NoteProperty -Name $key -Value $defaultSettings[$key]
                }
            }

            return $settings
        } catch {
            Write-Warning "Failed to load settings: $($_.Exception.Message)"
            Write-Log "Failed to load settings: $($_.Exception.Message)" -Level 'Warning'
        }
    }

    # Return default settings
    return Get-DefaultSettings
}

# Function to get default settings
function Get-DefaultSettings {
    [CmdletBinding()]
    param()

    return @{
        # Basic settings
        DefaultDNSPlugin = 'Manual'
        CloudflareToken = $null
        AWSProfile = ''
        AzureSubscriptionId = ''
        AzureTenantId = ''
        LastUsedEmail = ''
        DefaultCertPath = [Environment]::GetFolderPath("Desktop")
        AlwaysExportable = $true
        PreferredInstallLocation = 'ManagementServer'
        DefaultPEMLocation = ''
        DefaultPFXLocation = [Environment]::GetFolderPath("Desktop")

        # Renewal settings
        RenewalThresholdDays = 30
        AutoInstallRenewedCertificates = $false
        RestartServicesAfterRenewal = $false

        # Monitoring settings
        EnableExpirationMonitoring = $false
        ExpirationWarningThresholdDays = 14
        ExpirationCriticalThresholdDays = 7
    }
}

# Function to save script settings
function Save-ScriptSettings {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Settings,

        [Parameter()]
        [string]$SettingsPath = "$env:LOCALAPPDATA\PoshACME\script_settings.json"
    )

    try {
        $Settings | ConvertTo-Json | Set-Content -Path $SettingsPath
        return $true
    } catch {
        Write-Warning "Failed to save settings: $($_)"
        Write-Log "Failed to save settings: $($_)" -Level 'Warning'
        return $false
    }
}
