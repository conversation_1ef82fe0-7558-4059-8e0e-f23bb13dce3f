# Function to securely store credentials
function Set-SecureCredential {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ProviderName,
        
        [Parameter(Mandatory = $true)]
        [pscredential]$Credential
    )
    $credDir = "$env:APPDATA\PoshACME\Creds"
    if (-not (Test-Path $credDir)) {
        New-Item -ItemType Directory -Path $credDir -Force | Out-Null
    }
    $credPath = "$credDir\$ProviderName.cred"
    try {
        $Credential | Export-Clixml -Path $credPath
    } catch {
        $msg = "Failed to save credentials for ${ProviderName} to '$credPath': $($_.Exception.Message)"
        Write-Error $msg
        Write-Log $msg -Level 'Error'
    }
}

# Function to retrieve secure credentials
function Get-SecureCredential {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ProviderName
    )
    $credPath = "$env:APPDATA\PoshACME\Creds\$ProviderName.cred"
    if (Test-Path $credPath) {
        try {
            $cred = Import-Clixml -Path $credPath
            if ($null -eq $cred) { return $null }
            return $cred
        } catch {
            $msg = "Failed to import credentials for ${ProviderName} from '$credPath': $($_.Exception.Message)"
            Write-Error $msg
            Write-Log $msg -Level 'Error'
            return $null
        }
    }
    return $null
}
