# Function to get renewal hooks
function Get-RenewalHooks {
    [CmdletBinding()]
    param (
        [Parameter()]
        [string]$HooksPath = "$env:LOCALAPPDATA\PoshACME\renewal_hooks.json"
    )
    
    if (Test-Path $HooksPath) {
        try {
            $hooks = Get-Content $HooksPath -Raw | ConvertFrom-Json
            return $hooks
        } catch {
            Write-Warning "Failed to load renewal hooks: $($_)"
            Write-Log "Failed to load renewal hooks: $($_)" -Level 'Warning'
        }
    }
    
    # Return default hooks
    return @{
        PreRenewalScripts = @()
        PostRenewalScripts = @()
        EnableHooks = $false
    }
}

# Function to save renewal hooks
function Save-RenewalHooks {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [object]$Hooks,
        
        [Parameter()]
        [string]$HooksPath = "$env:LOCALAPPDATA\PoshACME\renewal_hooks.json"
    )
    
    try {
        $Hooks | ConvertTo-Json | Set-Content -Path $HooksPath
        return $true
    } catch {
        Write-Warning "Failed to save renewal hooks: $($_)"
        Write-Log "Failed to save renewal hooks: $($_)" -Level 'Warning'
        return $false
    }
}

# Function to run pre-renewal hooks
function Invoke-PreRenewalHooks {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain
    )
    
    $hooks = Get-RenewalHooks
    
    if (-not $hooks.EnableHooks -or $hooks.PreRenewalScripts.Count -eq 0) {
        Write-Verbose "No pre-renewal hooks to run"
        return $true
    }
    
    $success = $true
    
    foreach ($script in $hooks.PreRenewalScripts) {
        if (-not (Test-Path $script)) {
            Write-Warning "Pre-renewal script not found: $script"
            Write-Log "Pre-renewal script not found: $script" -Level 'Warning'
            continue
        }
        
        try {
            Write-Host "Running pre-renewal hook: $script" -ForegroundColor Cyan
            Write-Log "Running pre-renewal hook: $script" -Level 'Info'
            
            # Run the script with the domain as a parameter
            $result = & $script -Domain $MainDomain
            
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Pre-renewal hook failed: $script (Exit code: $LASTEXITCODE)"
                Write-Log "Pre-renewal hook failed: $script (Exit code: $LASTEXITCODE)" -Level 'Warning'
                $success = $false
            } else {
                Write-Verbose "Pre-renewal hook completed successfully: $script"
                Write-Log "Pre-renewal hook completed successfully: $script" -Level 'Info'
            }
        } catch {
            Write-Error "Error running pre-renewal hook: $script - $($_)"
            Write-Log "Error running pre-renewal hook: $script - $($_)" -Level 'Error'
            $success = $false
        }
    }
    
    return $success
}

# Function to run post-renewal hooks
function Invoke-PostRenewalHooks {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$MainDomain,
        
        [Parameter()]
        [bool]$RenewalSuccess,
        
        [Parameter()]
        [object]$Certificate
    )
    
    $hooks = Get-RenewalHooks
    
    if (-not $hooks.EnableHooks -or $hooks.PostRenewalScripts.Count -eq 0) {
        Write-Verbose "No post-renewal hooks to run"
        return $true
    }
    
    $success = $true
    
    foreach ($script in $hooks.PostRenewalScripts) {
        if (-not (Test-Path $script)) {
            Write-Warning "Post-renewal script not found: $script"
            Write-Log "Post-renewal script not found: $script" -Level 'Warning'
            continue
        }
        
        try {
            Write-Host "Running post-renewal hook: $script" -ForegroundColor Cyan
            Write-Log "Running post-renewal hook: $script" -Level 'Info'
            
            # Run the script with parameters
            $params = @{
                Domain = $MainDomain
                Success = $RenewalSuccess
            }
            
            if ($Certificate) {
                $params.CertificatePath = $Certificate.CertFile
                $params.KeyPath = $Certificate.KeyFile
                $params.PfxPath = $Certificate.PfxFile
                $params.ExpirationDate = $Certificate.Certificate.NotAfter
            }
            
            $result = & $script @params
            
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Post-renewal hook failed: $script (Exit code: $LASTEXITCODE)"
                Write-Log "Post-renewal hook failed: $script (Exit code: $LASTEXITCODE)" -Level 'Warning'
                $success = $false
            } else {
                Write-Verbose "Post-renewal hook completed successfully: $script"
                Write-Log "Post-renewal hook completed successfully: $script" -Level 'Info'
            }
        } catch {
            Write-Error "Error running post-renewal hook: $script - $($_)"
            Write-Log "Error running post-renewal hook: $script - $($_)" -Level 'Error'
            $success = $false
        }
    }
    
    return $success
}

# Function to configure renewal hooks
function Set-RenewalHooks {
    [CmdletBinding()]
    param()
    
    $hooks = Get-RenewalHooks
    
    Write-Host "`nRenewal Hooks Settings:" -ForegroundColor Cyan
    Write-Host "1) Enable/Disable Hooks: $($hooks.EnableHooks)"
    Write-Host "2) Manage Pre-Renewal Scripts"
    Write-Host "3) Manage Post-Renewal Scripts"
    Write-Host "0) Back"
    
    $choice = Get-ValidatedInput -Prompt "`nEnter your choice (0-3)" -ValidOptions (0..3)
    
    switch ($choice) {
        0 { return }
        1 {
            $enableHooks = Read-Host "`nEnable renewal hooks? (Y/N)"
            $hooks.EnableHooks = $enableHooks -match '^[Yy]$'
            Save-RenewalHooks -Hooks $hooks
            Write-Host "`nRenewal hooks $( if ($hooks.EnableHooks) { 'enabled' } else { 'disabled' } )." -ForegroundColor Green
        }
        2 {
            # Manage pre-renewal scripts
            Write-Host "`nPre-Renewal Scripts:" -ForegroundColor Cyan
            if ($hooks.PreRenewalScripts.Count -eq 0) {
                Write-Host "No pre-renewal scripts configured."
            } else {
                for ($i = 0; $i -lt $hooks.PreRenewalScripts.Count; $i++) {
                    Write-Host "$($i + 1)) $($hooks.PreRenewalScripts[$i])"
                }
            }
            
            Write-Host "`nOptions:"
            Write-Host "A) Add script"
            Write-Host "R) Remove script"
            Write-Host "B) Back"
            
            $subChoice = Read-Host "`nEnter your choice"
            
            switch ($subChoice.ToUpper()) {
                "A" {
                    $scriptPath = Read-Host "`nEnter the full path to the script"
                    if (Test-Path $scriptPath) {
                        $hooks.PreRenewalScripts += $scriptPath
                        Save-RenewalHooks -Hooks $hooks
                        Write-Host "`nScript added successfully." -ForegroundColor Green
                    } else {
                        Write-Warning "Script not found: $scriptPath"
                    }
                }
                "R" {
                    if ($hooks.PreRenewalScripts.Count -eq 0) {
                        Write-Warning "No scripts to remove."
                    } else {
                        $index = Read-Host "`nEnter the number of the script to remove (1-$($hooks.PreRenewalScripts.Count))"
                        if ($index -match '^\d+$' -and [int]$index -ge 1 -and [int]$index -le $hooks.PreRenewalScripts.Count) {
                            $hooks.PreRenewalScripts = $hooks.PreRenewalScripts | Where-Object { $_ -ne $hooks.PreRenewalScripts[[int]$index - 1] }
                            Save-RenewalHooks -Hooks $hooks
                            Write-Host "`nScript removed successfully." -ForegroundColor Green
                        } else {
                            Write-Warning "Invalid selection."
                        }
                    }
                }
                "B" { }
                default { Write-Warning "Invalid choice." }
            }
        }
        3 {
            # Manage post-renewal scripts
            Write-Host "`nPost-Renewal Scripts:" -ForegroundColor Cyan
            if ($hooks.PostRenewalScripts.Count -eq 0) {
                Write-Host "No post-renewal scripts configured."
            } else {
                for ($i = 0; $i -lt $hooks.PostRenewalScripts.Count; $i++) {
                    Write-Host "$($i + 1)) $($hooks.PostRenewalScripts[$i])"
                }
            }
            
            Write-Host "`nOptions:"
            Write-Host "A) Add script"
            Write-Host "R) Remove script"
            Write-Host "B) Back"
            
            $subChoice = Read-Host "`nEnter your choice"
            
            switch ($subChoice.ToUpper()) {
                "A" {
                    $scriptPath = Read-Host "`nEnter the full path to the script"
                    if (Test-Path $scriptPath) {
                        $hooks.PostRenewalScripts += $scriptPath
                        Save-RenewalHooks -Hooks $hooks
                        Write-Host "`nScript added successfully." -ForegroundColor Green
                    } else {
                        Write-Warning "Script not found: $scriptPath"
                    }
                }
                "R" {
                    if ($hooks.PostRenewalScripts.Count -eq 0) {
                        Write-Warning "No scripts to remove."
                    } else {
                        $index = Read-Host "`nEnter the number of the script to remove (1-$($hooks.PostRenewalScripts.Count))"
                        if ($index -match '^\d+$' -and [int]$index -ge 1 -and [int]$index -le $hooks.PostRenewalScripts.Count) {
                            $hooks.PostRenewalScripts = $hooks.PostRenewalScripts | Where-Object { $_ -ne $hooks.PostRenewalScripts[[int]$index - 1] }
                            Save-RenewalHooks -Hooks $hooks
                            Write-Host "`nScript removed successfully." -ForegroundColor Green
                        } else {
                            Write-Warning "Invalid selection."
                        }
                    }
                }
                "B" { }
                default { Write-Warning "Invalid choice." }
            }
        }
    }
    
    Read-Host "`nPress Enter to continue"
    
    # Show the menu again
    Set-RenewalHooks
}
