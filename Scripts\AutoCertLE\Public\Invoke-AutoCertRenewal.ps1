
<#
.SYNOPSIS
    Performs automatic renewal of certificates.

.DESCRIPTION
    Renews all certificates that are nearing expiration.

.PARAMETER RenewAll
    Forces renewal of all certificates regardless of expiration date.

.PARAMETER NonInteractive
    Runs in non-interactive mode without prompting for user input.

.PARAMETER Force
    Forces renewal even if certificates are not close to expiration.

.EXAMPLE
    Invoke-AutoCertRenewal -RenewAll -NonInteractive
#>
function Invoke-AutoCertRenewal {
    [CmdletBinding()]
    param (
        [Parameter()]
        [switch]$RenewAll,

        [Parameter()]
        [switch]$NonInteractive,

        [Parameter()]
        [switch]$Force
    )

    Write-Host "Running in non-interactive renewal mode..."
    Write-Log "Running in non-interactive renewal mode" -Level 'Info'

    # Get all certificates
    $orders = Get-PAOrder
    if (-not $orders) {
        Write-Host "No certificates found to renew." -ForegroundColor Yellow
        Write-Log "No certificates found to renew." -Level 'Warning'
        return
    }

    $renewalCount = 0
    $errorCount = 0

    foreach ($order in $orders) {
        $mainDomain = $order.MainDomain
        Write-Host "Processing certificate for $mainDomain..." -ForegroundColor Cyan

        try {
            # Check if renewal is needed
            $needsRenewal = Test-CertificateRenewalNeeded -MainDomain $mainDomain -Force:$Force
            if (-not $needsRenewal) {
                $cert = Get-CachedPACertificate -MainDomain $mainDomain -Force
                Write-Host "Certificate for $mainDomain is still valid until $($cert.Certificate.NotAfter). Skipping renewal." -ForegroundColor Green
                Write-Log "Certificate for $mainDomain is still valid until $($cert.Certificate.NotAfter). Skipping renewal." -Level 'Info'
                continue
            }

            # Renew the certificate
            Write-Host "Renewing certificate for $mainDomain..." -ForegroundColor Yellow
            Write-Log "Renewing certificate for $mainDomain" -Level 'Info'

            # Run pre-renewal hooks
            $preHooksSuccess = Invoke-PreRenewalHooks -MainDomain $mainDomain
            if (-not $preHooksSuccess) {
                Write-Warning "Pre-renewal hooks failed for $mainDomain. Continuing with renewal anyway."
                Write-Log "Pre-renewal hooks failed for $mainDomain. Continuing with renewal anyway." -Level 'Warning'
            }

            # Submit the renewal
            $newCert = Submit-Renewal -MainDomain $mainDomain -Force:$Force

            if ($newCert) {
                Write-Host "Certificate for $mainDomain renewed successfully." -ForegroundColor Green
                Write-Log "Certificate for $mainDomain renewed successfully." -Level 'Success'
                $renewalCount++

                # Run post-renewal hooks
                $postHooksSuccess = Invoke-PostRenewalHooks -MainDomain $mainDomain -RenewalSuccess $true -Certificate $newCert
                if (-not $postHooksSuccess) {
                    Write-Warning "Post-renewal hooks failed for $mainDomain."
                    Write-Log "Post-renewal hooks failed for $mainDomain." -Level 'Warning'
                }

                # Send success notification
                Send-RenewalNotification -MainDomain $mainDomain -Success $true -ExpirationDate $newCert.Certificate.NotAfter

                # Auto-install the certificate if configured
                $settings = Get-ScriptSettings
                if ($settings.AutoInstallRenewedCertificates) {
                    Write-Host "Auto-installing renewed certificate for $mainDomain..." -ForegroundColor Yellow
                    Write-Log "Auto-installing renewed certificate for $mainDomain" -Level 'Info'

                    try {
                        # Install to the preferred location
                        switch ($settings.PreferredInstallLocation) {
                            'LocalStore' {
                                # Install to local certificate store
                                Install-Certificate -PACertificate $newCert
                            }
                            'RecordingServer' {
                                # Get certificate content
                                $certContent = Get-CertificatePEMContent -Certificate $newCert -IncludeKey
                                if ($certContent.Success) {
                                    # Get the Recording Server certificate folder
                                    $rsCertFolder = Get-RSCertFolder
                                    if ($rsCertFolder) {
                                        # Save PEM files to the Recording Server folder
                                        Save-PEMFiles -directory $rsCertFolder -certContent $certContent.CertContent -keyContent $certContent.KeyContent

                                        # Restart the Recording Server service if configured
                                        if ($settings.RestartServicesAfterRenewal) {
                                            $serviceName = "SalientRecordingServer"
                                            if (Get-Service -Name $serviceName -ErrorAction SilentlyContinue) {
                                                Restart-Service -Name $serviceName -Force
                                                Write-Host "Recording Server service restarted." -ForegroundColor Green
                                                Write-Log "Recording Server service restarted after certificate installation." -Level 'Success'
                                            }
                                        }
                                    }
                                }
                            }
                            'ManagementServer' {
                                # Install to Management Server (IIS)
                                Install-Certificate -PACertificate $newCert

                                # Restart IIS if configured
                                if ($settings.RestartServicesAfterRenewal) {
                                    iisreset /restart
                                    Write-Host "IIS restarted." -ForegroundColor Green
                                    Write-Log "IIS restarted after certificate installation." -Level 'Success'
                                }
                            }
                        }
                    } catch {
                        Write-Error "Failed to auto-install certificate for ${mainDomain}: $($_.Exception.Message)"
                        Write-Log "Failed to auto-install certificate for ${mainDomain}: $($_.Exception.Message)" -Level 'Error'
                    }
                }
            } else {
                Write-Warning "Failed to renew certificate for $mainDomain."
                Write-Log "Failed to renew certificate for $mainDomain." -Level 'Warning'
                $errorCount++

                # Run post-renewal hooks with failure status
                $postHooksSuccess = Invoke-PostRenewalHooks -MainDomain $mainDomain -RenewalSuccess $false
                if (-not $postHooksSuccess) {
                    Write-Warning "Post-renewal hooks failed for $mainDomain."
                    Write-Log "Post-renewal hooks failed for $mainDomain." -Level 'Warning'
                }

                # Send failure notification
                Send-RenewalNotification -MainDomain $mainDomain -Success $false -ErrorMessage "Renewal process failed"
            }
        } catch {
            Write-Error "Error renewing certificate for ${mainDomain}: $($_)"
            Write-Log "Error renewing certificate for ${mainDomain}: $($_)" -Level 'Error'
            $errorCount++
        }
    }

    Write-Host "`nRenewal Summary:" -ForegroundColor Cyan
    Write-Host "Certificates processed: $($orders.Count)" -ForegroundColor White
    Write-Host "Successful renewals: $renewalCount" -ForegroundColor Green
    Write-Host "Failed renewals: $errorCount" -ForegroundColor Red

    Write-Log "Renewal Summary - Processed: $($orders.Count), Successful: $renewalCount, Failed: $errorCount" -Level 'Info'
}

