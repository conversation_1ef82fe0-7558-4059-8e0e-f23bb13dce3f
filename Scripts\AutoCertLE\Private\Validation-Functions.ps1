# Function to validate integer input within a range
function Get-ValidatedInput {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Prompt,
        
        [Parameter(Mandatory = $true)]
        [int[]]$ValidOptions
    )
    do {
        $formattedPrompt = "${Prompt}: "
        $userInput = Read-Host $formattedPrompt
        if ([int]::TryParse($userInput, [ref]$null) -and $ValidOptions -contains [int]$userInput) {
            return [int]$userInput
        } elseif ($userInput -eq '0') {
            return 0
        } else {
            $validChoices = ($ValidOptions | Sort-Object) -join ', '
            Write-Warning "Please enter a valid option ($validChoices) or 0 to go back."
        }
    } while ($true)
}

# Function to validate file paths
function Test-ValidPath {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Path,
        
        [Parameter()]
        [switch]$IsDirectory,
        
        [Parameter()]
        [switch]$MustExist,
        
        [Parameter()]
        [switch]$MustNotExist,
        
        [Parameter()]
        [switch]$RequireWrite
    )

    try {
        # Check if path is null or empty
        if ([string]::IsNullOrWhiteSpace($Path)) {
            Write-Warning "Path cannot be empty."
            return $false
        }

        # Check for invalid characters
        $invalidChars = [System.IO.Path]::GetInvalidPathChars()
        if ($Path.IndexOfAny($invalidChars) -ge 0) {
            Write-Warning "Path contains invalid characters."
            return $false
        }

        # Check if the path exists
        if ($MustExist -and -not (Test-Path $Path)) {
            Write-Warning "Path does not exist: $Path"
            return $false
        }

        # Check if the path must not exist
        if ($MustNotExist -and (Test-Path $Path)) {
            Write-Warning "Path already exists: $Path"
            return $false
        }

        # Check if the path is a directory
        if ($IsDirectory -and -not (Test-Path $Path -PathType Container)) {
            Write-Warning "Path is not a directory: $Path"
            return $false
        }

        # Check if the path is a file
        if (-not $IsDirectory -and -not (Test-Path $Path -PathType Leaf)) {
            Write-Warning "Path is not a file: $Path"
            return $false
        }

        # Check if the path is writable
        if ($RequireWrite) {
            $testFile = [System.IO.Path]::Combine($Path, [System.IO.Path]::GetRandomFileName())
            try {
                [System.IO.File]::Create($testFile).Dispose()
                [System.IO.File]::Delete($testFile)
            } catch {
                Write-Warning "Path is not writable: $Path"
                return $false
            }
        }

        return $true
    } catch {
        Write-Warning "An error occurred while validating the path: $($_)"
        return $false
    }
}

# Function to validate email addresses
function Test-ValidEmail {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Email
    )

    if ([string]::IsNullOrWhiteSpace($Email)) {
        Write-Warning "Email address cannot be empty."
        return $false
    }

    if ($Email -notmatch '^[\w\.-]+@[\w\.-]+\.\w+$') {
        Write-Warning "Invalid email address format: $Email"
        return $false
    }

    return $true
}

# Function to validate domain names
function Test-ValidDomain {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Domain
    )

    if ([string]::IsNullOrWhiteSpace($Domain)) {
        Write-Warning "Domain name cannot be empty."
        return $false
    }

    if ($Domain -notmatch '^[a-zA-Z0-9.-]+$') {
        Write-Warning "Invalid domain name format: $Domain"
        return $false
    }

    return $true
}

# Function to validate plugin parameters
function Test-PluginParameters {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Plugin,
        
        [Parameter(Mandatory = $true)]
        [hashtable]$Parameters
    )

    $validationRules = @{
        'Cloudflare' = @{
            'CFToken' = '^[a-zA-Z0-9_-]{40,}$'
        }
        'Route53' = @{
            'ProfileName' = '^[a-zA-Z0-9_-]+$'
            'AccessKey' = '^[A-Z0-9]{20}$'
            'SecretKey' = '^[a-zA-Z0-9/+]{40}$'
        }
        'Azure' = @{
            'SubscriptionId' = '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$'
            'TenantId' = '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$'
        }
    }

    if (-not $validationRules.ContainsKey($Plugin)) {
        Write-Debug "No validation rules defined for plugin: $Plugin"
        return $true
    }

    $rules = $validationRules[$Plugin]
    $isValid = $true

    foreach ($param in $Parameters.GetEnumerator()) {
        if ($rules.ContainsKey($param.Key)) {
            if ($param.Value -notmatch $rules[$param.Key]) {
                Write-Error "Invalid format for $($param.Key) in $Plugin plugin"
                $isValid = $false
            }
        }
    }

    return $isValid
}
