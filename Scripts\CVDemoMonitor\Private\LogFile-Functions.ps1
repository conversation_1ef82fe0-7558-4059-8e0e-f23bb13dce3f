# LogFile-Functions.ps1
# Contains functions related to log file analysis

function Search-LogFiles {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [hashtable]$ServiceConfigs,

        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl,

        [Parameter(Mandatory = $false)]
        [bool]$NoSlack = $false,

        [Parameter(Mandatory = $false)]
        [int]$LookbackMinutes = 60
    )

    Write-Log "Analyzing log files for critical errors" -Level 'INFO'

    # Get the logs folder path from the global config if available
    $logsFolder = $null

    if ($script:config -and $script:config.InstallPaths -and $script:config.InstallPaths.LogsFolder) {
        $logsFolder = $script:config.InstallPaths.LogsFolder
        Write-Log "Using logs folder from config: $logsFolder" -Level 'DEBUG'
    }
    else {
        # Try to find the path using our path resolution function
        $installPaths = Find-InstallPath
        if ($installPaths -and $installPaths.LogsFolder) {
            $logsFolder = $installPaths.LogsFolder
            Write-Log "Found logs folder: $logsFolder" -Level 'DEBUG'
        }
        else {
            # Fall back to default path
            $logsFolder = "C:\ProgramData\Salient Security Platform\Logs"
            Write-Log "Using default logs folder: $logsFolder" -Level 'WARNING'
        }
    }

    $logPaths = @{
        "RecordingServer" = Join-Path -Path $logsFolder -ChildPath "RecordingServer*.log"
        "AdminService" = Join-Path -Path $logsFolder -ChildPath "AdminService*.log"
        "ManagementServer" = Join-Path -Path $logsFolder -ChildPath "ManagementServer*.log"
    }

    $errorPatterns = @(
        "CRITICAL",
        "FATAL",
        "Exception",
        "Error",
        "Failed to",
        "Failure",
        "Unable to"
    )

    $startTime = (Get-Date).AddMinutes(-$LookbackMinutes)

    foreach ($serviceName in $ServiceConfigs.Keys) {
        $config = $ServiceConfigs[$serviceName]
        $friendlyName = $config.FriendlyName

        if ($logPaths.ContainsKey($serviceName)) {
            $logPath = $logPaths[$serviceName]

            try {
                $logFiles = Get-ChildItem -Path $logPath -ErrorAction SilentlyContinue

                if ($logFiles.Count -eq 0) {
                    Write-Log "No log files found for $friendlyName at path: $logPath" -Level 'WARNING'
                    continue
                }

                $criticalErrors = @()

                foreach ($logFile in $logFiles) {
                    # Only check files modified since our lookback time
                    if ($logFile.LastWriteTime -ge $startTime) {
                        $content = Get-Content -Path $logFile.FullName -ErrorAction SilentlyContinue

                        foreach ($line in $content) {
                            # Try to extract timestamp from the line
                            $hasTimestamp = $line -match '\[([\d\-]+\s[\d:]+)'
                            $timestamp = if ($hasTimestamp) { [datetime]::Parse($matches[1]) } else { $null }

                            # Skip if we have a timestamp and it's before our lookback period
                            if ($timestamp -and $timestamp -lt $startTime) {
                                continue
                            }

                            # Check for error patterns
                            foreach ($pattern in $errorPatterns) {
                                if ($line -match $pattern) {
                                    $criticalErrors += [PSCustomObject]@{
                                        Service = $friendlyName
                                        LogFile = $logFile.Name
                                        Timestamp = if ($timestamp) { $timestamp.ToString("yyyy-MM-dd HH:mm:ss") } else { "Unknown" }
                                        Message = $line.Trim()
                                    }
                                    break  # Once we've matched a pattern, no need to check others
                                }
                            }
                        }
                    }
                }

                # Report critical errors
                if ($criticalErrors.Count -gt 0) {
                    Write-Log "Found $($criticalErrors.Count) critical errors in $friendlyName logs" -Level 'WARNING'

                    # Group by log file to avoid too many notifications
                    $groupedErrors = $criticalErrors | Group-Object -Property LogFile

                    foreach ($group in $groupedErrors) {
                        $logFileName = $group.Name
                        $errorCount = $group.Count
                        $sampleErrors = $group.Group | Select-Object -First 3

                        $errorSamples = ($sampleErrors | ForEach-Object { "- [$($_.Timestamp)] $($_.Message)" }) -join "`n"

                        # Send notification if Slack is enabled
                        if (-not $NoSlack -and $WebhookUrl) {
                            $messageText = ":warning: *Critical Errors in Logs*

*Service:* $friendlyName
*Host:* $env:COMPUTERNAME
*Log File:* $logFileName
*Error Count:* $errorCount
*Time:* $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

*Sample Errors:*
$errorSamples"

                            if ($errorCount -gt 3) {
                                $messageText += "`n_...and $($errorCount - 3) more errors_"
                            }

                            try {
                                Send-SlackMessage -WebhookUrl $WebhookUrl -MessageText $messageText
                                Write-Log "Sent log error notification for $friendlyName ($logFileName)" -Level 'INFO'
                            }
                            catch {
                                $errorMsg = $_.Exception.Message
                                Write-Log "Failed to send log error notification for ${friendlyName} ($logFileName): $errorMsg" -Level 'ERROR'
                            }
                        }
                    }
                }
                else {
                    Write-Log "No critical errors found in $friendlyName logs" -Level 'INFO'
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Log "Error analyzing log files for ${friendlyName}: $errorMsg" -Level 'ERROR'
            }
        }
        else {
            Write-Log "No log path defined for $friendlyName" -Level 'DEBUG'
        }
    }
}
