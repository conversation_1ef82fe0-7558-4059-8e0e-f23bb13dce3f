<#
.SYNOPSIS
    Installs a certificate to the specified location.

.DESCRIPTION
    Handles the installation of a certificate to various locations, including
    the local certificate store, PEM files, and PFX files.

.PARAMETER PACertificate
    The certificate object from Posh-ACME.

.PARAMETER MainDomain
    The main domain for the certificate.

.EXAMPLE
    Install-Certificate -PACertificate $cert

.EXAMPLE
    Install-Certificate -MainDomain "example.com"
#>
function Install-Certificate {
    [CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='Medium')]
    param (
        [Parameter(ParameterSetName='Certificate', Mandatory=$true)]
        [object]$PACertificate,

        [Parameter(ParameterSetName='Domain', Mandatory=$true)]
        [string]$MainDomain
    )

    # Ensure the ACME server is set
    Initialize-ACMEServer

    # Get the certificate if not provided
    if ($PSCmdlet.ParameterSetName -eq 'Domain') {
        try {
            $PACertificate = Get-CachedPACertificate -MainDomain $MainDomain
            if (-not $PACertificate) {
                Write-Error "Certificate for $MainDomain not found."
                Write-Log "Certificate for $MainDomain not found." -Level 'Error'
                return
            }
        } catch {
            Write-Error "Failed to retrieve certificate for ${MainDomain}: $($_)"
            Write-Log "Failed to retrieve certificate for ${MainDomain}: $($_)" -Level 'Error'
            return
        }
    }

    # Get the main domain from the certificate if not specified
    if (-not $MainDomain) {
        $MainDomain = $PACertificate.MainDomain
    }

    # Load settings
    $Settings = Get-ScriptSettings

    # Display certificate information
    Write-Host "`nCertificate Information:" -ForegroundColor Cyan
    Write-Host "Domain: $MainDomain"
    Write-Host "Issuer: $($PACertificate.Certificate.Issuer)"
    Write-Host "Valid Until: $($PACertificate.Certificate.NotAfter)"
    Write-Host "Subject Alternative Names: $($PACertificate.Certificate.DnsNameList -join ', ')"

    # Get available installation targets
    $targets = Get-InstallationTargets
    $availableTargets = $targets | Where-Object { $_.IsAvailable }

    # Check if OpenSSL is available
    $opensslAvailable = Test-OpenSSLAvailable
    if (-not $opensslAvailable) {
        Write-Warning "OpenSSL is not available. Some installation options may not work correctly."
        Write-Log "OpenSSL is not available. Some installation options may not work correctly." -Level 'Warning'
    }

    # Validate certificate chain
    $chainValidation = Test-CertificateChain -Certificate $PACertificate
    if (-not $chainValidation.IsValid) {
        Write-Warning "Certificate chain validation failed. The certificate may not be trusted by clients."
        foreach ($errorMsg in $chainValidation.Errors) {
            Write-Warning "  - $errorMsg"
        }
        Write-Log "Certificate chain validation failed: $($chainValidation.Errors -join '; ')" -Level 'Warning'
    }

    # Installation options
    Write-Host "`nInstallation Options:" -ForegroundColor Cyan
    $i = 1
    foreach ($target in $availableTargets) {
        $adminNote = if ($target.RequiresAdmin) { " (requires admin)" } else { "" }
        Write-Host "$i) $($target.Name)$adminNote"
        $i++
    }
    Write-Host "0) Back"

    $installChoice = Get-ValidatedInput -Prompt "`nEnter your choice (0-$($availableTargets.Count))" -ValidOptions (1..$availableTargets.Count)

    if ($installChoice -eq 0) {
        return
    }

    # Get the selected target
    $selectedTarget = $availableTargets[$installChoice - 1]
    Write-Verbose "Selected installation target: $($selectedTarget.Name)"

    # Check if admin rights are required
    if ($selectedTarget.RequiresAdmin) {
        $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
        if (-not $isAdmin) {
            Write-Warning "This installation option requires administrator privileges. Please run PowerShell as Administrator and try again."
            Write-Log "Installation to $($selectedTarget.Name) failed: Administrator privileges required" -Level 'Warning'
            Read-Host "`nPress Enter to return to the main menu"
            return
        }
    }

    switch ($selectedTarget.Id) {
        "LocalStore" {
            # Install to local certificate store
            try {
                $certPath = $PACertificate.PfxFile
                if (-not $certPath) {
                    # Create a temporary PFX file if one doesn't exist
                    $tempFolder = [System.IO.Path]::GetTempPath()
                    $tempPfxPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.pfx"

                    # Get the certificate content
                    $certContent = Get-CertificatePEMContent -Certificate $PACertificate -IncludeKey
                    if (-not $certContent.Success) {
                        throw "Failed to get certificate content: $($certContent.ErrorMessage)"
                    }

                    # Create a temporary PFX file
                    # Use OpenSSL to create the PFX file
                    $opensslPath = "openssl"
                    $tempCertPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.crt"
                    $tempKeyPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.key"

                    try {
                        Set-Content -Path $tempCertPath -Value $certContent.CertContent -Encoding ASCII
                        Set-Content -Path $tempKeyPath -Value $certContent.KeyContent -Encoding ASCII

                        $opensslArgs = @(
                            "pkcs12", "-export",
                            "-out", $tempPfxPath,
                            "-inkey", $tempKeyPath,
                            "-in", $tempCertPath,
                            "-passout", "pass:"
                        )

                        $process = Start-Process -FilePath $opensslPath -ArgumentList $opensslArgs -NoNewWindow -Wait -PassThru
                        if ($process.ExitCode -ne 0) {
                            throw "OpenSSL failed to create PFX file with exit code $($process.ExitCode)"
                        }

                        $certPath = $tempPfxPath
                    } finally {
                        # Clean up temporary files
                        if (Test-Path $tempCertPath) { Remove-Item -Path $tempCertPath -Force }
                        if (Test-Path $tempKeyPath) { Remove-Item -Path $tempKeyPath -Force }
                    }
                }

                # Import the certificate to the local store
                $pfxBytes = [System.IO.File]::ReadAllBytes($certPath)
                $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
                $cert.Import($pfxBytes, $null, [System.Security.Cryptography.X509Certificates.X509KeyStorageFlags]::Exportable)

                # Add to the Personal store
                $store = New-Object System.Security.Cryptography.X509Certificates.X509Store("My", "LocalMachine")
                $store.Open([System.Security.Cryptography.X509Certificates.OpenFlags]::ReadWrite)
                $store.Add($cert)
                $store.Close()

                Write-Host "`nCertificate installed to the local machine's Personal certificate store." -ForegroundColor Green
                Write-Log "Certificate for $MainDomain installed to the local machine's Personal certificate store." -Level 'Success'

                # Clean up temporary PFX file if we created one
                if ($tempPfxPath -and (Test-Path $tempPfxPath)) {
                    Remove-Item -Path $tempPfxPath -Force
                }
            } catch {
                Write-Error "Failed to install certificate to local store: $($_)"
                Write-Log "Failed to install certificate to local store: $($_)" -Level 'Error'
            }
        }
        "PEM" {
            # Export as PEM files
            try {
                # Get default save location from settings or prompt user
                $defaultPath = $Settings.DefaultPEMLocation
                if (-not $defaultPath -or -not (Test-Path $defaultPath -PathType Container)) {
                    $defaultPath = [Environment]::GetFolderPath("Desktop")
                }

                $savePath = Read-Host "`nEnter the folder path to save PEM files (default: $defaultPath)"
                if ([string]::IsNullOrWhiteSpace($savePath)) {
                    $savePath = $defaultPath
                }

                # Validate the path
                if (-not (Test-ValidPath -Path $savePath -IsDirectory -MustExist -RequireWrite)) {
                    Write-Error "Invalid save location: $savePath"
                    return
                }

                # Get certificate content
                $certContent = Get-CertificatePEMContent -Certificate $PACertificate -IncludeKey
                if (-not $certContent.Success) {
                    throw "Failed to get certificate content: $($certContent.ErrorMessage)"
                }

                # Save the files
                $certFile = Join-Path -Path $savePath -ChildPath "$MainDomain.crt"
                $keyFile = Join-Path -Path $savePath -ChildPath "$MainDomain.key"
                $chainFile = Join-Path -Path $savePath -ChildPath "$MainDomain-chain.crt"

                Set-Content -Path $certFile -Value $certContent.CertContent -Encoding ASCII
                Set-Content -Path $keyFile -Value $certContent.KeyContent -Encoding ASCII

                # Save the certificate chain if available
                if ($PACertificate.ChainFile) {
                    $chainContent = Get-Content -Path $PACertificate.ChainFile -Raw
                    Set-Content -Path $chainFile -Value $chainContent -Encoding ASCII
                }

                Write-Host "`nPEM files saved to:" -ForegroundColor Green
                Write-Host "Certificate: $certFile"
                Write-Host "Private Key: $keyFile"
                if (Test-Path $chainFile) {
                    Write-Host "Chain: $chainFile"
                }

                Write-Log "PEM files for $MainDomain exported to $savePath" -Level 'Success'

                # Update settings with the new path
                $Settings.DefaultPEMLocation = $savePath
                Save-ScriptSettings -Settings $Settings
            } catch {
                Write-Error "Failed to export PEM files: $($_)"
                Write-Log "Failed to export PEM files: $($_)" -Level 'Error'
            }
        }
        "PFX" {
            # Export as PFX file
            try {
                # Get default save location from settings or prompt user
                $defaultPath = $Settings.DefaultPFXLocation
                if (-not $defaultPath -or -not (Test-Path $defaultPath -PathType Container)) {
                    $defaultPath = [Environment]::GetFolderPath("Desktop")
                }

                $savePath = Read-Host "`nEnter the folder path to save PFX file (default: $defaultPath)"
                if ([string]::IsNullOrWhiteSpace($savePath)) {
                    $savePath = $defaultPath
                }

                # Validate the path
                if (-not (Test-ValidPath -Path $savePath -IsDirectory -MustExist -RequireWrite)) {
                    Write-Error "Invalid save location: $savePath"
                    return
                }

                # Prompt for password
                $securePassword = Read-Host -AsSecureString "`nEnter a password for the PFX file (leave blank for no password)"

                # If PFX file already exists, use it
                if ($PACertificate.PfxFile -and (Test-Path $PACertificate.PfxFile)) {
                    $pfxPath = Join-Path -Path $savePath -ChildPath "$MainDomain.pfx"
                    Copy-Item -Path $PACertificate.PfxFile -Destination $pfxPath -Force
                } else {
                    # Create a new PFX file
                    $certContent = Get-CertificatePEMContent -Certificate $PACertificate -IncludeKey
                    if (-not $certContent.Success) {
                        throw "Failed to get certificate content: $($certContent.ErrorMessage)"
                    }

                    # Use OpenSSL to create the PFX file
                    $opensslPath = "openssl"
                    $tempFolder = [System.IO.Path]::GetTempPath()
                    $tempCertPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.crt"
                    $tempKeyPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.key"
                    $pfxPath = Join-Path -Path $savePath -ChildPath "$MainDomain.pfx"

                    try {
                        Set-Content -Path $tempCertPath -Value $certContent.CertContent -Encoding ASCII
                        Set-Content -Path $tempKeyPath -Value $certContent.KeyContent -Encoding ASCII

                        # Convert secure string to plain text for OpenSSL
                        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($securePassword)
                        $plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
                        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)

                        if ([string]::IsNullOrEmpty($plainPassword)) {
                            $opensslArgs = @(
                                "pkcs12", "-export",
                                "-out", $pfxPath,
                                "-inkey", $tempKeyPath,
                                "-in", $tempCertPath,
                                "-passout", "pass:"
                            )
                        } else {
                            $opensslArgs = @(
                                "pkcs12", "-export",
                                "-out", $pfxPath,
                                "-inkey", $tempKeyPath,
                                "-in", $tempCertPath,
                                "-passout", "pass:$plainPassword"
                            )
                        }

                        $process = Start-Process -FilePath $opensslPath -ArgumentList $opensslArgs -NoNewWindow -Wait -PassThru
                        if ($process.ExitCode -ne 0) {
                            throw "OpenSSL failed to create PFX file with exit code $($process.ExitCode)"
                        }
                    } finally {
                        # Clean up temporary files
                        if (Test-Path $tempCertPath) { Remove-Item -Path $tempCertPath -Force }
                        if (Test-Path $tempKeyPath) { Remove-Item -Path $tempKeyPath -Force }
                    }
                }

                Write-Host "`nPFX file saved to: $pfxPath" -ForegroundColor Green
                Write-Log "PFX file for $MainDomain exported to $pfxPath" -Level 'Success'

                # Update settings with the new path
                $Settings.DefaultPFXLocation = $savePath
                Save-ScriptSettings -Settings $Settings
            } catch {
                Write-Error "Failed to export PFX file: $($_)"
                Write-Log "Failed to export PFX file: $($_)" -Level 'Error'
            }
        }
        "RecordingServer" {
            # Install to CompleteView Recording Server
            try {
                # Get certificate content
                $certContent = Get-CertificatePEMContent -Certificate $PACertificate -IncludeKey
                if (-not $certContent.Success) {
                    throw "Failed to get certificate content: $($certContent.ErrorMessage)"
                }

                # Get the Recording Server certificate folder
                $rsCertFolder = Get-RSCertFolder
                if (-not $rsCertFolder) {
                    throw "CompleteView Recording Server certificate folder not found."
                }

                # Save PEM files to the Recording Server folder
                $pemFiles = Save-PEMFiles -directory $rsCertFolder -certContent $certContent.CertContent -keyContent $certContent.KeyContent
                if (-not $pemFiles) {
                    throw "Failed to save PEM files to Recording Server folder."
                }

                Write-Host "`nCertificate installed to CompleteView Recording Server:" -ForegroundColor Green
                Write-Host "Certificate: $($pemFiles.CertFile)"
                Write-Host "Private Key: $($pemFiles.KeyFile)"

                Write-Log "Certificate for $MainDomain installed to CompleteView Recording Server at $rsCertFolder" -Level 'Success'

                # Prompt to restart the Recording Server service
                $restartService = Read-Host "`nDo you want to restart the Recording Server service to apply the new certificate? (Y/N)"
                if ($restartService -match '^[Yy]$') {
                    $serviceName = "SalientRecordingServer"
                    if (Get-Service -Name $serviceName -ErrorAction SilentlyContinue) {
                        Restart-Service -Name $serviceName -Force
                        Write-Host "`nRecording Server service restarted." -ForegroundColor Green
                        Write-Log "Recording Server service restarted after certificate installation." -Level 'Success'
                    } else {
                        Write-Warning "Recording Server service not found. Please restart it manually."
                        Write-Log "Recording Server service not found for restart." -Level 'Warning'
                    }
                }
            } catch {
                Write-Error "Failed to install certificate to Recording Server: $($_)"
                Write-Log "Failed to install certificate to Recording Server: $($_)" -Level 'Error'
            }
        }
        "ManagementServer" {
            # Install to CompleteView Management Server
            try {
                # Get certificate content
                $certContent = Get-CertificatePEMContent -Certificate $PACertificate -IncludeKey
                if (-not $certContent.Success) {
                    throw "Failed to get certificate content: $($certContent.ErrorMessage)"
                }

                # Create a temporary PFX file
                $tempFolder = [System.IO.Path]::GetTempPath()
                $tempCertPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.crt"
                $tempKeyPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.key"
                $tempPfxPath = Join-Path -Path $tempFolder -ChildPath "$MainDomain.pfx"

                try {
                    Set-Content -Path $tempCertPath -Value $certContent.CertContent -Encoding ASCII
                    Set-Content -Path $tempKeyPath -Value $certContent.KeyContent -Encoding ASCII

                    # Use OpenSSL to create the PFX file
                    $opensslPath = "openssl"
                    $opensslArgs = @(
                        "pkcs12", "-export",
                        "-out", $tempPfxPath,
                        "-inkey", $tempKeyPath,
                        "-in", $tempCertPath,
                        "-passout", "pass:"
                    )

                    $process = Start-Process -FilePath $opensslPath -ArgumentList $opensslArgs -NoNewWindow -Wait -PassThru
                    if ($process.ExitCode -ne 0) {
                        throw "OpenSSL failed to create PFX file with exit code $($process.ExitCode)"
                    }

                    # Import the certificate to the local store
                    $pfxBytes = [System.IO.File]::ReadAllBytes($tempPfxPath)
                    $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
                    $cert.Import($pfxBytes, $null, [System.Security.Cryptography.X509Certificates.X509KeyStorageFlags]::Exportable)

                    # Add to the Personal store
                    $store = New-Object System.Security.Cryptography.X509Certificates.X509Store("My", "LocalMachine")
                    $store.Open([System.Security.Cryptography.X509Certificates.OpenFlags]::ReadWrite)
                    $store.Add($cert)
                    $store.Close()

                    # Get the certificate thumbprint
                    $thumbprint = $cert.Thumbprint

                    # Configure IIS to use the certificate
                    $iisModule = Get-Module -ListAvailable -Name WebAdministration
                    if (-not $iisModule) {
                        Import-Module WebAdministration -ErrorAction Stop
                    }

                    # Check if IIS is installed
                    if (-not (Get-Module -Name WebAdministration)) {
                        throw "IIS WebAdministration module not available. Please ensure IIS is installed."
                    }

                    # Get the default website
                    $siteName = "Default Web Site"
                    $site = Get-Website -Name $siteName -ErrorAction SilentlyContinue
                    if (-not $site) {
                        throw "Default Web Site not found in IIS."
                    }

                    # Bind the certificate to the default website
                    $binding = Get-WebBinding -Name $siteName -Protocol "https" -ErrorAction SilentlyContinue
                    if ($binding) {
                        # Update existing binding
                        $binding.AddSslCertificate($thumbprint, "My")
                    } else {
                        # Create new binding
                        New-WebBinding -Name $siteName -IP "*" -Port 443 -Protocol https
                        $binding = Get-WebBinding -Name $siteName -Protocol "https"
                        $binding.AddSslCertificate($thumbprint, "My")
                    }

                    Write-Host "`nCertificate installed to CompleteView Management Server:" -ForegroundColor Green
                    Write-Host "Certificate thumbprint: $thumbprint"
                    Write-Host "Bound to: $siteName"

                    Write-Log "Certificate for $MainDomain installed to CompleteView Management Server with thumbprint $thumbprint" -Level 'Success'

                    # Prompt to restart IIS
                    $restartIIS = Read-Host "`nDo you want to restart IIS to apply the new certificate? (Y/N)"
                    if ($restartIIS -match '^[Yy]$') {
                        iisreset /restart
                        Write-Host "`nIIS restarted." -ForegroundColor Green
                        Write-Log "IIS restarted after certificate installation." -Level 'Success'
                    }
                } finally {
                    # Clean up temporary files
                    if (Test-Path $tempCertPath) { Remove-Item -Path $tempCertPath -Force }
                    if (Test-Path $tempKeyPath) { Remove-Item -Path $tempKeyPath -Force }
                    if (Test-Path $tempPfxPath) { Remove-Item -Path $tempPfxPath -Force }
                }
            } catch {
                Write-Error "Failed to install certificate to Management Server: $($_)"
                Write-Log "Failed to install certificate to Management Server: $($_)" -Level 'Error'
            }
        }
        "Nginx" {
            # Install to Nginx web server
            Install-CertificateToNginx -Certificate $PACertificate -RestartService:$true
        }
        "Apache" {
            # Install to Apache web server
            Install-CertificateToApache -Certificate $PACertificate -RestartService:$true
        }
    }

    Read-Host "`nPress Enter to return to the main menu"
}
