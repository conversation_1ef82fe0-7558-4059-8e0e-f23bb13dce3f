function ConvertFrom-ExcelData {
    [alias("Use-ExcelData")]
    param(
        [<PERSON><PERSON>("FullName")]
        [Parameter(ValueFromPipelineByPropertyName = $true, ValueFromPipeline = $true, Mandatory = $true)]
        [ValidateScript( { Test-Path $_ -PathType Leaf })]
        $Path,
        [ScriptBlock]$ScriptBlock,
        [<PERSON><PERSON>("Sheet")]
        $WorksheetName = 1,
		[<PERSON><PERSON>('HeaderRow', 'TopRow')]
        [int]$StartRow = 1,
        [string[]]$Header,
        [switch]$NoHeader,
        [switch]$DataOnly
    )

    $null = $PSBoundParameters.Remove('ScriptBlock')
    $params = @{} + $PSBoundParameters

    $data = Import-Excel @params

    $PropertyNames = $data[0].psobject.Properties |
        Where-Object {$_.membertype -match 'property'} |
        Select-Object -ExpandProperty name

    foreach ($record in $data) {
        & $ScriptBlock $PropertyNames $record
    }
}