<#
.SYNOPSIS
    Installs a certificate to Salient CompleteView servers.

.DESCRIPTION
    Installs a Let's Encrypt certificate to Salient CompleteView Management Servers and/or Recording Servers.
    Management Servers use the Windows certificate store, while Recording Servers use PEM files with specific naming conventions.

.PARAMETER Certificate
    The certificate object to install. If not provided, you will be prompted to select from existing certificates.

.PARAMETER ServerType
    The type of server to install to: ManagementServer, RecordingServer, or Both. If not specified, the function will attempt to detect the server type.

.PARAMETER ComputerName
    The name of the remote server to install to. If not provided, installs to the local machine.

.PARAMETER CreateFolders
    Creates the certificate folders if they don't exist.

.PARAMETER Password
    The password to use for PFX files when installing to Management Servers.

.PARAMETER InstallToLocalMachine
    Installs to the LocalMachine certificate store instead of CurrentUser when installing to a Management Server.

.EXAMPLE
    Install-SalientCertificate

.EXAMPLE
    Install-SalientCertificate -ServerType RecordingServer -ComputerName "RS01"

.EXAMPLE
    Install-SalientCertificate -Certificate $cert -ServerType Both -CreateFolders
#>
function Install-SalientCertificate {
    [CmdletBinding()]
    param (
        [Parameter()]
        [object]$Certificate,
        
        [Parameter()]
        [ValidateSet("ManagementServer", "RecordingServer", "Both", "Auto")]
        [string]$ServerType = "Auto",
        
        [Parameter()]
        [string]$ComputerName = $env:COMPUTERNAME,
        
        [Parameter()]
        [switch]$CreateFolders,
        
        [Parameter()]
        [securestring]$Password,
        
        [Parameter()]
        [switch]$InstallToLocalMachine
    )
    
    try {
        # If no certificate is provided, prompt to select one
        if (-not $Certificate) {
            $certificates = Get-ExistingCertificates
            
            if ($certificates.Count -eq 0) {
                Write-Warning "No certificates found. Please register a certificate first."
                return $false
            }
            
            Write-Host "`nSelect a certificate to install:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $certificates.Count; $i++) {
                Write-Host "$($i + 1)) $($certificates[$i].MainDomain) (expires: $($certificates[$i].Certificate.NotAfter))"
            }
            
            $certChoice = Get-ValidatedInput -Prompt "`nEnter your choice (1-$($certificates.Count))" -ValidOptions (1..$certificates.Count)
            $Certificate = $certificates[$certChoice - 1]
        }
        
        # Detect server type if set to Auto
        if ($ServerType -eq "Auto") {
            $serverInfo = Get-SalientServerType -ComputerName $ComputerName
            
            if (-not $serverInfo) {
                Write-Warning "Could not detect server type. Please specify the server type manually."
                
                Write-Host "`nSelect server type:" -ForegroundColor Cyan
                Write-Host "1) Management Server"
                Write-Host "2) Recording Server"
                Write-Host "3) Both"
                
                $typeChoice = Get-ValidatedInput -Prompt "`nEnter your choice (1-3)" -ValidOptions (1..3)
                
                switch ($typeChoice) {
                    1 { $ServerType = "ManagementServer" }
                    2 { $ServerType = "RecordingServer" }
                    3 { $ServerType = "Both" }
                }
            } else {
                if ($serverInfo.IsManagementServer -and $serverInfo.IsRecordingServer) {
                    $ServerType = "Both"
                } elseif ($serverInfo.IsManagementServer) {
                    $ServerType = "ManagementServer"
                } elseif ($serverInfo.IsRecordingServer) {
                    $ServerType = "RecordingServer"
                } else {
                    Write-Warning "No Salient CompleteView services detected on $ComputerName."
                    
                    Write-Host "`nSelect server type:" -ForegroundColor Cyan
                    Write-Host "1) Management Server"
                    Write-Host "2) Recording Server"
                    Write-Host "3) Both"
                    Write-Host "0) Cancel"
                    
                    $typeChoice = Get-ValidatedInput -Prompt "`nEnter your choice (0-3)" -ValidOptions (0..3)
                    
                    switch ($typeChoice) {
                        0 { return $false }
                        1 { $ServerType = "ManagementServer" }
                        2 { $ServerType = "RecordingServer" }
                        3 { $ServerType = "Both" }
                    }
                }
            }
        }
        
        # Install based on server type
        $results = @{
            Success = $true
            ManagementServer = $null
            RecordingServer = $null
        }
        
        if ($ServerType -eq "ManagementServer" -or $ServerType -eq "Both") {
            Write-Host "`nInstalling certificate to Management Server..." -ForegroundColor Cyan
            
            # If no password is provided and it's a remote server, prompt for one
            if (-not $Password -and $ComputerName -ne $env:COMPUTERNAME) {
                $Password = Read-Host -AsSecureString "Enter password for PFX file (leave blank for no password)"
            }
            
            $mgmtResult = Install-CertificateToManagementServer -Certificate $Certificate -ComputerName $ComputerName -Password $Password -InstallToLocalMachine:$InstallToLocalMachine
            $results.ManagementServer = $mgmtResult
            
            if (-not $mgmtResult.Success) {
                $results.Success = $false
                Write-Warning "Failed to install certificate to Management Server: $($mgmtResult.ErrorMessage)"
            }
        }
        
        if ($ServerType -eq "RecordingServer" -or $ServerType -eq "Both") {
            Write-Host "`nInstalling certificate to Recording Server..." -ForegroundColor Cyan
            
            $recResult = Install-CertificateToRecordingServer -Certificate $Certificate -ComputerName $ComputerName -CreateFolder:$CreateFolders
            $results.RecordingServer = $recResult
            
            if (-not $recResult.Success) {
                $results.Success = $false
                Write-Warning "Failed to install certificate to Recording Server: $($recResult.ErrorMessage)"
            }
        }
        
        if ($results.Success) {
            Write-Host "`nCertificate installation completed successfully." -ForegroundColor Green
            Write-Host "`nIMPORTANT: You must manually select this certificate in the CompleteView Management Console." -ForegroundColor Yellow
            Write-Host "Do NOT restart services as this may cause recording interruptions." -ForegroundColor Yellow
        } else {
            Write-Warning "`nCertificate installation completed with errors. See above for details."
        }
        
        return $results
    } catch {
        Write-Error "Failed to install certificate: $($_.Exception.Message)"
        Write-Log "Failed to install certificate: $($_.Exception.Message)" -Level 'Error'
        return $false
    }
}
