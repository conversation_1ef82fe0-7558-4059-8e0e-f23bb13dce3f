# Table of contents

* [README](README.md)
* [InferData](inferdata/README.md)
  * [Test-Boolean](inferdata/test-boolean.md)
  * [Invoke-AllTests](inferdata/invoke-alltests.md)
  * [Test-Integer](inferdata/test-integer.md)
  * [Test-String](inferdata/test-string.md)
  * [Test-Date](inferdata/test-date.md)
  * [Test-Number](inferdata/test-number.md)
* [mdHelp](mdhelp/README.md)
  * [en](mdhelp/en/README.md)
    * [Expand-NumberFormat](mdhelp/en/expand-numberformat.md)
    * [Open-ExcelPackage](mdhelp/en/open-excelpackage.md)
    * [Add-ExcelChart](mdhelp/en/add-excelchart.md)
    * [Copy-ExcelWorkSheet](mdhelp/en/copy-excelworksheet.md)
    * [Set-ExcelRange](mdhelp/en/set-excelrange.md)
    * [Import-Excel](mdhelp/en/import-excel.md)
    * [Set-ExcelRow](mdhelp/en/set-excelrow.md)
    * [Get-ExcelSheetInfo](mdhelp/en/get-excelsheetinfo.md)
    * [New-PivotTableDefinition](mdhelp/en/new-pivottabledefinition.md)
    * [Add-ExcelDataValidationRule](mdhelp/en/add-exceldatavalidationrule.md)
    * [Join-Worksheet](mdhelp/en/join-worksheet.md)
    * [New-ConditionalFormattingIconSet](mdhelp/en/new-conditionalformattingiconset.md)
    * [Send-SQLDataToExcel](mdhelp/en/send-sqldatatoexcel.md)
    * [Get-ExcelWorkbookInfo](mdhelp/en/get-excelworkbookinfo.md)
    * [New-ConditionalText](mdhelp/en/new-conditionaltext.md)
    * [Compare-WorkSheet](mdhelp/en/compare-worksheet.md)
    * [New-ExcelChartDefinition](mdhelp/en/new-excelchartdefinition.md)
    * [Remove-WorkSheet](mdhelp/en/remove-worksheet.md)
    * [ConvertFrom-ExcelToSQLInsert](mdhelp/en/convertfrom-exceltosqlinsert.md)
    * [Close-ExcelPackage](mdhelp/en/close-excelpackage.md)
    * [Set-ExcelColumn](mdhelp/en/set-excelcolumn.md)
    * [Add-WorkSheet](mdhelp/en/add-worksheet.md)
    * [Add-ExcelTable](mdhelp/en/add-exceltable.md)
    * [Convert-ExcelRangeToImage](mdhelp/en/convert-excelrangetoimage.md)
    * [Add-ConditionalFormatting](mdhelp/en/add-conditionalformatting.md)
    * [Update-FirstObjectProperties](mdhelp/en/update-firstobjectproperties.md)
    * [Export-Excel](mdhelp/en/export-excel.md)
    * [Select-Worksheet](mdhelp/en/select-worksheet.md)
    * [Merge-Worksheet](mdhelp/en/merge-worksheet.md)
    * [Merge-MultipleSheets](mdhelp/en/merge-multiplesheets.md)
    * [Add-ExcelName](mdhelp/en/add-excelname.md)
    * [ConvertFrom-ExcelSheet](mdhelp/en/convertfrom-excelsheet.md)
    * [Add-PivotTable](mdhelp/en/add-pivottable.md)
* [Public](public/README.md)
  * [Import-USPS](public/import-usps.md)
  * [Get-Range](public/get-range.md)
  * [Get-XYRange](public/get-xyrange.md)
  * [Set-CellStyle](public/set-cellstyle.md)
  * [Invoke-Sum](public/invoke-sum.md)
  * [Get-ExcelColumnName](public/get-excelcolumnname.md)
  * [Import-UPS](public/import-ups.md)
  * [Set-WorksheetProtection](public/set-worksheetprotection.md)
  * [New-Plot](public/new-plot.md)
  * [Import-Html](public/import-html.md)
  * [New-ExcelStyle](public/new-excelstyle.md)
  * [ConvertFrom-ExcelData](public/convertfrom-exceldata.md)
  * [ConvertTo-ExcelXlsx](public/convertto-excelxlsx.md)
  * [New-PSItem](public/new-psitem.md)
  * [Get-HtmlTable](public/get-htmltable.md)
* [Charting](charting/README.md)
  * [LineChart](charting/linechart.md)
  * [PieChart](charting/piechart.md)
  * [ColumnChart](charting/columnchart.md)
  * [BarChart](charting/barchart.md)
  * [DoChart](charting/dochart.md)
* [Examples](examples/README.md)
  * [Untitled](examples/untitled.md)
  * [Charts](examples/charts/README.md)
    * [Multiplecharts](examples/charts/multiplecharts.md)
* [Pivot](pivot/README.md)
  * [Pivot](pivot/pivot.md)

